import React from "react";
import { Layout } from "@douyinfe/semi-ui";
import { Outlet } from "react-router-dom";

const SettingIndex = () => {
  const { <PERSON>er, Footer, Sider, Content } = Layout;
  return (
    <Layout className="full-height setting-index">
      <Header className="setting-index-header h-[56px] ">Header</Header>
      <Layout className="h-full setting-container">
        <Sider className="w-[240px] p-[10px]">
          <div className="setting-index-sider h-full w-full rounded-lg">
            123123
          </div>
        </Sider>
        <Content className="h-full m-2">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};
export default SettingIndex;
