import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const ICIOFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.instruction"
          label="指令"
          rows={6}
          placeholder="AI执行的具体任务"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.context"
          label="背景"
          rows={6}
          placeholder="提供大量背景信息"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.inputData"
          label="输入数据"
          rows={6}
          placeholder="告知模型需要处理的数据"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.outputIndicator"
          label="输出引导"
          rows={6}
          placeholder="告知我们输出的类型或风格"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default ICIOFields;