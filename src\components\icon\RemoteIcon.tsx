import React from "react";

export default function RemoteIcon() {
  return (
    <svg
      t="1739866587335"
      className="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="29747"
      width="2em"
      height="2em"
    >
      <path
        d="M938.666667 1002.666667H85.333333a64 64 0 0 1-64-64V85.333333a64 64 0 0 1 64-64h853.333334a64 64 0 0 1 64 64v853.333334a64 64 0 0 1-64 64zM85.333333 64a21.333333 21.333333 0 0 0-21.333333 21.333333v853.333334a21.333333 21.333333 0 0 0 21.333333 21.333333h853.333334a21.333333 21.333333 0 0 0 21.333333-21.333333V85.333333a21.333333 21.333333 0 0 0-21.333333-21.333333z"
        p-id="29748"
      ></path>
      <path
        d="M128 160A32 32 0 1 1 160 128 32 32 0 0 1 128 160z m0-42.666667a10.666667 10.666667 0 1 0 10.666667 10.666667 10.666667 10.666667 0 0 0-10.666667-10.666667zM213.333333 160A32 32 0 1 1 245.333333 128 32 32 0 0 1 213.333333 160z m0-42.666667a10.666667 10.666667 0 1 0 10.666667 10.666667 10.666667 10.666667 0 0 0-10.666667-10.666667zM298.666667 160A32 32 0 1 1 330.666667 128 32 32 0 0 1 298.666667 160z m0-42.666667a10.666667 10.666667 0 1 0 10.666666 10.666667 10.666667 10.666667 0 0 0-10.666666-10.666667zM42.666667 192h938.666666v42.666667H42.666667zM725.333333 576H298.666667a42.666667 42.666667 0 0 1-42.666667-42.666667v-42.666666a42.666667 42.666667 0 0 1 42.666667-42.666667h426.666666a42.666667 42.666667 0 0 1 42.666667 42.666667v42.666666a42.666667 42.666667 0 0 1-42.666667 42.666667z m-426.666666-85.333333v42.666666h426.666666v-42.666666zM469.333333 661.333333h-128a21.333333 21.333333 0 0 1 0-42.666666h128a21.333333 21.333333 0 0 1 0 42.666666zM682.666667 661.333333h-128a21.333333 21.333333 0 0 1 0-42.666666h128a21.333333 21.333333 0 0 1 0 42.666666z"
        p-id="29749"
      ></path>
      <path
        d="M640 576a21.333333 21.333333 0 0 1-21.333333-21.333333v-85.333334a21.333333 21.333333 0 0 1 42.666666 0v85.333334a21.333333 21.333333 0 0 1-21.333333 21.333333z"
        p-id="29750"
      ></path>
    </svg>
  );
}
