// import { Layout } from "@douyinfe/semi-ui";
// import { Navigate, Route, Routes } from "react-router-dom";
// import CommentIndex from "./pages/comment/Index";
// import SystemLayout from "@/layout/system/index";
// import RoleIndex from "./pages/platform/role";
// import ModelIndex from "./pages/platform/model";
// import PluginIndex from "./pages/setting/PluginIndex";
// import User from '@/pages/system/user/index'
// import NormalIndex from "./pages/setting/NormalIndex";
// import NotFound from "./pages/404";
// import { invoke } from "@tauri-apps/api/tauri";
// import { useEffect, useState } from "react";
// import { LocalForageService as storage } from "./utils/storage";
// import Workbench from "./pages/workbench";

// function Index() {
//   const [runEnvType, setRunEnvType] = useState<string>("web");
//   const { Content } = Layout;

//   useEffect(() => {
//     invoke("get_os_name")
//       .then((osName) => {
//         setRunEnvType(osName as string);
//       })
//       .catch(() => console.log("Web浏览器"));

//     storage.getItem<"light" | "dark">("theme_mode").then((mode) => {
//       if (mode) {
//         document.body.setAttribute("theme-mode", mode);
//       } else {
//         document.body.setAttribute("theme-mode", "dark");
//       }
//     });
//   }, []);

//   return (
//     <Content
//       className={`env-${runEnvType} main-box full-height semi-light-scrollbar`}
//     >
//       <Routes>
//         <Route path="/" element={<Navigate to="/comment" />} />
//         <Route
//           path="/system"
//           element={<SystemLayout />}
//           children={[
//             <Route key="workbench" path="workbench" element={<Workbench />} />,
//           ]}
//         />
//         <Route
//           path="/platform"
//           element={<SystemLayout />}
//           children={[
//             <Route key="setting-role" path="role" element={<RoleIndex />} />,
//             <Route key="setting-model" path="model" element={<ModelIndex />} />,
//             <Route
//               key="setting-plugin"
//               path="plugin"
//               element={<PluginIndex />}
//             />,
//             <Route
//               key="setting-normal"
//               path="normal"
//               element={<NormalIndex />}
//             />,
//           ]}
//         />
//         <Route
//           path="/system"
//           element={<SystemLayout />}
//           children={[
//             <Route key="user" path="user" element={<User />} />,
//           ]}
//         ></Route>
//         <Route path="/comment" element={<CommentIndex />} />
//         <Route path="*" element={<NotFound />} />
//       </Routes>
//     </Content>
//   );
// }

// export default Index;
