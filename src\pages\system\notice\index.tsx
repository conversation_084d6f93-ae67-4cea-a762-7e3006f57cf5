import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import {
  IconDelete,
  IconEdit,
  IconMore,
  IconPlusStroked,
  IconSync,
} from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Pagination,
  Radio,
  RadioGroup,
  Select,
  Table,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useBoolean } from "@/hooks";
import { debounce } from "lodash-es";

import classNames from "classnames";
import { useTable } from "@/hooks/useTables";
import { getOssConfigList } from "@/api/system/oss-config";
import { closeNotice, getNoticeList, sendNotice } from "@/api/system/notice";
import { useNavigate } from "@tanstack/react-router";
import NoticeForm from "./notice-form";
import TableColumnFilter from "@/components/TableColumnFilter";
export default function Notice() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_notice_type",
    "sys_notice_status",
  ]);
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    loadDictionary();
  }, []);

  const noticeTypes = useMemo(() => {
    return (
      dictionaryData?.sys_notice_type?.map((item: any) => ({
        label: item.dictLabel,
        value: item.dictValue,
      })) || []
    );
  }, [dictionaryData]);

  const {
    dataSource,
    loading,
    columns,
    visibleColumns,
    pagination,
    updateVisibleColumns,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getNoticeList,
    params: {},
    columns: [
      {
        title: "公告标题",
        dataIndex: "noticeTitle",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "公告类型",
        dataIndex: "noticeType",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_notice_type"
              dictValue={text}
              dictionaryData={dictionaryData.sys_notice_type || []}
            />
          );
        },
      },
      {
        title: "发布人",
        dataIndex: "createdByName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_notice_status"
              dictValue={text}
              dictionaryData={dictionaryData.sys_notice_status || []}
            />
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button theme="borderless" onClick={() => handlePreview(row)}>
                预览
              </Button>
              <Dropdown
                className="min-w-[100px]"
                zIndex={1000}
                position="bottom"
                trigger={"click"}
                render={
                  <Dropdown.Menu>
                    <Dropdown.Item
                      disabled={row.status !== "2"}
                      onClick={() => handlePublish(row.noticeId)}
                    >
                      发布
                    </Dropdown.Item>
                    <Dropdown.Item
                      disabled={row.status === "0" || row.status === "1"}
                      onClick={() => handleEditForm(row.noticeId)}
                    >
                      编辑
                    </Dropdown.Item>
                    <Dropdown.Item
                      disabled={row?.status === "0"}
                      onClick={() => handleClose(row.noticeId)}
                    >
                      关闭
                    </Dropdown.Item>
                    <Dropdown.Item
                      type="danger"
                    // onClick={() => handleRemoveDictData(row.dictCode)}
                    >
                      删除
                    </Dropdown.Item>
                  </Dropdown.Menu>
                }
              >
                <Button
                  type="tertiary"
                  theme="borderless"
                  icon={<IconMore className="cursor-pointer" />}
                ></Button>
              </Dropdown>
            </div>
          );
        },
      },
    ],
  });
  const handlePublish = (id: number) => {
    sendNotice(id).then(({ msg }) => {
      Toast.success(msg);
      refresh();
    });
  };
  const handleClose = (id: number) => {
    closeNotice(id).then(({ msg }) => {
      Toast.success(msg);
      refresh();
    });
  };
  // 防抖提交（300ms）
  const debouncedSubmit = useCallback(
    debounce((values: any) => {
      setSearchParams(values);
      refresh();
    }, 300),
    []
  );
  const handlePreview = (row: any) => {
    navigate({
      to: `/system/notice/preview/${row.noticeId}`,
    });
  };

  const handleAddForm = () => {
    // setVisible(true);
    navigate({
      to: `/system/notice/form`,
    });
  };
  const handleEditForm = (id: number) => {
    // setVisible(true);
    navigate({
      to: `/system/notice/form`,
      search: {
        id,
      },
    });
  };

  return (
    <div className=" w-full h-full p-2">
      <div className=" w-full h-full p-4 bg-semi-color-white rounded-lg  shadow-md flex items-stretch  sm:overflow-auto flex-col  ">
        <div className="flex flex-col  justify-between">
          <div className="font-semibold text-xl px-2 my-2">通知公告</div>
          <div className="flex flex-row items-center gap-2 w-full justify-between mb-1">
            <Form<typeof searchParams>
              initValues={searchParams}
              autoScrollToError={{ behavior: "smooth", block: "center" }}
              showValidateIcon
              onValueChange={(values) => {
                debouncedSubmit(values);
              }}
              onReset={() => {
                resetSearchParams();
                refresh();
              }}
              onSubmit={(values) => {
                setSearchParams(values);
                refresh();
              }}
            >
              <div className="flex gap-2 ">
                <Form.Input
                  className="min-w-[250px]"
                  showClear
                  trigger={["change", "blur"]}
                  noLabel
                  fieldStyle={{ paddingBottom: 0 }}
                  field="noticeTitle"
                  placeholder={"请输入公告标题"}
                />
              </div>
            </Form>
            <div className="flex gap-2 items-center">
              <Button
                type="primary"
                icon={<IconPlusStroked />}
                theme="solid"
                onClick={() => {
                  // handleAddForm();
                  handleAddForm();
                }}
              >
                新增
              </Button>
              <Button theme="solid" type="danger">
                批量删除
              </Button>
              <TableColumnFilter
                columns={columns}
                onColumnsChange={(selectedColumns) => {
                  updateVisibleColumns(selectedColumns);
                }}
              />
              <Tooltip content="刷新列表">
                <Button
                  type="tertiary"
                  icon={<IconSync />}
                  onClick={() => refresh()}
                />
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="flex-1 sm:overflow-hidden mt-1 ">
          <Table
            rowSelection={rowSelection as any}
            columns={visibleColumns}
            className="h-full"
            dataSource={dataSource}
            loading={loading}
            size="small"
            scroll={{ x: 962, y: "calc(100vh - 210px)" }}
            pagination={false}
            rowKey="noticeId"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
          />
        </div>
        {pagination.total > 0 && (
          <div className="flex justify-end">
            <Pagination {...pagination}></Pagination>
          </div>
        )}
      </div>
      {/* <NoticeForm visible={visible} onCancel={() => setVisible(false)} /> */}
    </div>
  );
}
