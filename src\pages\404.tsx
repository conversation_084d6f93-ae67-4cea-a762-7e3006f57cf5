import { useGlobalStore } from "@/store";
import { Button } from "@douyinfe/semi-ui";
import { useRouter } from "@tanstack/react-router";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

function NotFound() {
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const { themeConfig } = useGlobalStore.getState();
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  const backToHome = () => {
    router.navigate({ to: "/" });
  };

  const goBack = () => {
    router.history.back();
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 100, damping: 12 }
    }
  };

  const isDark = themeConfig.isDark;
  const textColor = isDark ? "text-white" : "text-gray-900";
  const subtextColor = isDark ? "text-gray-400" : "text-gray-500";
  const bgColor = isDark ? "bg-gray-900" : "bg-white";
  const accentColor = "#3873FA"; // 蓝色
  const strokeColor = isDark ? "#ffffff" : "#000000";

  if (!mounted) return null;

  return (
    <div className={`w-full h-screen flex items-center justify-center ${bgColor} overflow-hidden`}>
      <motion.div 
        className="flex flex-col items-center justify-center px-4 max-w-md w-full relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* 404 SVG动画 */}
        <motion.div 
          className="relative mb-8 w-full h-64"
          variants={itemVariants}
        >
          <svg width="100%" height="100%" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
            {/* 背景网格 */}
            <g opacity="0.1">
              {[...Array(10)].map((_, i) => (
                <line 
                  key={`h-${i}`} 
                  x1="0" 
                  y1={i * 20} 
                  x2="400" 
                  y2={i * 20} 
                  stroke={strokeColor} 
                  strokeWidth="0.5"
                />
              ))}
              {[...Array(20)].map((_, i) => (
                <line 
                  key={`v-${i}`} 
                  x1={i * 20} 
                  y1="0" 
                  x2={i * 20} 
                  y2="200" 
                  stroke={strokeColor} 
                  strokeWidth="0.5"
                />
              ))}
            </g>

            {/* 404文字 - 重新调整间距 */}
            <g>
              {/* 数字4 - 左侧 */}
              <motion.path
                d="M80,50 L80,100 L120,100 M120,50 L120,150"
                fill="transparent"
                stroke={accentColor}
                strokeWidth="12"
                strokeLinecap="round"
                strokeLinejoin="round"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ 
                  pathLength: 1, 
                  opacity: 1,
                  transition: { duration: 1.5, ease: "easeInOut", delay: 0.2 } 
                }}
              />
              
              {/* 数字0 - 中间 */}
              <motion.path
                d="M200,50 C180,50 170,70 170,100 C170,130 180,150 200,150 C220,150 230,130 230,100 C230,70 220,50 200,50 Z"
                fill="transparent"
                stroke={accentColor}
                strokeWidth="12"
                strokeLinecap="round"
                strokeLinejoin="round"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ 
                  pathLength: 1, 
                  opacity: 1,
                  transition: { duration: 1.5, ease: "easeInOut", delay: 0.6 } 
                }}
              />
              
              {/* 数字4 - 右侧 */}
              <motion.path
                d="M280,50 L280,100 L320,100 M320,50 L320,150"
                fill="transparent"
                stroke={accentColor}
                strokeWidth="12"
                strokeLinecap="round"
                strokeLinejoin="round"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{ 
                  pathLength: 1, 
                  opacity: 1,
                  transition: { duration: 1.5, ease: "easeInOut", delay: 1 } 
                }}
              />
            </g>

            {/* 错误指示器 - 移到右侧 */}
            <motion.g
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5, duration: 0.5 }}
            >
              <circle cx="370" cy="100" r="20" fill="transparent" stroke={accentColor} strokeWidth="2" strokeDasharray="5,5">
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="0 370 100"
                  to="360 370 100"
                  dur="20s"
                  repeatCount="indefinite"
                />
              </circle>
              <circle cx="370" cy="100" r="12" fill="transparent" stroke={accentColor} strokeWidth="2" strokeDasharray="3,3">
                <animateTransform
                  attributeName="transform"
                  type="rotate"
                  from="360 370 100"
                  to="0 370 100"
                  dur="15s"
                  repeatCount="indefinite"
                />
              </circle>
              
              {/* X标记 */}
              <motion.path
                d="M362,92 L378,108 M378,92 L362,108"
                stroke={accentColor}
                strokeWidth="3"
                strokeLinecap="round"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2, duration: 0.3 }}
              />
            </motion.g>

            {/* 底部线条 */}
            <motion.path
              d="M50,170 L350,170"
              stroke={accentColor}
              strokeWidth="2"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: 1.5 }}
            />
            
            {/* 底部点 */}
            {[...Array(3)].map((_, i) => (
              <motion.circle 
                key={i}
                cx={100 + i * 100} 
                cy="170" 
                r={3}
                fill={accentColor}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2 + i * 0.2, duration: 0.5 }}
              />
            ))}
          </svg>
        </motion.div>

        {/* 标题 */}
        <motion.h1 
          className={`text-3xl font-bold mb-2 ${textColor}`}
          variants={itemVariants}
        >
          页面未找到
        </motion.h1>

        {/* 描述 */}
        <motion.p 
          className={`text-center mb-8 ${subtextColor}`}
          variants={itemVariants}
        >
          很抱歉，您要访问的页面不存在或已被移除。
        </motion.p>

        {/* 按钮组 */}
        <motion.div 
          className="flex flex-col sm:flex-row gap-4 w-full max-w-xs"
          variants={itemVariants}
        >
          <Button
            className="w-full rounded-full transition-all duration-300 ease-in-out hover:shadow-lg"
            onClick={goBack}
            theme='solid' type='primary'
            style={{ height: 44, color: 'white', border: 'none' }}
          >
            返回上一页
          </Button>
          <Button
            className={`w-full backdrop-blur-sm bg-white/10 border border-gray-200 rounded-full transition-all duration-300 ease-in-out hover:bg-white/20 ${isDark ? 'text-white border-gray-700' : 'text-gray-800'}`}
            onClick={backToHome}
            style={{ height: 44 }}
            theme="outline"
            type="tertiary"
          >
            返回首页
          </Button>
        </motion.div>
      </motion.div>
    </div>
  );
}

export default NotFound;
