import Icon, { IconCamera, IconEdit, IconMore } from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Col,
  Form,
  Row,
  TabPane,
  Tabs,
  Toast,
  Upload,
  Typography,
  Divider,
  Space,
  Card,
} from "@douyinfe/semi-ui";
import { ArrowLeft, Edit, MailOpen } from "lucide-react";
import React, { useState } from "react";

export default function Profile() {
  const [activeTab, setActiveTab] = useState("1");

  return (
    <div className="h-full px-2 py-2 bg-[var(--semi-color-bg-0)]   overflow-y-auto">
      <div className="max-w-6xl mx-auto p-6 h-full flex flex-col">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div className="flex flex-col gap-4 w-full">
            <div className="flex items-center gap-1 text-sm cursor-pointer">
              <Icon svg={<ArrowLeft size="1em" />} size="small" />
              <span>返回首页</span>
            </div>
            <div className="text-2xl md:text-2xl font-bold text-semi-color-text-0">
              个人资料
            </div>
            <div className="w-full border-b border-semi-color-border border-solid flex gap-2 items-center">
              <div className="p-1 font-bold border-b-2 border-solid border-semi-color-primary cursor-pointer">
                基本信息
              </div>
              <div className="p-1">日志审计</div>
            </div>
          </div>
        </div>

        <div className="bg-semi-color-white flex-1 rounded-xl shadow-sm  mb-2 ">
          <div className="p-6">
            <div className="flex flex-col md:flex-row gap-2">
              {/* 左侧导航 */}
              <div className="w-full md:w-32 shrink-0 flex flex-col gap-2">
                <div className="py-2 hover:bg-semi-color-fill-0 px-1 text-sm cursor-pointer rounded-md text-center select-none">
                  偏好设置
                </div>
                <div className="py-2 hover:bg-semi-color-fill-0 px-1 text-sm cursor-pointer rounded-md text-center">
                  安全设置
                </div>
              </div>

              {/* 右侧表单内容 */}
              <div className="flex-1 flex flex-col gap-4">
                <Card
                  headerLine={false}
                  headerStyle={{ padding: "0px" }}
                  bodyStyle={{ padding: "0px" }}
                  header={
                    <div className="flex justify-between items-center bg-semi-color-fill-0 p-2  py-2 border-b border-solid border-semi-color-border">
                      <Typography.Title heading={6} className="m-0">
                        个人资料
                      </Typography.Title>
                      <Button theme="solid" icon={<IconEdit />}>
                        保存
                      </Button>
                    </div>
                  }
                >
                  <div className="p-6">
                    <Form>
                      <Row gutter={[24, 16]}>
                        <Col span={12}>
                          <Form.Input
                            field="username"
                            label="用户名"
                            placeholder="请输入用户名"
                            initValue="xgh0407"
                          />
                        </Col>
                        <Col span={12}>
                          <Form.Input
                            field="nickName"
                            label="昵称"
                            placeholder="请输入昵称"
                            initValue="herther"
                          />
                        </Col>
                        <Col span={12}>
                          <Form.Input
                            field="phone"
                            label="手机号码"
                            placeholder="请输入手机号码"
                          />
                        </Col>
                        <Col span={12}>
                          <Form.RadioGroup
                            field="gender"
                            label="用户性别"
                            direction="horizontal"
                          >
                            <Form.Radio value="male">男</Form.Radio>
                            <Form.Radio value="female">女</Form.Radio>
                          </Form.RadioGroup>
                        </Col>
                        <Col span={24}>
                          <Form.TextArea
                            field="bio"
                            label="个人简介"
                            placeholder="介绍一下自己吧"
                            rows={4}
                          />
                        </Col>
                      </Row>
                    </Form>
                  </div>
                </Card>
                <Card
                  headerLine={false}
                  headerStyle={{ padding: "0px" }}
                  bodyStyle={{ padding: "0px" }}
                  header={
                    <div className="flex justify-between items-center bg-semi-color-fill-0 p-2 py-4 border-b border-solid border-semi-color-border">
                      <Typography.Title heading={6} className="m-0">
                        账号设置
                      </Typography.Title>
                    </div>
                  }
                >
                  <div>
                    <div className="flex flex-col">
                      <div className="flex items-center border-b border-solid border-semi-color-border justify-between p-4 ">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-lg bg-[#f5f5f5] flex items-center justify-center ">
                           <Icon svg={<MailOpen />}/>
                          </div>
                          <div className="ml-3">
                            <div className="font-medium">Email</div>
                            <div className="text-xs text-[var(--semi-color-text-2)]">
                              <EMAIL>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2 items-center">
                          <Button type="tertiary" theme="outline" size="small">
                            修改密码
                          </Button>
                          <Button
                            type="tertiary"
                            theme="borderless"
                            size="small"
                            icon={<Icon svg={<Edit size="1em" />} />}
                          ></Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
                <Card
                  headerLine={false}
                  headerStyle={{ padding: "0px" }}
                  bodyStyle={{ padding: "0px" }}
                  header={
                    <div className="flex justify-between items-center bg-semi-color-fill-0 p-2 py-4 border-b border-solid border-semi-color-border">
                      <Typography.Title heading={6} className="m-0">
                        第三方账号
                      </Typography.Title>
                    </div>
                  }
                >
                  <div>
                    <div className="flex flex-col">
                      <div className="flex items-center border-b border-solid border-semi-color-border justify-between p-4 ">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-lg bg-[var(--semi-color-primary-light)] flex items-center justify-center text-[var(--semi-color-primary)]">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                            </svg>
                          </div>
                          <div className="ml-3">
                            <div className="font-medium">GitHub</div>
                            <div className="text-xs text-[var(--semi-color-text-2)]">
                              已绑定: xgh0407
                            </div>
                          </div>
                        </div>
                        <Space>
                          <Button type="tertiary">解绑</Button>
                          <Button type="primary">重新绑定</Button>
                        </Space>
                      </div>

                      <div className="flex items-center border-b border-solid border-semi-color-border justify-between p-4 ">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-lg bg-[#f5f5f5] flex items-center justify-center text-[#07c160]">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="currentColor"
                            >
                              <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .276-.08c.096 0 .191.016.283.048a10.134 10.134 0 0 0 2.994.453c.261 0 .52-.012.78-.034.29.602.725 1.167 1.282 1.68.915.832 2.132 1.342 3.428 1.469.226.022.456.034.685.034 1.794 0 3.388-.675 4.529-1.733 1.14-1.059 1.85-2.53 1.85-4.155 0-1.57-.664-3.015-1.741-4.066C18.37 7.126 16.687 6.35 14.788 6.21c-.249-.019-.5-.028-.753-.028-1.099 0-2.145.214-3.085.603V6.78c0-2.514-2.455-4.592-5.469-4.592zm-1.22 4.318a.784.784 0 0 1 .783.784.784.784 0 0 1-.783.784.784.784 0 0 1-.784-.784c0-.433.35-.784.784-.784zm6.453 0a.784.784 0 0 1 .784.784.784.784 0 0 1-.784.784.784.784 0 0 1-.783-.784c0-.433.35-.784.783-.784zM14.788 7.438c.229 0 .454.008.678.025 1.745.13 3.313.835 4.402 1.848.932.904 1.517 2.169 1.517 3.567 0 1.45-.631 2.782-1.665 3.754-1.035.971-2.472 1.594-4.067 1.594-.213 0-.425-.01-.635-.032-1.19-.117-2.28-.578-3.116-1.327-.835-.75-1.353-1.748-1.353-2.83v-3.254c0-.202.126-.384.317-.459 1.13-.448 2.114-.644 3.138-.644zM9.602 9.53c0-.434.35-.784.784-.784a.784.784 0 0 1 .783.784.784.784 0 0 1-.783.784.784.784 0 0 1-.784-.784zm4.318 0c0-.434.35-.784.784-.784a.784.784 0 0 1 .783.784.784.784 0 0 1-.783.784.784.784 0 0 1-.784-.784z" />
                            </svg>
                          </div>
                          <div className="ml-3">
                            <div className="font-medium">微信</div>
                            <div className="text-xs text-[var(--semi-color-text-2)]">
                              未绑定
                            </div>
                          </div>
                        </div>
                        <Button type="primary">绑定</Button>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
