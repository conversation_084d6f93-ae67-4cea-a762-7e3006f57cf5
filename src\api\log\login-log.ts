import { http } from "@/utils/axios";

const base_url = "/monitor/logininfor";

export function getLoginLogList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}
export function countLoginStatus() {
  return http.request<API.Result<API.Stats>>({
    url:`${base_url}/logs/count`,
    method: "GET",
  });
}

export function statsLoginLogs(params?: any) {
  return http.request<API.Result<Log.LogingLogstatsVO>>(
    {
      url:`${base_url}/logs/stats`,
      method: "GET",
      params,
    },
  );
}


/** 清空登录日志 */
export function clean(type: number | string) {
  return http.request<API.Result<any>>(
    {
      url:`${base_url}/clean/${type}`,
      method: "DELETE",
    },
  );
}
