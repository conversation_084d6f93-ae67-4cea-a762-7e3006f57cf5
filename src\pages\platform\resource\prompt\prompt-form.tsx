import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  Form,
  Modal,
  Row,
  Spin,
  Tag,
  Toast,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import React, { useEffect, useState } from "react";
import "./styles.scss";
import BROKEFields from "./components/BROKEFields";
import { APEFields } from "./components/APEFields";
import COASTFields from "./components/COASTFields";
import TAGFields from "./components/TAGFields";
import RISEFields from "./components/RISEFields";
import TRACEFields from "./components/TRACEFields";
import ERAFields from "./components/ERAFields";
import CAREFields from "./components/CAREFields";
import ROSESFields from "./components/ROSESFields";
import ICIOFields from "./components/ICIOFields";
import CRISPEFields from "./components/CRISPEFields";
import RACEFields from "./components/RACEFields";
import { debounce } from 'lodash-es';
import { addPromptTemplate, getPromptTemplateById, updatePromptTemplate } from "@/api/platform/promptTemplate";
import { getCategorySelected } from "@/api/platform/category";
import { EmojiSelectField } from "@/components/EmojiSelect";
import { useNavigate, useParams, useRouter } from "@tanstack/react-router";
const templateDescriptions = new Map<string, string>([
  ["APE", "行动，目的，期望"],
  ["BROKE", "背景，角色，目标，关键结果，改进"],
  ["COAST", "背景，客观，行动，场景，任务"],
  ["TAG", "任务，行动，目标"],
  ["RISE", "角色，输入，步骤，期望"],
  ["TRACE", "任务，请求，操作，上下文，示例"],
  ["ERA", "期望，角色，行动"],
  ["CARE", "上下文，行动，结果，示例"],
  ["ROSES", "角色，客观，场景，解决方案，步骤"],
  ["ICIO", "指令，背景，输入数据，输出引导"],
  ["CRISPE", "角色，见解，声明，个性，实验"],
  ["RACE", "角色，行动，背景，期望"],
]);
export default function PromptForm() {
  const searchParams = useRouter();

  const navigate = useNavigate();
  const [mode, setModel] = React.useState("custom");
  const [templateType, setTemplateType] = useState("APE");
  const formApiRef = React.useRef(null);
  const [variables, setVariables] = useState<any>([])
  const [loading, setLoading] = useState(false);
  const [initLoading, setInitLoading] = useState(false);
  const [extraText, setExtraText] = useState<string>(templateDescriptions.get("APE") || ""); // 添加extraText状态
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const [categorys, setCategorys] = useState([]);
  // 创建Map对象存储每种优化方法的描述
  const getCategorys = () => {
    getCategorySelected().then((res: any) => {
      // console.log(res)
      setCategorys(res.data)
    })
  }
  useEffect(() => {
    getCategorys();
  }, []);


  const extractVariables = (v: any) => {
    let extractedVariables: string[] = [];

    if (mode === "project") {
      Object.values(v.rowContent || {}).forEach((value: any) => {
        if (typeof value === "string") {
          const variableMatches = value.match(/\$\{([^}]*)\}/g);
          if (variableMatches) {
            extractedVariables.push(
              ...variableMatches.map((match: string) => match.slice(2, -1).trim())
            );
          }
        }
      });
      setVariables([...new Set(extractedVariables)]);
    }
  };

  // 创建防抖后的函数
  const debouncedExtractVariables = debounce((v) => {
    extractVariables(v);
  }, 300); // 延迟 300ms

  // 更新onValueChange处理函数
  const handleValueChange = (v: any) => {

    if (v.mode) {
      setModel(v.mode);
    }
    if (v.templateType) {
      setTemplateType(v.templateType);
      // 根据templateType从Map中获取对应的描述并设置到extraText
      setExtraText(templateDescriptions.get(v.templateType) || "");
    }
    if (v.content) {
      // setVariables([]);
      // 使用正则表达式提取 ${XXX} 中的变量名
      const variableMatches = v.content.match(/\$\{([^}]*)\}/g);

      // 如果匹配到变量，则提取它们，否则清空变量数组
      if (variableMatches) {
        const extractedVariables = variableMatches.map((match: string) =>
          match.slice(2, -1).trim()
        );
        setVariables(extractedVariables);
      } else {
        setVariables([]);
      }
    } else if (v.content === undefined) {
      setVariables([]);
    }

    if (mode === "project") {
      let extractedVariables: string[] = [];
      Object.values(v.rowContent).forEach((value: any) => {
        if (typeof value === "string") {
          const variableMatches = value.match(/\$\{([^}]*)\}/g);
          if (variableMatches) {
            extractedVariables.push(
              ...variableMatches.map((match: string) =>
                match.slice(2, -1).trim()
              )
            );

          }
        }
      });
      setVariables([...new Set(extractedVariables)]);
    }

    //如果为mode 为project 还要监听这里面所有表单的变量放到variables中
    debouncedExtractVariables(v)

  };

  const renderTemplateFields = () => {
    switch (templateType) {
      case 'APE':
        return <APEFields />;
      case 'BROKE':
        return <BROKEFields />;
      case 'COAST':
        return <COASTFields />;
      case 'TAG':
        return <TAGFields />;
      case 'RISE':
        return <RISEFields />;
      case 'TRACE':
        return <TRACEFields />;
      case 'ERA':
        return <ERAFields />;
      case 'CARE':
        return <CAREFields />;
      case 'ROSES':
        return <ROSESFields />;
      case 'ICIO':
        return <ICIOFields />;
      case 'CRISPE':
        return <CRISPEFields />;
      case 'RACE':
        return <RACEFields />;
      default:
        return null;
    }
  };
  const handleSubmit = async () => {
    const values = await formApiRef?.current?.validate();
    searchParams.state.location.search?.id ? await handleEdit(values, searchParams.state.location.search?.id) : await handleSave(values);
  };

  const handleSave = async (values: any) => {
    setLoading(true)
    addPromptTemplate({
      ...values,
      //去除name 的空格
      name: values.name.trim(),
      variables: variables.join(","),
    }).then((res) => {
      Toast.success(res.msg);
      navigate({
        to: "/platform/resource/prompt",
      })

    }).finally(() => {
      setLoading(false)
    });
  }
  const handleEdit = async (values: any, id: number) => {
    setLoading(true)
    updatePromptTemplate({
      ...values,
      id,
      name: values.name.trim(),
      variables: variables.join(","),
    }).then((res) => {
      Toast.success(res.msg);
      navigate({
        to: "/platform/resource/prompt",
      })

    }).finally(() => {
      setLoading(false)
    });
  }
  const getDetails = (id: number) => {
    setInitLoading(true)
    getPromptTemplateById(id).then(({ data }) => {
      formApiRef.current.setValues({
        ...data,
        rowContent: JSON.parse(data?.rowContent)
      });
      // console.log(JSON.parse(data?.rowContent));

      if (data?.variables) {
        setVariables(data?.variables.split(","))
      }
      setTimeout(() => {
        formApiRef.current.setValue("rowContent", JSON.parse(data?.rowContent))
      }, 0)
    }).finally(() => {
      setInitLoading(false)
    });
  }

  useEffect(() => {
    if (searchParams.state.location.search?.id) {
      getDetails(searchParams.state.location.search?.id)
    }
  }, [searchParams.state.location.search?.id])

  const renderTemplateContent = () => {
    return <div className="flex items-center">
      <span>模板所包含变量：</span>
      <div className="flex items-center gap-1">
        {
          variables.map((variable: string) => (
            <Tag key={variable}>{variable}</Tag>
          ))
        }
      </div>
    </div>
  }

  const handleRest = () => {
    Modal.warning({
      title: '是否确认退出', content: '退出后无法保存当前prompt模板', onOk: () => {
        navigate(
          {
            to: '/platform/resource/prompt',
          },
        )
      }
    });

    // formApiRef.current.reset()
  }

  return (
    <div className="h-full relative flex flex-col px-2 py-2">
      <Card
        className="h-full w-full"
        bordered={false}
        bodyStyle={{
          height: "calc(100vh - 150px)",
          width: "100%",
          overflowY: "auto",
        }}
        title={
          <Breadcrumb compact={false}>
            <Breadcrumb.Item onClick={() => navigate({ to: "/platform/resource/prompt" })}>提示词</Breadcrumb.Item>
            <Breadcrumb.Item>{
              searchParams.state.location.search?.id ? "修改提示词模板" : "新增提示词模板"
            }</Breadcrumb.Item>
          </Breadcrumb>
        }
        footer={
          <div className="flex items-center gap-2 w-full">
            <Button key="back" onClick={handleRest}>
              取消
            </Button>
            <Button loading={loading} type="primary" theme="solid" key="submit" onClick={handleSubmit}>
              提交
            </Button>
          </div>
        }
      >

        {
          initLoading && <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
            <Spin size="large" />
          </div>
        }
        <div className="h-full overflow-y-auto overflow-x-hidden">
          <Form
            getFormApi={getFormApi}
            className="w-full prompt-form"
            onValueChange={handleValueChange} // 使用新的处理函数
          >
            <Section text={"基本信息"}>
              <Row gutter={[10, 0]}>
                <Col span={8}>
                  <Form.Input
                    field="name"
                    label="模板名称"
                    rules={[{ required: true, message: "请填写模板名称" }, {
                      max: 20, message: '长度不能超过20个字符'
                    }]}
                    placeholder="请输入模板名称"
                    trigger="blur"
                  />
                </Col>
                <Col span={24} />
                <Col span={5}>
                  <Form.Select
                    field="category"
                    className="w-full"
                    filter
                    optionList={categorys}
                    label="模板分类"
                    showClear
                    rules={[
                      {
                        required: true,
                        message: "请选择模板分类",
                      },
                    ]}
                    placeholder="请选择模板分类"
                    trigger="blur"
                  />
                </Col>
                <Col span={5}>
                  <EmojiSelectField
                    rules={[{ required: true, message: '请选择模板图标' }]}
                    initValue={"😃"} label="模板图标" field="icon" />
                </Col>
                <Col span={24} />
                <Col span={12}>
                  <Form.TextArea
                    field="description"
                    label="模板描述"
                    maxCount={100}
                    placeholder="请输入模板描述"
                    trigger="blur"
                  />
                </Col>
              </Row>
            </Section>
            <Section text={"模版内容"}>
              <Row>
                <Col span={24}>
                  <Form.RadioGroup
                    rules={[{ required: true, message: "请选择模板创建模式" }]}
                    field="mode"
                    initValue={"custom"}
                    label="模板创建模式"
                  >
                    <Form.Radio value="custom">自定义创建</Form.Radio>
                    <Form.Radio value="project">基于模板工程</Form.Radio>
                  </Form.RadioGroup>
                </Col>
                {mode === "custom" && (
                  <Col span={18}>
                    <Form.TextArea
                      field="content"
                      rules={[
                        { required: true, message: "请输入提示词" }
                      ]}
                      label="提示词"
                      rows={12}
                      placeholder="请输入提示词"
                      trigger="blur"
                      extraText={
                        variables.length > 0 ? renderTemplateContent() : null
                      }
                    />
                  </Col>
                )}
                {mode === "project" && (
                  <>
                    <Col span={24}>
                      <Form.RadioGroup
                        rules={[
                          { required: true, message: "请选择框架创建模式" },
                        ]}
                        field="templateType"
                        initValue={"APE"}
                        label="Prompt优化方法"
                        extraText={extraText} // 使用extraText状态
                      >
                        <Form.Radio value="APE">APE</Form.Radio>
                        <Form.Radio value="BROKE">BROKE</Form.Radio>
                        <Form.Radio value="COAST">COAST</Form.Radio>
                        <Form.Radio value="TAG">TAG</Form.Radio>
                        <Form.Radio value="RISE">RISE</Form.Radio>
                        <Form.Radio value="TRACE">TRACE</Form.Radio>
                        <Form.Radio value="ERA">ERA</Form.Radio>
                        <Form.Radio value="CARE">CARE</Form.Radio>
                        <Form.Radio value="ROSES">ROSES</Form.Radio>
                        <Form.Radio value="ICIO">ICIO</Form.Radio>
                        <Form.Radio value="CRISPE">CRISPE</Form.Radio>
                        <Form.Radio value="RACE">RACE</Form.Radio>
                      </Form.RadioGroup>
                    </Col>
                    <Col span={24}>
                      {renderTemplateFields()}
                      {
                        variables.length > 0 ? renderTemplateContent() : null}
                    </Col>
                  </>
                )}
              </Row>
            </Section>
          </Form>
        </div>
      </Card>
    </div>
  );
}


