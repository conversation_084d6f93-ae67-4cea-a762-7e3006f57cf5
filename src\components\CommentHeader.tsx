import React, { useState } from "react";
import {
  Ava<PERSON>,
  Button,
  Dropdown,
  Input,
  Layout,
  Modal,
  Tag,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import {
  IconChevronDown,
  IconChevronRight,
  IconDeleteStroked,
  IconEdit,
  IconEditStroked,
  IconGlobe,
  IconMoon,
  IconPlus,
  IconShareStroked,
  IconSidebar,
  IconSun,
  IconTick,
} from "@douyinfe/semi-icons";
import { SessionSetting } from "../interface/setting";
import Sider from "@douyinfe/semi-ui/lib/es/layout/Sider";
import { IconColorPlatte, IconConfig } from "@douyinfe/semi-icons-lab";
import { LocalForageService as storage } from "../utils/storage";

interface CommentHeaderProps {
  sessionSidebar: boolean;
  themeMode: string;
  showTitleSpin: boolean;
  sessionTitle: string;
  sessionSetting: SessionSetting;
  createSession: (roleId: string) => void;
  clearCurrentChatList: () => void;
  renameSessionTitle: (content: string) => void;
  triggerSessionSidebar: () => void;
  openMessageShare: () => void;
  switchThemeMode: (theme: "dark" | "light") => void;
  jumpPage: (path: string) => void;
}

const headerEqual = (
  prevProps: CommentHeaderProps,
  currentProps: CommentHeaderProps
) => {
  return (
    prevProps.sessionSidebar === currentProps.sessionSidebar &&
    prevProps.showTitleSpin === currentProps.showTitleSpin &&
    prevProps.sessionTitle === currentProps.sessionTitle &&
    prevProps.sessionSetting.isShowHeaderTitle ==
      currentProps.sessionSetting.isShowHeaderTitle
  );
};

export const CommentHeader: React.FC<CommentHeaderProps> = React.memo(
  ({
    sessionSidebar,
    showTitleSpin,
    sessionTitle,
    jumpPage,
    themeMode,
    sessionSetting,
    createSession,
    clearCurrentChatList,
    renameSessionTitle,
    triggerSessionSidebar,
    openMessageShare,
    switchThemeMode,
  }) => {
    const { Header } = Layout;
    const [titleRenameVisible, setTitleRenameVisible] =
      useState<boolean>(false);
    const [titleContent, setTitleContent] = useState<string>(sessionTitle);

    const titleContentChange = (content: string) => {
      setTitleContent(content);
    };

    const titleRenameShow = () => {
      setTitleContent(sessionTitle);
      setTitleRenameVisible(true);
    };

    const titleRenameCancel = () => {
      setTitleRenameVisible(false);
    };

    const titleRenameOk = () => {
      renameSessionTitle(titleContent);
      Toast.success({
        content: "修改成功",
        showClose: false,
        duration: 1,
      });
      titleRenameCancel();
    };
    const toSettingPage = async () => {
      // const settingLastPath = await storage.getItem<string>(
      //   "setting_last_path"
      // );
      // debugger;
      // if (settingLastPath) {
      //   jumpPage(settingLastPath);
      // } else {
      jumpPage("/platform/app");
      // }
    };
    return (
      <div className="comment-header flex flex-row items-center">
        <div className="flex felx-row items-center gap-1">
          {/* <div
            className={`comment-header-left${
              sessionSidebar ? "" : " not-show-bar"
            }`}
          >
            <Tooltip content={"会话栏显隐"}>
              <Button
                theme="borderless"
                type="tertiary"
                icon={<IconSidebar />}
                aria-label="会话栏显隐"
                onClick={triggerSessionSidebar}
              />
            </Tooltip>
          </div> */}
          {!sessionSidebar && (
            <div
              className={`transition-opacity duration-300 ${
                !sessionSidebar ? "opacity-100" : "opacity-0"
              }`}
            >
              <Tooltip content={"会话栏显隐"}>
                <Button
                  theme="borderless"
                  type="tertiary"
                  icon={<IconSidebar />}
                  aria-label="会话栏显隐"
                  onClick={triggerSessionSidebar}
                />
              </Tooltip>
            </div>
          )}

          <Button
            type="tertiary"
            icon={<IconEditStroked />}
            iconPosition="right"
            onClick={titleRenameShow}
          >
            {sessionTitle}
            {/* {showTitleSpin ? (
              <IconGlobe spin />
            ) : sessionSetting.isShowHeaderTitle === "true" ? (
              <div style={{ lineHeight: "52px" }}>{sessionTitle}</div>
            ) : (
              ""
            )} */}
          </Button>
        </div>
        <div className="comment-header-right flex flex-row items-center gap-1">
          <div className="flex flex-row items-center gap-1">
            <Tooltip content={"分享对话"}>
              <Button
                theme="borderless"
                type="tertiary"
                icon={<IconShareStroked />}
                aria-label="分享对话"
                onClick={openMessageShare}
              />
            </Tooltip>
            <Tooltip content={"创建新的聊天"}>
              <Button
                theme="borderless"
                type="tertiary"
                icon={<IconPlus />}
                aria-label="创建新的聊天"
                onClick={() => createSession("")}
              />
            </Tooltip>
            <Tooltip content={"清空对话"}>
              <Button
                theme="borderless"
                type="tertiary"
                icon={<IconDeleteStroked />}
                aria-label="清空对话"
                onClick={clearCurrentChatList}
              />
            </Tooltip>
          </div>
          {/* <Tooltip content={"创建新的聊天"}>
           
          </Tooltip> */}
          <Dropdown
            trigger={"click"}
            className="w-[200px]"
            position={"bottomLeft"}
            render={
              <Dropdown.Menu>
                <Dropdown.Item
                  icon={<IconConfig style={{ fontSize: "18px" }} />}
                  onClick={toSettingPage}
                >
                  系统设置
                </Dropdown.Item>
                <Dropdown
                  trigger={"click"}
                  className="w-[200px]"
                  position={"left"}
                  render={
                    <Dropdown.Menu>
                      <Dropdown.Item
                        icon={<IconMoon style={{ fontSize: "18px" }} />}
                        onClick={() => switchThemeMode("dark")}
                      >
                        <div className="w-full flex items-center justify-between">
                          <div>夜间模式</div>
                          {/* {themeMode === "dark" && <IconTick />} */}
                        </div>
                      </Dropdown.Item>
                      <Dropdown.Item
                        icon={<IconSun style={{ fontSize: "18px" }} />}
                        onClick={() => switchThemeMode("light")}
                      >
                        <div className="w-full flex items-center justify-between">
                          <div>白天模式</div>
                          {/* {themeMode === "light" && <IconTick />} */}
                        </div>
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  }
                >
                  <Dropdown.Item
                    icon={<IconColorPlatte style={{ fontSize: "18px" }} />}
                  >
                    <div className="w-full flex items-center justify-between">
                      <div>主题切换</div>
                      <IconChevronRight />
                    </div>
                  </Dropdown.Item>
                </Dropdown>
              </Dropdown.Menu>
            }
          >
            <Avatar color="light-blue" size="small" alt="LynkzHub">
              AI
            </Avatar>
          </Dropdown>
        </div>
        <Modal
          title="标题重命名"
          visible={titleRenameVisible}
          onCancel={titleRenameCancel}
          onOk={titleRenameOk}
          centered
        >
          <Input
            value={titleContent}
            onChange={(content) => titleContentChange(content)}
            placeholder={"请输入聊天标题"}
          />
        </Modal>
      </div>
    );
  },
  headerEqual
);
