import { http } from "@/utils/axios";

const base_url = "/monitor/operlog";

export function getOperLogList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function statsOperLogs(params?: any) {
  return http.request<API.Result<Log.OperLogstatsVO>>({
    url: `${base_url}/logs/stats`,
    params,
    method: "GET",
  });
}

export function countOperLogStatus() {
  return http.request<API.Result<Log.OperLogstats>>({
    url: `${base_url}/logs/count`,
    method: "get",
  });
}


export function getErrorStack(id: number) {
  return http.request<any>({
    url: `${base_url}/stack/${id}`,
    method: "get",
  });
}

export function clean(type: number | string) {
  return http.request<any>({
    url: `${base_url}/clean/${type}`,
    method: "DELETE",
  });
}

export function deleteLog(ids:string) {
  return http.request<API.Result<void>>({
    url: `${base_url}/${ids}`,
    method: "DELETE",
  });
}