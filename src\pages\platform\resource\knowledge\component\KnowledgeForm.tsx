import { useAuth } from "@/components/Auth";
import NotionIcon from "@/components/icon/NotionIcon";
import RemoteIcon from "@/components/icon/RemoteIcon";
import { TextIcon } from "@/components/icon/TextIcon";
import { ResultEnum } from "@/enums/httpEnum";
import { getAccessToken } from "@/utils/auth";
import {
  Avatar,
  Button,
  Checkbox,
  Col,
  Divider,
  Form,
  Icon,
  Modal,
  Row,
  Select,
  Steps,
  Toast,
  Upload,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { addKnowledge } from "@/api/platform/knowledge";
import { IconCamera } from "@douyinfe/semi-icons";
import { getChatModelSelectOptions } from "@/api/platform/chatModel";
import AliLinkIcon from "@/components/icon/AliLinkIcon";
import AppIcon from "@/components/icon/AppIcon";
import useBoolean from "@/hooks/useBoolean";
const KnowledgeForm: React.FC<KnowledgeFormProps> = ({
  open,
  onCancel,
  onOK,
}) => {
  const [current, setCurrent] = useState<number>(0);
  const [aiModels, setAiModels] = useState([]);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const handleCancel = () => {
    setCurrent(0);
    onCancel();
  };
  const handleSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addKnowledge({
        ...values,
        tags: values.tags.join(","),
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  // 表单提交处理
  const submitForm = async () => {
    try {
      const values = await formApiRef.current?.validate();
      await handleSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };

  const formApiRef = React.useRef(null);
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const getAiChatModel = () => {
    getChatModelSelectOptions(1).then(({ data }) => {
      setAiModels(data);
    });
  };
  useEffect(() => {
    if (open) {
      getAiChatModel();
    }
  }, [open]);
  const handleReset = () => {
    formApiRef.current?.reset();
  };
  return (
    <>
      <Modal
        title="新增知识库"
        width={700}
        className="semi-light-scrollbar"
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
        footer={
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button style={{ marginRight: 8 }} onClick={handleReset}>
              重置
            </Button>
            <Button theme="solid" loading={loading} onClick={submitForm}>
              提交
            </Button>
          </div>
        }
        // onOk={handleOnOk}
        visible={open}
        onCancel={handleCancel}
        closeOnEsc={false}
      >
        <div
          className="flex items-center  overflow-auto justify-center"
          style={{ maxHeight: "calc(100vh - 44px+32px)" }}
        >
          <Form getFormApi={getFormApi} style={{ padding: 10, width: "100%" }}>
            <Row gutter={10}>
              <Col span={12}>
                <Form.Input
                  rules={[{ required: true, message: "请填写知识库名称" }]}
                  field="name"
                  placeholder={"请输入知识库名称"}
                  label="知识库名称"
                  trigger="blur"
                />
              </Col>
              <Col span={12}>
                <Form.Select
                  className="w-full"
                  rules={[{ required: true, message: "请选择向量模型" }]}
                  field="embedModel"
                  placeholder="请选择向量模型"
                  showClear
                  filter
                  optionList={aiModels}
                  label="向量模型"
                  trigger="blur"
                />
              </Col>
              <Col span={24}>
                <Form.TagInput
                  className="w-full"
                  placeholder="请输入知识库标签"
                  field="tags"
                  rules={[{ required: true, message: "请输入知识库标签" }]}
                  label="知识库标签"
                  trigger="blur"
                />
              </Col>
              <Col span={24}>
                <Form.TextArea
                  field="description"
                  placeholder="请输入知识库描述"
                  label="知识库描述"
                  rules={[{ required: true, message: "请填写知识库描述" }]}
                  trigger="blur"
                />
              </Col>
            </Row>
          </Form>
        </div>
      </Modal>
    </>
  );
};

export default KnowledgeForm;
