import LoginBanner from "@/components/icon/LoginBanner";
import LoginLogoIcon from "@/components/icon/LoginLogoIcon";
import Icon from "@douyinfe/semi-icons";
import { Button, Dropdown } from "@douyinfe/semi-ui";
import React, { useCallback, useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import classNames from "classnames";
import { useMounted } from "@/hooks/use-mounted";
import { Languages } from "lucide-react";
import { useMediaQuery } from "react-responsive";

const AuthLayout: React.FC<any> = ({ children }) => {
  // 使用 react-responsive 检测不同屏幕尺寸
  const isMobile = useMediaQuery({ maxWidth: 640 });
  const isTablet = useMediaQuery({ minWidth: 641, maxWidth: 1023 });
  const isSmallScreen = useMediaQuery({ maxWidth: 1023 });

  return (
    <div className="relative w-full h-screen overflow-hidden bg-[#fbfbfd] dark:bg-[#1d1d1f]">
      {/* 顶部导航 - 调整移动端间距 */}
      <header className="absolute top-0 left-0 right-0 z-10 flex justify-between items-center px-4 sm:px-6 md:px-8 py-4 sm:py-6">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <LoginLogoIcon className="w-[100px] h-[34px] sm:w-[120px] sm:h-[40px]" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Dropdown
            position="bottomLeft"
            trigger="click"
            render={
              <Dropdown.Menu>
                <Dropdown.Item>中文简体</Dropdown.Item>
                <Dropdown.Item>英文(English)</Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <Button
              className="flex items-center justify-center"
              style={{
                width: isMobile ? "32px" : "36px",
                height: isMobile ? "32px" : "36px",
                borderRadius: "50%",
                backgroundColor: "rgba(0,0,0,0.05)",
                backdropFilter: "blur(10px)",
              }}
              theme="borderless"
              size="small"
              type="tertiary"
            >
              <Icon svg={<Languages size={isMobile ? "14px" : "16px"} />} />
            </Button>
          </Dropdown>
        </motion.div>
      </header>

      {/* 主内容区 - 改进移动端布局，增加底部内边距避免与页脚重叠 */}
      <main className="flex items-center justify-center h-full pt-16 pb-16 sm:pb-20 sm:pt-0">
        <div className="container mx-auto px-4 sm:px-6 flex flex-col lg:flex-row items-center justify-between max-w-6xl">
          {/* 左侧内容 - 移动端优化 */}

          {
            !isSmallScreen && <motion.div
              className="w-full lg:w-1/2 mb-6 sm:mb-10 lg:mb-0 flex flex-col items-center lg:items-start"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-semibold text-[#1d1d1f] dark:text-[#f5f5f7] mb-3 sm:mb-4 text-center lg:text-left tracking-tight">
                欢迎使用 LynkzHub
              </h1>

              <p className="text-lg sm:text-xl text-[#86868b] dark:text-[#a1a1a6] mb-6 sm:mb-8 text-center lg:text-left max-w-lg font-light leading-relaxed">
                连接您的团队，AI简化工作，提高生产力
              </p>

              {/* 在移动端隐藏或缩小Banner */}
              <div className={`relative ${isMobile ? 'hidden' : isTablet ? 'scale-75 -mt-6 -mb-6' : ''}`}>
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                >
                  <LoginBanner className={`${isTablet ? 'w-[360px] h-[360px]' : 'w-[480px] h-[480px]'}`} />
                </motion.div>
              </div>
            </motion.div>
          }

          {/* 右侧登录表单 - 移动端全宽 */}
          <motion.div
            className="w-full lg:w-5/12"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="bg-white dark:bg-[#1d1d1f] rounded-xl sm:rounded-2xl shadow-md border border-[#e5e5e5] dark:border-[#333336] overflow-hidden">
              <div className="p-4 sm:p-8 md:p-10">
                {children}
              </div>

              <div className="bg-[#f5f5f7] dark:bg-[#2d2d2f] px-6 sm:px-8 md:px-10 py-2 sm:py-2 text-center">
                <p className="text-xs sm:text-sm text-[#86868b] dark:text-[#a1a1a6]">
                  登录即表示您同意我们的 <a href="#" className="text-[#0066cc] hover:underline">服务条款</a> 和 <a href="#" className="text-[#0066cc] hover:underline">隐私政策</a>
                </p>
              </div>
            </div>

            {/* 在移动端显示帮助链接 */}
            {isSmallScreen && (
              <div className="mt-4 text-center">
                <p className="text-xs sm:text-sm text-[#86868b] dark:text-[#a1a1a6]">
                  需要帮助? <a href="#" className="text-[#0066cc] hover:underline">联系支持团队</a>
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </main>

      {/* 底部版权信息 - 移动端调整，确保不会与内容重叠 */}
      <footer className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 text-center bg-[#fbfbfd] dark:bg-[#1d1d1f]">
        <motion.p
          className="text-xs sm:text-sm text-[#86868b] dark:text-[#a1a1a6]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          © {new Date().getFullYear()} LynkzHub. All rights reserved.
        </motion.p>
      </footer>

      {/* 苹果风格的背景装饰 */}
      <AppleStyleBackground isMobile={isMobile} />
    </div>
  );
};

// 苹果风格的背景装饰 - 移动端优化
const AppleStyleBackground = ({ isMobile }: { isMobile: boolean }) => {
  return (
    <>
      {/* 顶部渐变光晕 - 移动端缩小 */}
      <div
        className="absolute top-[-300px] right-[-200px] rounded-full bg-gradient-to-br from-[#f2f2f7]/30 to-[#e5e5ea]/10 blur-[100px] dark:from-[#2c2c2e]/20 dark:to-[#1c1c1e]/10 pointer-events-none"
        style={{
          width: isMobile ? '500px' : '800px',
          height: isMobile ? '500px' : '800px',
        }}
      />

      {/* 底部渐变光晕 - 移动端缩小 */}
      <div
        className="absolute bottom-[-300px] left-[-200px] rounded-full bg-gradient-to-tr from-[#f2f2f7]/30 to-[#e5e5ea]/10 blur-[100px] dark:from-[#2c2c2e]/20 dark:to-[#1c1c1e]/10 pointer-events-none"
        style={{
          width: isMobile ? '500px' : '800px',
          height: isMobile ? '500px' : '800px',
        }}
      />

      {/* 微妙的网格背景 */}
      <div
        className="absolute inset-0 opacity-[0.015] dark:opacity-[0.03] pointer-events-none"
        style={{
          backgroundImage: `
            linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: isMobile ? '20px 20px' : '40px 40px'
        }}
      />
    </>
  );
};

export default AuthLayout;
