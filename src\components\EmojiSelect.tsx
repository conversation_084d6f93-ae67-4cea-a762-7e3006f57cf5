import React, { useEffect, useState } from 'react';
import { Popover, withField } from '@douyinfe/semi-ui';

// 动态加载 EmojiPicker
import Picker from '@emoji-mart/react'
// import data from '@emoji-mart/data-apple'
import data from '@emoji-mart/data';
import { init } from 'emoji-mart';
interface EmojiSelectProps {
    value?: string;
    onChange: (emoji: string) => void;
}

const EmojiSelect: React.FC<EmojiSelectProps> = ({ value, onChange }) => {
    const [loadEmoji, setLoadEmoji] = useState(false);
    useEffect(() => {
        init({ data });
    }, [])
    return (
        <Popover
            content={
                loadEmoji ? (
                    <React.Suspense fallback="加载中...">
                        <Picker
                            locale="zh"
                            // categories={["frequent", "people", "flags", "symbols", "places"]}
                            // set="apple"
                            navPosition="none"
                            previewPosition="none"
                            data={data}
                            onEmojiSelect={(e: any) => {
                                console.log(e);

                                onChange(e.native)
                            }}
                        />
                    </React.Suspense>
                ) : null
            }

            trigger="click"
            onVisibleChange={(visible) => {
                if (visible) setLoadEmoji(true);
            }}
        >
            <div
                onMouseEnter={() => setLoadEmoji(true)}
                className="p-1 hover:bg-semi-color-fill-0 rounded-md text-md w-[50px] flex items-center justify-center border border-solid border-semi-color-border cursor-pointer"
            >
                {value}
            </div>
        </Popover>
    );
};

const EmojiSelectField = withField(
    ({ value, onChange, ...rest }) => (
        <EmojiSelect value={value} onChange={onChange} {...rest} />
    ),
    {
        valueKey: 'value',
        onKeyChangeFnName: 'onChange',
        // valuePath: 'native',
    }
);

export { EmojiSelect, EmojiSelectField };