import { addDictType, editDictType, getDictType } from "@/api/system/dict";
import { useBoolean } from "@/hooks";
import { Button, Form, Modal, Spin, Toast } from "@douyinfe/semi-ui";
import React, { useEffect } from "react";

function DdictTypeForm({ open, onCancel, onRefresh, title, id }: any) {
  const formApiRef = React.useRef(null);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const [spinning, { setTrue: openSpinning, setFalse: closeSpinning }] =
    useBoolean(false);
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const handleReset = () => {
    // @ts-expect-error
    formApiRef.current?.reset();
    onCancel();
  };
  const handleCancel = () => {
    onCancel();
    onRefresh();
  };
  const handleSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addDictType(values);
      Toast.success(res.msg);
      closeLoading();
      onRefresh();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const getDictTypeDetail = async () => {
    try {
      openSpinning();
      // setLoading();
      const { data } = await getDictType(id);
      // @ts-expect-error
      formApiRef.current?.setValues(data);
      closeSpinning();
    } catch (err) {
      closeSpinning();
      console.error(err);
    }
  };

  const handleEditSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await editDictType({
        ...values,
        dictId: id
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const submitForm = async () => {
    try {
      // @ts-expect-error
      const values = await formApiRef.current?.validate();
      id ? await handleEditSubmit(values) : await handleSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };
  useEffect(() => {
    if (id) {
      getDictTypeDetail();
    }
  }, [open, id]);
  return (
    <div>
      <Modal
        title={title}
        centered
        width={500}
        className="semi-light-scrollbar"
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
        }}
        footer={
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button style={{ marginRight: 8 }} onClick={handleReset}>
              取消
            </Button>
            <Button theme="solid" loading={loading} onClick={submitForm}>
              提交
            </Button>
          </div>
        }
        visible={open}
        onCancel={handleReset}
        closeOnEsc={false}
      >
        <Spin tip="正在加载..." spinning={spinning}>
          <Form getFormApi={getFormApi}>
            <Form.Input
              field="dictName"
              rules={[{ required: true, message: "请填写知字典名称" }]}
              label="字典名称"
              placeholder={"请输入字典名称"}
              required
            />
            <Form.Input
              field="dictType"
              rules={[{ required: true, message: "请填写字典类型" }]}
              extraText="字典类型只能使用英文/下划线命名"
              placeholder={"请输入字典类型"}
              label="字典类型"
              required
            />
            <Form.TextArea field="remark" label="备注" />
          </Form>
        </Spin>
      </Modal>
    </div>
  );
}
export default DdictTypeForm;
