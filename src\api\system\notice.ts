import { http } from "@/utils/axios";

const base_url = "system/notice";

export function getNoticeList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function getNotice(id: any) {
  return http.request<API.Result>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}
export function addNotice(data: NoticeVO) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}

export function editNotice(data: NoticeVO) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}
export function delNotice(id: number | string) {
  return http.request<API.Result>({
    url: `${base_url}/${id}`,
    method: "DELETE",
  });
}
export function sendNotice(id: number | string) {
  return http.request<API.Result>({
    url: `${base_url}/send/${id}`,
    method: "PUT",
  });
}
export function closeNotice(id: number | string) {
  return http.request<API.Result>({
    url: `${base_url}/close/${id}`,
    method: "DELETE",
  });
}
export function getMeNotice(params?: any) {
  return http.request<API.Result>({
    url: `${base_url}/me`,
    params,
  });
}

export function readNotice(id: number | string) {
  return http.request<API.Result>({
    url: `${base_url}/read/${id}`,
  });
}
