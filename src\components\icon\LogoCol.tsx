import React from "react";

export default function LogoCol(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill="none"
      version="1.1"
      width="417"
      height="252"
      viewBox="0 0 417 252"
    >
      <g>
        <g>
          <path
            d="M147.732984,109.908C147.252295,107.365,147,104.735,147,102.035L147,12C147,5.37258,152.37258,0,159,0L185,0C191.6274,0,197,5.37258,197,12L197,99C197,105.627,202.3726,111,209,111L258,111C264.627,111,270,116.373,270,123L270,138C270,144.627,264.627,150,258,150L181,150C162.7746,150,148,135.225,148,117L148,112.524C148,111.648,147.895676,110.768,147.732984,109.908"
            fillRule="evenodd"
            fill="#3873FA"
            fillOpacity="1"
          />
        </g>
        <g>
          <rect
            x="221"
            y="41"
            width="47"
            height="45"
            rx="9"
            fill="#3873FA"
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M6.79202,232L39.12,232L39.12,223.072L17.448,223.072L17.448,178.648L6.79202,178.648L6.79202,232ZM56.472,248.128C65.472,248.128,69.792,242.728,73.32,233.224L87.648,191.68L77.496,191.68L71.952,210.4C70.944,214.144,69.936,218.03199999999998,69.072,221.776L68.712,221.776C67.56,217.888,66.552,214,65.328,210.4L58.92,191.68L48.264,191.68L63.96,231.208L63.24,233.656C62.088,237.184,59.784,239.776,55.68,239.776C54.744,239.776,53.592,239.488,52.872,239.272L50.928,247.40800000000002C52.512,247.84,54.096,248.128,56.472,248.128ZM103.632,232L114.216,232L114.216,204.28C117.168,201.328,119.328,199.744,122.568,199.744C126.528,199.744,128.256,201.904,128.256,208.168L128.256,232L138.84,232L138.84,206.872C138.84,196.72,135.096,190.672,126.384,190.672C120.912,190.672,116.808,193.552,113.28,197.008L113.064,197.008L112.272,191.68L103.632,191.68L103.632,232ZM159.144,232L169.584,232L169.584,221.776L175.2,215.224L184.992,232L196.512,232L181.392,208.312L195.216,191.68L183.624,191.68L169.872,208.96L169.584,208.96L169.584,174.544L159.144,174.544L159.144,232ZM208.104,232L240,232L240,223.648L221.28,223.648L239.352,197.368L239.352,191.68L210.12,191.68L210.12,200.03199999999998L226.176,200.03199999999998L208.104,226.38400000000001L208.104,232ZM257.856,232L268.512,232L268.512,208.96L288.672,208.96L288.672,232L299.256,232L299.256,178.648L288.672,178.648L288.672,199.744L268.512,199.744L268.512,178.648L257.856,178.648L257.856,232ZM332.88,233.00799999999998C338.424,233.00799999999998,342.24,230.272,345.696,226.168L345.984,226.168L346.776,232L355.416,232L355.416,191.68L344.832,191.68L344.832,218.89600000000002C342.096,222.49599999999998,339.936,223.936,336.696,223.936C332.736,223.936,330.936,221.776,330.936,215.512L330.936,191.68L320.424,191.68L320.424,216.808C320.424,226.95999999999998,324.168,233.00799999999998,332.88,233.00799999999998ZM396.312,233.00799999999998C405.168,233.00799999999998,413.448,225.01600000000002,413.448,211.12C413.448,198.808,407.472,190.672,397.536,190.672C393.576,190.672,389.544,192.61599999999999,386.304,195.496L386.664,189.016L386.664,174.544L376.08,174.544L376.08,232L384.36,232L385.224,227.752L385.584,227.752C388.824,231.136,392.712,233.00799999999998,396.312,233.00799999999998ZM394.008,224.296C391.776,224.296,389.112,223.50400000000002,386.664,221.272L386.664,203.488C389.4,200.752,391.848,199.38400000000001,394.584,199.38400000000001C400.128,199.38400000000001,402.576,203.632,402.576,211.336C402.576,220.12,398.76,224.296,394.008,224.296Z"
            fill="#3D3D3D"
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M290.84,37L292.32,37L292.32,30.83L294.41,30.83L294.41,29.59L288.76,29.59L288.76,30.83L290.84,30.83L290.84,37ZM296.92,37L298.25,37L298.25,33.91C298.25,33.2,298.13,32.18,298.06,31.48L298.1,31.48L298.69,33.22L299.84,36.33L300.69,36.33L301.83,33.22L302.43,31.48L302.48,31.48C302.4,32.18,302.29,33.2,302.29,33.91L302.29,37L303.64,37L303.64,29.59L302,29.59L300.76,33.07C300.61,33.519999999999996,300.48,34.01,300.32,34.480000000000004L300.27,34.480000000000004C300.12,34.01,299.98,33.519999999999996,299.82,33.07L298.56,29.59L296.92,29.59L296.92,37Z"
            fill="#3D3D3D"
            fillOpacity="1"
          />
        </g>
      </g>
    </svg>
  );
}
