import React, { useEffect, useState, useRef, useCallback } from "react";
import mermaid from "mermaid";
import classNames from "classnames";
import { Button, Dropdown, Icon, Typography, Tooltip, Toast } from "@douyinfe/semi-ui";
import { Palette, Download, ZoomIn, ZoomOut, Maximize, Minimize, Copy, Check } from "lucide-react";

const { Text } = Typography;

// 添加CopyButton组件
interface CopyButtonProps {
  getTextFn: () => string;
  textClass?: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ getTextFn, textClass }) => {
  const [copied, setCopied] = useState<boolean>(false);

  const onCopy = async () => {
    try {
      setCopied(true);
      const text = getTextFn();
      await navigator.clipboard.writeText(text);
      Toast.success({
        content: "复制成功",
        showClose: false,
        duration: 1,
      });

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (error) {
      console.log(error);
      Toast.error({
        content: "复制失败",
        showClose: false,
        duration: 1,
      });
      setCopied(false);
    }
  };

  return (
    <Tooltip content="复制">
      <Button
        theme="borderless"
        type="tertiary"
        size="small"
        icon={
          !copied ? (
            <Icon svg={<Copy className={`${textClass}`} size="1em" />} />
          ) : (
            <Icon
              svg={<Check size="1em" />}
              style={{ color: "var(--semi-color-success)" }}
            />
          )
        }
        aria-label="复制"
        onClick={onCopy}
      />
    </Tooltip>
  );
};

interface MermaidBoxProps {
  elementId: string;
  lang: string;
  chartContent: string;
}

const isEqual = (prevProps: MermaidBoxProps, currentProps: MermaidBoxProps) => {
  return prevProps.elementId === currentProps.elementId
    && prevProps.chartContent.length === currentProps.chartContent.length;
}

export const MermaidBox: React.FC<MermaidBoxProps> = React.memo((
  {
    lang,
    elementId,
    chartContent
  }
) => {
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const tabs = [{
    text: '图表',
    value: 'chart'
  }, {
    text: '代码',
    value: 'code'
  }];
  const [currentTabs, setCurrentTabs] = useState<string>(tabs[0].value);
  const [codeTheme, setCodeTheme] = useState<"light" | "dark">("light");
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState<number>(1);
  const [fitToScreen, setFitToScreen] = useState<boolean>(true);
  const [renderKey, setRenderKey] = useState<number>(0); // 用于强制重新渲染

  // 获取代码文本的函数，用于复制功能
  const getCodeText = () => {
    return chartContent;
  };

  // 尝试修复常见的Mermaid语法错误
  const fixMermaidSyntax = (content: string): string => {
    let fixedContent = content;

    // 修复箭头后缺少消息的问题 - 更精确的正则表达式
    fixedContent = fixedContent.replace(/(\w+)(-|--)(>|>>)(\w+):(\s*$|\s*\n)/gm, '$1$2$3$4: 消息\n');

    // 修复非标准箭头语法 => 改为 ->> (高亮箭头)
    fixedContent = fixedContent.replace(/(\w+)=>(\w+):/g, '$1->>$2:');
    fixedContent = fixedContent.replace(/(\w+)=>(\w+)(\s+)(.+)/g, '$1->>$2$3$4');

    // 确保序列图以 sequenceDiagram 开头
    if (fixedContent.trim().startsWith('sequenceDiagram') === false &&
      fixedContent.includes('->') &&
      (fixedContent.includes('participant') || fixedContent.match(/\w+\s*->/))) {
      fixedContent = 'sequenceDiagram\n' + fixedContent;
    }

    // 确保每行箭头后有消息文本
    fixedContent = fixedContent.replace(/(\w+)(-|--|->|-->|->>|-->>)(\w+)(\s*)$/gm, '$1$2$3: 消息');

    // 修复group语法问题
    const lines = fixedContent.split('\n');
    const newLines = [];

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();

      // 处理group语法
      if (line.startsWith('group') && !line.match(/group\s+\w+\s*$/)) {
        // 如果group后面没有名称，添加一个默认名称
        if (line === 'group') {
          newLines.push('group GroupName');
        } else {
          // 如果group后面有内容但格式不正确
          const parts = line.split(/\s+/);
          if (parts.length > 1) {
            // 提取group后的第一个词作为组名
            newLines.push(`group ${parts[1]}`);

            // 如果这行还有其他内容（可能是序列图命令），作为单独的行添加
            if (parts.length > 2) {
              const restOfLine = parts.slice(2).join(' ');
              if (restOfLine.includes('->')) {
                newLines.push(restOfLine);
              }
            }
          } else {
            newLines.push('group GroupName');
          }
        }
      } else {
        newLines.push(line);
      }
    }

    return newLines.join('\n');
  };

  // 使用useCallback包装renderChart，避免不必要的重新创建
  const renderChart = useCallback(async () => {
    if (currentTabs !== 'chart' || !ref.current) return;

    try {
      setError(null);
      // 清空之前的内容
      if (ref.current) {
        ref.current.innerHTML = '';
      }

      // 确保每次渲染都有唯一ID
      const uniqueId = `mermaid-${elementId}-${Date.now()}`;

      // 配置mermaid主题
      mermaid.initialize({
        startOnLoad: false,
        theme: codeTheme === "dark" ? "dark" : "default",
        securityLevel: "loose",
        flowchart: {
          htmlLabels: true,
          curve: 'linear',
        },
        sequence: {
          diagramMarginX: 50,
          diagramMarginY: 10,
          actorMargin: 50,
          width: 150,
          height: 65,
          boxMargin: 10,
          boxTextMargin: 5,
          noteMargin: 10,
          messageMargin: 35,
        },
        logLevel: 5, // 设置为最详细的日志级别
        deterministicIds: false, // 禁用确定性ID
        fontFamily: 'Arial, sans-serif' // 设置字体
      });

      // 预处理内容
      let processedContent = chartContent.trim();

      // 确保序列图以 sequenceDiagram 开头
      if (!processedContent.startsWith('sequenceDiagram') &&
        (processedContent.includes('->') || processedContent.includes('participant'))) {
        processedContent = 'sequenceDiagram\n' + processedContent;
      }

      // 确保group语法正确
      processedContent = processedContent.replace(/group\s*$/gm, 'group DefaultGroup');

      // 确保end前有内容
      processedContent = processedContent.replace(/^\s*end\s*$/gm, '    Note over Alice: Empty group\nend');

      console.log("Rendering mermaid chart with content:", processedContent);

      try {
        const { svg, bindFunctions } = await mermaid.render(uniqueId, processedContent);

        if (ref.current) {
          ref.current.innerHTML = svg;
          if (bindFunctions) {
            bindFunctions(ref.current);
          }

          // 确保SVG适应容器大小
          const svgElement = ref.current.querySelector('svg');
          if (svgElement) {
            svgElement.setAttribute('width', '100%');
            svgElement.setAttribute('height', fitToScreen ? '100%' : 'auto');
            svgElement.style.maxWidth = '100%';

            // 修改这里：使用transform-origin为左上角，确保缩放时不会超出容器
            svgElement.style.transform = `scale(${zoom})`;
            svgElement.style.transformOrigin = 'top left';

            // 添加这一行：设置容器的overflow为auto，确保可以滚动查看放大后的内容
            ref.current.style.overflow = 'auto';

            svgElement.style.transition = 'transform 0.3s ease';
          }
        }
      } catch (firstError) {
        console.error("First attempt failed:", firstError);

        // 第一次尝试失败，尝试更严格的修复
        try {
          // 尝试修复语法
          const fixedContent = fixMermaidSyntax(processedContent);
          console.log("Retrying with fixed content:", fixedContent);

          const { svg, bindFunctions } = await mermaid.render(uniqueId + '-fixed', fixedContent);

          if (ref.current) {
            ref.current.innerHTML = svg;
            if (bindFunctions) {
              bindFunctions(ref.current);
            }

            // 确保SVG适应容器大小
            const svgElement = ref.current.querySelector('svg');
            if (svgElement) {
              svgElement.setAttribute('width', '100%');
              svgElement.setAttribute('height', fitToScreen ? '100%' : 'auto');
              svgElement.style.maxWidth = '100%';

              // 同样修改这里：使用transform-origin为左上角
              svgElement.style.transform = `scale(${zoom})`;
              svgElement.style.transformOrigin = 'top left';

              // 添加这一行：设置容器的overflow为auto
              ref.current.style.overflow = 'auto';

              svgElement.style.transition = 'transform 0.3s ease';
            }
          }
        } catch (secondError) {
          console.error("Second attempt failed:", secondError);
          // 如果两次尝试都失败，显示一个基本的错误消息
          if (ref.current) {
            ref.current.innerHTML = `<div style="color: #e53e3e; padding: 10px; text-align: left;">
              <p>图表渲染失败，请检查语法。</p>
              <pre style="background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 12px; overflow: auto;">${processedContent.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
            </div>`;
          }
          throw secondError;
        }
      }
    } catch (error) {
      console.error("Mermaid rendering error:", error);
      if (error instanceof Error) {
        setError(error.message);
        console.log(error.message);
      } else {
        setError("图表渲染失败");
        console.error(error);
      }
    }
  }, [chartContent, codeTheme, currentTabs, elementId, fitToScreen, zoom]);

  // 处理标签切换
  const handleTabChange = (tabValue: string) => {
    setCurrentTabs(tabValue);
    // 如果切换到图表标签，设置一个短暂延迟后渲染图表
    if (tabValue === 'chart') {
      setTimeout(() => {
        renderChart();
      }, 50);
    }
  };

  // 组件挂载时初始化
  useEffect(() => {
    // 组件挂载后立即尝试渲染图表
    if (chartContent) {
      // 使用短暂延迟确保DOM已完全加载
      const timer = setTimeout(() => {
        renderChart();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, []); // 空依赖数组，仅在挂载时执行一次

  // 监听主题变化，强制重新渲染
  useEffect(() => {
    // 增加renderKey触发完全重新渲染
    setRenderKey(prev => prev + 1);

    // 延迟执行渲染，确保DOM已更新
    if (currentTabs === 'chart') {
      const timer = setTimeout(() => {
        renderChart();
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [codeTheme, renderChart, currentTabs]);

  // 监听内容变化
  useEffect(() => {
    if (chartContent && currentTabs === 'chart') {
      renderChart();
    }
  }, [chartContent, renderChart]);

  // 监听缩放和适应屏幕设置变化
  useEffect(() => {
    if (currentTabs === 'chart' && ref.current) {
      const svgElement = ref.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.transform = `scale(${zoom})`;
        svgElement.style.transformOrigin = 'top left'; // 确保缩放原点一致
        svgElement.setAttribute('height', fitToScreen ? '100%' : 'auto');

        // 根据是否适应屏幕来调整容器样式
        if (fitToScreen) {
          ref.current.style.display = 'flex';
          ref.current.style.justifyContent = 'center';
          ref.current.style.alignItems = 'center';
        } else {
          ref.current.style.display = 'block';
        }
      }
    }
  }, [zoom, fitToScreen, currentTabs]);

  const switchCodeTheme = (theme: "light" | "dark") => {
    setCodeTheme(theme);
  };

  // 下载图表为PNG
  const downloadChart = () => {
    if (!ref.current) return;

    const svgElement = ref.current.querySelector('svg');
    if (!svgElement) return;

    // 创建一个临时的Canvas元素
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 获取SVG的尺寸
    const bbox = svgElement.getBBox();
    const width = bbox.width;
    const height = bbox.height;

    // 设置Canvas尺寸
    canvas.width = width * 2; // 2倍大小以提高清晰度
    canvas.height = height * 2;
    ctx.scale(2, 2);

    // 将SVG转换为XML字符串
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    // 创建图像并绘制到Canvas
    const img = new Image();
    img.onload = () => {
      ctx.fillStyle = codeTheme === 'dark' ? '#1e1e1e' : '#ffffff';
      ctx.fillRect(0, 0, width, height);
      ctx.drawImage(img, 0, 0, width, height);
      URL.revokeObjectURL(url);

      // 将Canvas转换为PNG并下载
      const pngUrl = canvas.toDataURL('image/png');
      const a = document.createElement('a');
      a.href = pngUrl;
      a.download = `mermaid-diagram-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    };
    img.src = url;
  };

  // 放大图表
  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  // 缩小图表
  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  // 切换适应屏幕
  const toggleFitToScreen = () => {
    setFitToScreen(!fitToScreen);
  };


  return (
    <div
      ref={containerRef}
      className={classNames("w-full flex flex-col rounded-md border border-semi-color-border border-solid overflow-hidden hover:shadow-sm code-block-container", {
        "bg-white": codeTheme === "light",
        "bg-gray-800": codeTheme === "dark"
      })}
    >
      <div className={classNames("h-[42px] w-full p-1 flex justify-between items-center px-2 border-b", {
        "bg-semi-color-fill-1 border-semi-color-border": codeTheme === "light",
        "bg-gray-700 border-gray-600": codeTheme === "dark"
      })}>
        <div className="flex p-2 items-center gap-1">
          {
            tabs.map(item => {
              return (
                <div
                  key={item.value}
                  className={classNames("p-2 px-3 cursor-pointer rounded-md", {
                    // 当前选中的标签样式
                    "bg-white text-black font-medium": currentTabs === item.value && codeTheme === "light",
                    "bg-gray-900 text-white font-medium": currentTabs === item.value && codeTheme === "dark",
                    // 未选中的标签样式
                    "hover:bg-semi-color-fill-0": codeTheme === "light" && currentTabs !== item.value,
                    "hover:bg-gray-600 text-gray-300": codeTheme === "dark" && currentTabs !== item.value,
                    "text-gray-500": codeTheme === "light" && currentTabs !== item.value,
                  })}
                  onClick={() => handleTabChange(item.value)}
                >
                  {item.text}
                </div>
              )
            })
          }
        </div>
        <div className="flex space-x-1">
          {currentTabs === 'chart' ? (
            <>
              <Tooltip content="下载图表">
                <Button
                  theme="borderless"
                  type="tertiary"
                  size="small"
                  icon={<Icon svg={<Download size="1em" />} className={codeTheme === "dark" ? "text-white" : ""} />}
                  onClick={downloadChart}
                  aria-label="下载图表"
                />
              </Tooltip>
              <Tooltip content="放大">
                <Button
                  theme="borderless"
                  type="tertiary"
                  size="small"
                  icon={<Icon svg={<ZoomIn size="1em" />} className={codeTheme === "dark" ? "text-white" : ""} />}
                  onClick={zoomIn}
                  aria-label="放大"
                />
              </Tooltip>
              <Tooltip content="缩小">
                <Button
                  theme="borderless"
                  type="tertiary"
                  size="small"
                  icon={<Icon svg={<ZoomOut size="1em" />} className={codeTheme === "dark" ? "text-white" : ""} />}
                  onClick={zoomOut}
                  aria-label="缩小"
                />
              </Tooltip>
            </>
          ) : (
            <>
              {/* 使用新的CopyButton组件 */}
              <CopyButton
                textClass={codeTheme === "dark" ? "text-white" : ""}
                getTextFn={getCodeText}
              />
            </>
          )}
          <Dropdown
            showTick
            trigger={"click"}
            render={
              <Dropdown.Menu>
                <Dropdown.Item
                  active={codeTheme === "light"}
                  onClick={() => switchCodeTheme("light")}
                >
                  浅色模式
                </Dropdown.Item>
                <Dropdown.Item
                  active={codeTheme === "dark"}
                  onClick={() => switchCodeTheme("dark")}
                >
                  深色模式
                </Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <Button
              theme="borderless"
              type="tertiary"
              size="small"
              icon={<Icon svg={<Palette size="1em" />} className={codeTheme === "dark" ? "text-white" : ""} />}
              aria-label="主题模式"
            />
          </Dropdown>
        </div>
      </div>
      <div className={classNames("p-4", { "text-white": codeTheme === "dark" })}>
        {currentTabs === 'chart' ? (
          <div
            className={classNames("w-full h-[300px]", {
              "flex items-center justify-center": fitToScreen && zoom <= 1
            })}
            style={{
              textAlign: 'center',
              padding: 10,
              overflow: 'auto', // 确保始终可以滚动
              position: 'relative' // 添加相对定位
            }}
            ref={ref}
          ></div>
        ) : (
          <pre className={classNames("text-sm overflow-auto", { "bg-gray-900 p-3 rounded": codeTheme === "dark" })}>
            <code className="language-mermaid">{chartContent}</code>
          </pre>
        )}
      </div>
    </div>
  )
}, isEqual);
