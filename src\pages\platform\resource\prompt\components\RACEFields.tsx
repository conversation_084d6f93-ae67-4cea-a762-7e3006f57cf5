import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const RACEFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.role"
          label="角色"
          rows={6}
          placeholder="指定角色"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.action"
          label="行动"
          rows={6}
          placeholder="指定行动方案"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.background"
          label="背景"
          rows={6}
          placeholder="描述背景信息"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.expectation"
          label="期望"
          rows={6}
          placeholder="描述期望结果"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default RACEFields;