import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const ROSESFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.role"
          label="角色"
          rows={6}
          placeholder="定义角色"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.objective"
          label="客观"
          rows={6}
          placeholder="描述客观条件"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.scenario"
          label="场景"
          rows={6}
          placeholder="描述场景"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.solution"
          label="解决方案"
          rows={6}
          placeholder="提供解决方案"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.steps"
          label="步骤"
          rows={6}
          placeholder="描述步骤"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default ROSESFields;