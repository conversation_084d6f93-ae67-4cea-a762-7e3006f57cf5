import LoginBanner from "@/components/icon/LoginBanner";
import LoginLogoIcon from "@/components/icon/LoginLogoIcon";
import { Logo } from "@/components/icon/Logo";
import Icon, { IconLanguage } from "@douyinfe/semi-icons";
import { Button, Dropdown, Select } from "@douyinfe/semi-ui";
import React, { useCallback, useEffect, useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import classNames from "classnames";
import { useMounted } from "@/hooks/use-mounted";
import { Languages } from "lucide-react";

const AuthLayout: React.FC<any> = ({ children }) => {
  return (
    <div className="relative w-full overflow-hidden flex flex-col h-screen bg-semi-color-fill-0 dark:bg-semi-color-black">
      <div className="flex-1">
        <div className="flex min-h-screen w-full">
          <div className="hidden lg:flex lg:w-1/2 bg-muted border-r flex-col p-18 relative">
            <div className="absolute inset-0 w-full h-full">
              <BackgroundPaths />
            </div>
            <h1 className="text-xl absolute top-10 left-10 font-semibold flex items-center gap-3 animate-in fade-in duration-1000">
              <LoginLogoIcon className="w-[140px] h-[50px]" />
            </h1>
            <div className="flex-1" />
            <FlipWords
              words={["更便捷，更高效，更专业，更放心"]}
              className="mb-[100px] ml-10 text-lg"
            />
          </div>

          <div className="w-full lg:w-1/2 p-6 bg-semi-color-white">
            <div className="w-full h-full flex  flex-col p-4 md:p-8 justify-center">
              <div className="absolute right-10 top-10">
                <Dropdown
                  position="bottomLeft"
                  trigger="click"
                  render={
                    <Dropdown.Menu>
                      <Dropdown.Item >
                      中文简体
                      </Dropdown.Item>
                      <Dropdown.Item>
                      英文(English)
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  }
                >
                  <Button
                    style={{
                      width: "40px",
                      height: "40px",
                      borderRadius: "50%",
                    }}
                    theme="borderless"
                    size="small"
                    type="tertiary"
                  >
                    <Icon svg={<Languages size={"18px"} />} />
                  </Button>
                </Dropdown>
              </div>
              <div className="enter-x mt-6 w-full sm:mx-auto md:max-w-md animate-in fade-in duration-1000">
                {children}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const FloatingPaths: React.FC<any> = ({ position }) => {
  const generatePathData = React.useCallback(() => {
    return Array.from({ length: 36 }, (_, i) => ({
      id: i,
      d: `M-${380 - i * 5 * position} -${189 + i * 6}
         C-${380 - i * 5 * position} -${189 + i * 6}
           -${312 - i * 5 * position} ${216 - i * 6}
           ${152 - i * 5 * position} ${343 - i * 6}
         C${616 - i * 5 * position} ${470 - i * 6}
           ${684 - i * 5 * position} ${875 - i * 6}
           ${684 - i * 5 * position} ${875 - i * 6}`,
      color: `rgba(${15 + i * 2}, ${23 + i * 1.5}, ${42 + i}, ${0.1 + i * 0.02})`,
      width: 0.5 + i * 0.025,
    }));
  }, [position]);

  const paths = React.useMemo(() => generatePathData(), [generatePathData]);

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg
        className="w-full h-full text-muted-foreground"
        viewBox="0 0 696 316"
        fill="none"
      >
        <title>Background Paths</title>
        {paths.map((path) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke={path.color}
            strokeWidth={path.width}
            initial={{ pathLength: 0 }}
            animate={{
              pathLength: 1,
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 20 + Math.random() * 15,
              repeat: Infinity,
              ease: "linear",
              repeatType: "loop",
            }}
          />
        ))}
      </svg>
    </div>
  );
};

function BackgroundPaths() {
  return (
    <div className="relative h-full w-full flex items-center justify-center overflow-hidden">
      <div className="absolute inset-0">
        <FloatingPaths position={1} />
        <FloatingPaths position={-1} />
      </div>
    </div>
  );
}

const FlipWords = ({
  words,
  duration = 3000,
  className,
}: {
  words: string[];
  duration?: number;
  className?: string;
}) => {
  const [currentWord, setCurrentWord] = useState(words[0]);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const mounted = useMounted();

  // thanks for the fix Julian - https://github.com/Julian-AT
  const startAnimation = useCallback(() => {
    const word = words[words.indexOf(currentWord) + 1] || words[0];
    setCurrentWord(word);
    setIsAnimating(true);
  }, [currentWord, words]);

  useEffect(() => {
    if (!isAnimating)
      setTimeout(() => {
        startAnimation();
      }, duration);
  }, [isAnimating, duration, startAnimation]);

  if (!mounted) return null;

  return (
    <AnimatePresence
      onExitComplete={() => {
        setIsAnimating(false);
      }}
    >
      <motion.div
        initial={{
          opacity: 0,
          y: 10,
        }}
        animate={{
          opacity: 1,
          y: 0,
        }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 10,
        }}
        exit={{
          opacity: 0,
          y: -40,
          x: 40,
          filter: "blur(8px)",
          scale: 2,
          position: "absolute",
        }}
        className={classNames(
          "z-10 inline-block relative text-left text-foreground px-2",
          className
        )}
        key={currentWord}
      >
        {/* edit suggested by Sajal: https://x.com/DewanganSajal */}
        {currentWord.split(" ").map((word, wordIndex) => (
          <motion.span
            key={word + wordIndex}
            initial={{ opacity: 0, y: 10, filter: "blur(4px)" }}
            animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
            transition={{
              delay: wordIndex * 0.01,
              duration: 0.03,
            }}
            className="inline-block whitespace-nowrap"
          >
            {word.split("").map((letter, letterIndex) => (
              <motion.span
                key={word + letterIndex}
                initial={{ opacity: 0, y: 10, filter: "blur(4px)" }}
                animate={{ opacity: 1, y: 0, filter: "blur(0px)" }}
                transition={{
                  delay: wordIndex * 0.2 + letterIndex * 0.08,
                  duration: 0.2,
                }}
                className="inline-block"
              >
                {letter}
              </motion.span>
            ))}
            <span className="inline-block">&nbsp;</span>
          </motion.span>
        ))}
      </motion.div>
    </AnimatePresence>
  );
};

export default AuthLayout;
