import { http } from "@/utils/axios";

const base_url = "aigc/v1/knowledge";
export function addKnowledge(params: any) {
  return http.request<API.Result>({
    url: base_url,
    method: "POST",
    params,
  });
}

export function getKnowledgeList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function getKnowledge(id: any) {
  return http.request<API.Result>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}
