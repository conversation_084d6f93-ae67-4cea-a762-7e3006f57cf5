import { getDeptTree, getUserList } from "@/api/system/user";
import {
  IconDeleteStroked,
  IconEditStroked,
  IconMore,
  IconPlusStroked,
} from "@douyinfe/semi-icons";
import { Button, Dropdown, Tree, Typography } from "@douyinfe/semi-ui";
import React, { useEffect, useState } from "react";
import DeptForm from "./dept-form";

export const DeptList = ({ onDeptSelect, onDeptData }: any) => {
  const [deptTree, setDeptTree] = useState<any>([]);
  const titles = ["新增部门", "修改部门"];
  const types = ["add", "edit"];
  const [id, setId] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const [title, setTitle] = useState<any>(titles[0]);
  const [parentId, setParentId] = useState<any>(null);
  const [selectedKey, setSelectedKey] = useState()
  const handleOpen = (parentId: any, id?: any) => {
    if (id) {
      setTitle(titles[1]);
      setId(id);
    } else {
      setTitle(titles[0]);
      setId(id);
    }
    setParentId(parentId);
    setOpen(true);
  };

  const getDeptList = () => {
    getDeptTree().then((res) => {
      setDeptTree(res.data);
      // setId(res.data[0].value)
      onDeptData(res.data)
      setSelectedKey(res.data[0].value)
    });
  };
  useEffect(() => {
    getDeptList();
  }, []);
  const renderLabel = (label: any, item: any) => (
    <div className="group/item flex items-center">
      <Typography.Text
        className="cursor-pointer py-1"
        ellipsis={{ showTooltip: true }}
        style={{ width: "calc(100% - 48px)" }}
      >
        {label}
      </Typography.Text>
      <Dropdown
        position={"rightTop"}
        render={
          <Dropdown.Menu className="w-[200px]">
            <Dropdown.Item
              icon={<IconPlusStroked />}
              onClick={() => handleOpen(item.parentId)}
            >
              添加部门
            </Dropdown.Item>
            <Dropdown.Item
              icon={<IconEditStroked />}
              onClick={() => handleOpen(item.parentId, item.id)}
            >
              修改部门
            </Dropdown.Item>
            <Dropdown.Divider />
            <Dropdown.Item icon={<IconDeleteStroked />} type="danger">
              删除部门
            </Dropdown.Item>
          </Dropdown.Menu>
        }
      >
        <div className="group/edit invisible group-hover/item:visible ">
          <Button
            theme="borderless"
            type="tertiary"
            onClick={(e) => {
              e.stopPropagation();
            }}
            icon={<IconMore />}
            size="small"
          />
        </div>
      </Dropdown>
    </div>
  );
  return (
    <>
      <Tree
        treeData={deptTree}
        filterTreeNode
        renderLabel={renderLabel}
        onSelect={(selectedKeys: any, info: any) => {
          setSelectedKey(selectedKeys)
          onDeptSelect(selectedKeys);
        }}
      // showFilteredOnly={showFilteredOnly}
      // style={style}
      />
      <DeptForm
        title={title}
        open={open}
        parentId={parentId}
        onRefresh={getDeptList}
        id={id}
        onCancel={() => {
          setOpen(false);
        }}
      />
    </>
  );
};
