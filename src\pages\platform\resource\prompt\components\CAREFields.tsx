import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const CAREFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.context"
          label="上下文"
          rows={6}
          placeholder="提供上下文"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.action"
          label="行动"
          rows={6}
          placeholder="提供行动指南"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.result"
          label="结果"
          rows={6}
          placeholder="预期结果"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.example"
          label="示例"
          rows={6}
          placeholder="给出示例"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default CAREFields;