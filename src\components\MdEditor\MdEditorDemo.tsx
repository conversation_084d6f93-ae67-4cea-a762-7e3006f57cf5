import React, { useState } from 'react';
import { Card, Typography, Space, Button, Toast } from '@douyinfe/semi-ui';
import PromptEditor from './PromptEditor';

const { Title, Paragraph } = Typography;

const MdEditorDemo: React.FC = () => {
  const [content, setContent] = useState(`# AI 助手提示词

你是一个专业的 AI 助手，请遵循以下指导原则：

## 回答风格
- 保持**友好**和**专业**的语气
- 提供*准确*和*有用*的信息
- 使用清晰简洁的语言

## 核心能力
- 理解和分析用户问题
- 提供结构化的回答
- 承认知识的局限性

> 重要：始终以用户的需求为中心，提供最有价值的帮助。

## 示例对话

**用户**: 请帮我解释一下机器学习的基本概念。

**助手**: 机器学习是人工智能的一个分支，主要包括：

1. **监督学习** - 使用标记数据训练模型
2. **无监督学习** - 从未标记数据中发现模式
3. **强化学习** - 通过奖励机制学习最优策略

请根据用户的具体问题，提供相应的专业建议。`);

  const handleSave = () => {
    Toast.success('提示词已保存！');
    console.log('保存的内容:', content);
  };

  const handleClear = () => {
    setContent('');
    Toast.info('内容已清空');
  };

  const loadTemplate = (template: string) => {
    const templates = {
      'ai-assistant': `# AI 助手提示词

你是一个专业的 AI 助手，具有以下特点：

## 角色定义
- **专业性** - 在各个领域都有深入的知识
- **友好性** - 以友善、耐心的态度回答问题
- **准确性** - 提供准确、可靠的信息

## 回答风格
- 简洁明了，重点突出
- 结构化组织信息
- 提供实用的建议

## 限制条件
- 不提供有害或不当内容
- 承认知识的局限性
- 鼓励用户独立思考

请根据用户的问题，提供有帮助的回答。`,

      'code-review': `# 代码审查提示词

请对以下代码进行专业的审查，重点关注：

## 审查要点
- **代码质量** - 可读性、可维护性
- **性能优化** - 潜在的性能问题
- **安全性** - 安全漏洞和风险
- **最佳实践** - 是否遵循行业标准

## 输出格式
1. **总体评价** - 代码的整体质量
2. **具体问题** - 发现的问题和建议
3. **改进建议** - 具体的优化方案

\`\`\`javascript
// 在这里粘贴需要审查的代码
\`\`\`

请提供详细的审查报告。`,

      'writing-assistant': `# 写作助手提示词

我需要你帮助我改进以下文本：

## 改进目标
- **清晰度** - 让表达更加清晰明了
- **流畅性** - 提高文本的可读性
- **准确性** - 确保信息准确无误
- **风格** - 保持一致的写作风格

## 具体要求
- 保持原意不变
- 修正语法错误
- 优化句式结构
- 增强表达力

> 请在下方粘贴需要改进的文本内容

---

**原文本**：
[在此处粘贴原文本]

**改进后**：
[提供改进建议]`
    };

    setContent(templates[template as keyof typeof templates] || '');
  };

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-6">
      <div className="text-center mb-8">
        <Title heading={1} className="mb-4">
          增强版提示词编辑器
        </Title>
        <Paragraph className="text-gray-600 text-lg">
          高度定制 · 斜杠命令 · 语法高亮 · 智能提示
        </Paragraph>
      </div>

      {/* 快速模板 */}
      <Card title="快速模板" className="shadow-sm border-gray-200">
        <Space wrap>
          <Button
            onClick={() => loadTemplate('ai-assistant')}
            type="tertiary"
          >
            AI 助手模板
          </Button>
          <Button
            onClick={() => loadTemplate('code-review')}
            type="tertiary"
          >
            代码审查模板
          </Button>
          <Button
            onClick={() => loadTemplate('writing-assistant')}
            type="tertiary"
          >
            写作助手模板
          </Button>
        </Space>
      </Card>

      {/* 编辑器演示 */}
      <Card
        title="编辑器演示"
        className="shadow-sm border-gray-200"
        headerExtraContent={
          <Space>
            <Button onClick={handleClear} type="tertiary">
              清空内容
            </Button>
            <Button onClick={handleSave} theme="solid" type="primary">
              保存内容
            </Button>
          </Space>
        }
      >
        <Space vertical className="w-full" spacing="medium">
          <Paragraph type="secondary" className="text-gray-600">
            这是一个高度定制化的提示词编辑器。支持斜杠命令快速插入内容，1级标题使用主色调，变量语法自动加粗高亮。
            输入 / 可以打开命令菜单，支持键盘导航和快速插入各种 Markdown 元素。
          </Paragraph>

          <PromptEditor
            value={content}
            onChange={setContent}
            placeholder="开始编写您的提示词..."
            minHeight={500}
            maxHeight={800}
          />
        </Space>
      </Card>

      {/* 功能说明 */}
      <Card title="功能特色" className="shadow-sm border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Title heading={4} className="text-gray-900 mb-3">斜杠命令</Title>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• <strong>/h1</strong> - 一级标题（主色调）</li>
              <li>• <strong>/h2</strong> - 二级标题</li>
              <li>• <strong>/bold</strong> - 粗体文本</li>
              <li>• <strong>/code</strong> - 行内代码</li>
              <li>• <strong>/quote</strong> - 引用块</li>
              <li>• <strong>/list</strong> - 无序列表</li>
              <li>• <strong>/table</strong> - 插入表格</li>
              <li>• <strong>/link</strong> - 插入链接</li>
              <li>• <strong>/image</strong> - 插入图片</li>
              <li>• <strong>/variable</strong> - 插入变量</li>
            </ul>
          </div>

          <div>
            <Title heading={4} className="text-gray-900 mb-3">定制化特性</Title>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• <strong>主色调标题</strong> - H1 标题使用蓝色主题</li>
              <li>• <strong>变量高亮</strong> - 双花括号变量自动加粗</li>
              <li>• <strong>智能菜单</strong> - 输入 / 显示命令菜单</li>
              <li>• <strong>键盘导航</strong> - 方向键选择命令</li>
              <li>• <strong>语法高亮</strong> - 实时语法着色</li>
              <li>• <strong>无工具栏</strong> - 专注内容编写</li>
              <li>• <strong>快捷插入</strong> - 一键插入常用元素</li>
              <li>• <strong>响应式设计</strong> - 移动端友好</li>
            </ul>
          </div>
        </div>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明" className="shadow-sm border-gray-200">
        <Space vertical spacing="medium">
          <div>
            <Title heading={4} className="text-gray-900">基础用法</Title>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto border border-gray-200 mt-2">
              {`import EnhancedMdEditor from './components/MdEditor/EnhancedMdEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <EnhancedMdEditor
      value={content}
      onChange={setContent}
      placeholder="开始输入..."
      minHeight={400}
    />
  );
}`}
            </pre>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default MdEditorDemo;
