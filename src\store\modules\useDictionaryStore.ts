import { create } from "zustand";
import { persist, devtools, PersistStorage, StorageValue } from "zustand/middleware";

export interface DictionaryState {
  dictionary: Record<string, any>;
  setData: (data: Record<string, any>) => void;
  cleanData: () => void;
}

// 包装 localStorage 以符合 PersistStorage 接口
const storage: PersistStorage<DictionaryState> = {
  getItem: (name): StorageValue<DictionaryState> | Promise<StorageValue<DictionaryState> | null> | null => {
    const item = localStorage.getItem(name);
    return item ? JSON.parse(item) : null;
  },
  setItem: (name, value) => {
    localStorage.setItem(name, JSON.stringify(value));
  },
  removeItem: (name) => {
    localStorage.removeItem(name);
  },
};

const createPersistedDictionarySlice = persist<DictionaryState>(
  (set, get) => ({
    dictionary: {},
    setData: (dictionary: Record<string, any>) => set({ dictionary }),
    cleanData: () => {set({ dictionary: {} })
    //删除缓存的数据
    storage.removeItem('dictionary-storage')
  },
  }),
  {
    name: "dictionary-storage",
    storage: storage, // 使用包装后的 storage
  }
);

export const createDictionarySlice = devtools(createPersistedDictionarySlice);

export const useDictionaryStore = create(createDictionarySlice);