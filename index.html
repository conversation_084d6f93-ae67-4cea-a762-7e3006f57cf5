<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="renderer" content="webkit" />
  <meta name="description" content="LynkzHub" />
  <meta name="keywords" content="LynkzHub" />
  <meta name="author" content="Herther" />
  <link rel="icon" href="/favicon.svg" />
  <meta name="viewport"
    content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0" />
  <title>
    <%= VITE_APP_TITLE %>
  </title>
  <script>
    // 生产环境下注入百度统计
    if (window._VBEN_ADMIN_PRO_APP_CONF_) {
      var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement('script');
        hm.src =
          'https://hm.baidu.com/hm.js?24bb3eb91dfe4ebfcbcee6952a107cb6';
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
      })();
    }
  </script>
  <script src="https://minio.tianai.cloud/public/static/captcha/js/load.min.js"></script>
  <script src="/captcha/js/tac.min.js"></script>

<style data-app-loading="inject-css">
  #__app-loading__.hidden {
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.33, 1, 0.68, 1);
  }
  
  .loading {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #ffffff;
  }
  
  .loading.hidden {
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.33, 1, 0.68, 1);
  }
  
  .dark .loading {
    background: #000000;
  }
  
  /* 大气科技感背景 */
  .bg-decoration {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: -1;
  }
  
  /* 动态网格背景 */
  .grid-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      linear-gradient(rgba(0, 102, 204, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 102, 204, 0.03) 1px, transparent 1px);
    background-size: 40px 40px;
    opacity: 0.7;
    z-index: -1;
    animation: grid-pulse 8s infinite alternate;
  }
  
  @keyframes grid-pulse {
    0% { background-size: 40px 40px; opacity: 0.5; }
    100% { background-size: 42px 42px; opacity: 0.7; }
  }
  
  /* 大型光晕效果 */
  .bg-blur {
    position: absolute;
    border-radius: 50%;
    filter: blur(120px);
    opacity: 0.6;
    animation: float 20s ease-in-out infinite;
  }
  
  .bg-blur-1 {
    top: 10%;
    right: 10%;
    width: 800px;
    height: 800px;
    background: radial-gradient(circle, rgba(0, 122, 255, 0.15), rgba(64, 200, 224, 0.05));
    animation-delay: 0s;
  }
  
  .bg-blur-2 {
    bottom: 5%;
    left: 10%;
    width: 700px;
    height: 700px;
    background: radial-gradient(circle, rgba(88, 86, 214, 0.12), rgba(10, 132, 255, 0.04));
    animation-delay: -5s;
  }
  
  .bg-blur-3 {
    top: 40%;
    left: 25%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(90, 200, 250, 0.08), rgba(0, 122, 255, 0.03));
    animation-delay: -10s;
  }
  
  @keyframes float {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(-20px, 20px); }
    50% { transform: translate(20px, 40px); }
    75% { transform: translate(40px, -20px); }
  }
  
  /* 粒子背景 */
  .particles-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }
  
  .particle-bg {
    position: absolute;
    background-color: rgba(0, 122, 255, 0.3);
    border-radius: 50%;
    animation: particle-float 15s infinite linear;
  }
  
  @keyframes particle-float {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-100vh) rotate(360deg); }
  }
  
  /* 生成30个随机粒子 */
  .particles-background::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 0%, rgba(255, 255, 255, 0.8) 100%);
    opacity: 0.1;
    z-index: 1;
  }
  
  /* 主加载动画容器 */
  .loading-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 60px;
    position: relative;
    width: 200px;
    height: 200px;
    margin-left: auto;
    margin-right: auto;
  }
  
  /* 高级加载动画 */
  .loader-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  /* 外环 */
  .loader-ring-outer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 4px solid transparent;
    border-top: 4px solid #007AFF;
    border-right: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 4px solid #007AFF;
    animation: spin-outer 3s linear infinite;
  }
  
  /* 中环 */
  .loader-ring-middle {
    position: absolute;
    top: 20%;
    left: 20%;
    width: 60%;
    height: 60%;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top: 3px solid rgba(0, 122, 255, 0.8);
    border-right: 3px solid rgba(0, 122, 255, 0.6);
    border-bottom: 3px solid transparent;
    border-left: 3px solid rgba(0, 122, 255, 0.8);
    animation: spin-middle 2.5s linear infinite;
  }
  
  /* 内环 */
  .loader-ring-inner {
    position: absolute;
    top: 35%;
    left: 35%;
    width: 30%;
    height: 30%;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top: 2px solid rgba(0, 122, 255, 0.7);
    border-right: 2px solid transparent;
    border-bottom: 2px solid transparent;
    border-left: 2px solid rgba(0, 122, 255, 0.7);
    animation: spin-inner 2s linear infinite;
  }
  
  /* 中心圆点 */
  .loader-core {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 15%;
    height: 15%;
    transform: translate(-50%, -50%);
    background: radial-gradient(circle, rgba(0, 122, 255, 1), rgba(64, 200, 224, 0.8));
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(0, 122, 255, 0.8);
    animation: pulse 2s ease-in-out infinite;
  }
  
  /* 光线效果 */
  .loader-rays {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  .ray {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 40%;
    margin-left: -1px;
    background: linear-gradient(to top, rgba(0, 122, 255, 0), rgba(0, 122, 255, 0.5));
    transform-origin: center top;
  }
  
  /* 12个光线，均匀分布 */
  .ray:nth-child(1) { transform: rotate(0deg) translateY(-50%); }
  .ray:nth-child(2) { transform: rotate(30deg) translateY(-50%); }
  .ray:nth-child(3) { transform: rotate(60deg) translateY(-50%); }
  .ray:nth-child(4) { transform: rotate(90deg) translateY(-50%); }
  .ray:nth-child(5) { transform: rotate(120deg) translateY(-50%); }
  .ray:nth-child(6) { transform: rotate(150deg) translateY(-50%); }
  .ray:nth-child(7) { transform: rotate(180deg) translateY(-50%); }
  .ray:nth-child(8) { transform: rotate(210deg) translateY(-50%); }
  .ray:nth-child(9) { transform: rotate(240deg) translateY(-50%); }
  .ray:nth-child(10) { transform: rotate(270deg) translateY(-50%); }
  .ray:nth-child(11) { transform: rotate(300deg) translateY(-50%); }
  .ray:nth-child(12) { transform: rotate(330deg) translateY(-50%); }
  
  /* 动画定义 */
  @keyframes spin-outer {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes spin-middle {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
  }
  
  @keyframes spin-inner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.8; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; box-shadow: 0 0 20px rgba(0, 122, 255, 0.9); }
  }
  
  /* 标题容器 */
  .title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
  }
  
  /* 标题样式 */
  .title {
    font-size: 48px;
    font-weight: 700;
    color: #1d1d1f;
    margin: 0;
    letter-spacing: -0.5px;
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background: linear-gradient(90deg, #007AFF, #40C8E0, #5856D6, #007AFF);
    background-size: 300% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: title-shimmer 6s infinite linear;
    text-shadow: 0 0 30px rgba(0, 122, 255, 0.2);
  }
  
  @keyframes title-shimmer {
    0% { background-position: 0% center; }
    100% { background-position: 300% center; }
  }
  
  .subtitle {
    font-size: 18px;
    color: #86868b;
    margin-top: 16px;
    font-weight: 500;
    letter-spacing: -0.1px;
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    position: relative;
    overflow: hidden;
    padding-bottom: 8px;
  }
  
  .subtitle::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.8), transparent);
    animation: loading-line 2s infinite;
  }
  
  @keyframes loading-line {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  /* 深色模式适配 */
  .dark .title {
    background: linear-gradient(90deg, #0A84FF, #64D2FF, #5E5CE6, #0A84FF);
    background-size: 300% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .dark .subtitle {
    color: #a1a1a6;
  }
  
  .dark .subtitle::after {
    background: linear-gradient(90deg, transparent, rgba(10, 132, 255, 0.8), transparent);
  }
  
  .dark .grid-background {
    background-image: 
      linear-gradient(rgba(10, 132, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(10, 132, 255, 0.05) 1px, transparent 1px);
  }
  
  /* 底部装饰 */
  .bottom-decoration {
    position: absolute;
    bottom: 32px;
    font-size: 14px;
    color: #86868b;
    font-weight: 400;
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    opacity: 0.8;
    letter-spacing: 0.5px;
  }
  
  .dark .bottom-decoration {
    color: #a1a1a6;
  }
  
  /* 生成随机粒子的样式 */
  .particle-bg:nth-child(1) { width: 6px; height: 6px; left: 10%; top: 100%; animation-duration: 25s; animation-delay: 0s; opacity: 0.5; }
  .particle-bg:nth-child(2) { width: 8px; height: 8px; left: 20%; top: 100%; animation-duration: 35s; animation-delay: 2s; opacity: 0.3; }
  .particle-bg:nth-child(3) { width: 5px; height: 5px; left: 30%; top: 100%; animation-duration: 30s; animation-delay: 4s; opacity: 0.4; }
  .particle-bg:nth-child(4) { width: 7px; height: 7px; left: 40%; top: 100%; animation-duration: 28s; animation-delay: 6s; opacity: 0.2; }
  .particle-bg:nth-child(5) { width: 9px; height: 9px; left: 50%; top: 100%; animation-duration: 33s; animation-delay: 8s; opacity: 0.5; }
  .particle-bg:nth-child(6) { width: 4px; height: 4px; left: 60%; top: 100%; animation-duration: 26s; animation-delay: 10s; opacity: 0.3; }
  .particle-bg:nth-child(7) { width: 8px; height: 8px; left: 70%; top: 100%; animation-duration: 32s; animation-delay: 12s; opacity: 0.4; }
  .particle-bg:nth-child(8) { width: 6px; height: 6px; left: 80%; top: 100%; animation-duration: 29s; animation-delay: 14s; opacity: 0.2; }
  .particle-bg:nth-child(9) { width: 5px; height: 5px; left: 90%; top: 100%; animation-duration: 31s; animation-delay: 16s; opacity: 0.5; }
  .particle-bg:nth-child(10) { width: 7px; height: 7px; left: 15%; top: 100%; animation-duration: 27s; animation-delay: 18s; opacity: 0.3; }
  .particle-bg:nth-child(11) { width: 9px; height: 9px; left: 25%; top: 100%; animation-duration: 34s; animation-delay: 20s; opacity: 0.4; }
  .particle-bg:nth-child(12) { width: 4px; height: 4px; left: 35%; top: 100%; animation-duration: 30s; animation-delay: 22s; opacity: 0.2; }
  .particle-bg:nth-child(13) { width: 8px; height: 8px; left: 45%; top: 100%; animation-duration: 28s; animation-delay: 24s; opacity: 0.5; }
  .particle-bg:nth-child(14) { width: 6px; height: 6px; left: 55%; top: 100%; animation-duration: 32s; animation-delay: 26s; opacity: 0.3; }
  .particle-bg:nth-child(15) { width: 5px; height: 5px; left: 65%; top: 100%; animation-duration: 29s; animation-delay: 28s; opacity: 0.4; }
</style>
</head>

<body>
  <div id="root">
    <div id="__app-loading__" class="loading">
      <div class="bg-decoration">
        <div class="grid-background"></div>
        <div class="bg-blur bg-blur-1"></div>
        <div class="bg-blur bg-blur-2"></div>
        <div class="bg-blur bg-blur-3"></div>
        <div class="particles-background">
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
          <div class="particle-bg"></div>
        </div>
      </div>
      
      <div class="loading-wrap">
        <div class="loader-container">
          <div class="loader-ring-outer"></div>
          <div class="loader-ring-middle"></div>
          <div class="loader-ring-inner"></div>
          <div class="loader-core"></div>
          <div class="loader-rays">
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
            <div class="ray"></div>
          </div>
        </div>
      </div>
      
      <div class="title-container">
        <h1 class="title"><%= VITE_APP_TITLE %></h1>
        <p class="subtitle">正在初始化系统...</p>
      </div>
      
      <div class="bottom-decoration">
        © <%= new Date().getFullYear() %> LynkzHub · 未来科技
      </div>
    </div>

    <script type="module" src="/src/main.tsx"></script>
  </div>
</body>

</html>
