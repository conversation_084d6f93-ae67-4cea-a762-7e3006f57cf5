.prose {
  //     padding: 0;
  min-height: 200px;
  padding-left: 0;
  padding-right: 0;
  overflow-y: auto;
  height: calc(100vh - 230px);
  // font-family: "Inter", sans-serif;
}
.prose-content {
  font-family:'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
    tab-size: 4;
}
.prose-content :where(blockquote):not(.not-prose blockquote) {
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left: 0.25rem solid #e5e7eb;
  padding-left: 1em;
  margin: 1.6em 0;
  quotes: "'" "'" "'" "'";
}
.prose-content :where(h1):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0px;
  margin-bottom: 0.888889em;
  line-height: 1.11111;
}

.prose-content :where(hr):not(:where([class~="not-prose"] *)) {
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
  border-color: #e5e7eb;
}

.prose-content :where(ol > li):not(:where([class~="not-prose"] *))::marker {
  color: var(--semi-color-primary);
}
.prose-content h1,
h2,
h3,
h4,
h5,
h6 {
  // color: var(--semi-color-primary);
  // font-weight: 600;
}
.prose-content h1 {
  font-size: 1.5rem;
}
.prose :where(blockquote):not(.not-prose blockquote) {
  font-weight: 500;
  font-style: italic;
  color: #111827;
  border-left: 0.25rem solid #e5e7eb;
  padding-left: 1em;
  margin: 1.6em 0;
  quotes: "'" "'" "'" "'";
}
.prose :where(h1):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0px;
  margin-bottom: 0.888889em;
  line-height: 1.11111;
}

.prose :where(hr):not(:where([class~="not-prose"] *)) {
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
  border-color: #e5e7eb;
}

.prose :where(ol > li):not(:where([class~="not-prose"] *))::marker {
  color: var(--semi-color-primary);
}
.prose h1,
h2,
h3,
h4,
h5,
h6 {
  // color: var(--semi-color-primary);
  // font-weight: 600;
}
.prose h1 {
  font-size: 1.5rem;
}
