import { Layout, Toast } from "@douyinfe/semi-ui";
import NotFound from "./pages/404";
import ErrorBoundary from "./pages/ErrorBoundary";
import { useEffect, useState } from "react";
import { LocalForageService as storage } from "./utils/storage";
import {
  createRouter,
  parseSearchWith,
  RouterProvider,
  stringifySearchWith,
} from "@tanstack/react-router";
import routeTree from "./router";
import { initVChartSemiTheme } from "@visactor/vchart-semi-theme";
import { GlobalLoading } from "./components/GlobalLoading";
import { config } from "md-editor-rt";
initVChartSemiTheme();

const router = createRouter({
  routeTree,
  defaultPreload: "intent",
  defaultStaleTime: 5000,
  scrollRestoration: true,
  parseSearch: parseSearchWith((value) => JSON.parse(decodeFromBinary(value))),
  stringifySearch: stringifySearchWith((value) =>
    encodeToBinary(JSON.stringify(value))
  ),
});
function decodeFromBinary(str: string): string {
  return decodeURIComponent(
    Array.prototype.map
      .call(atob(str), function (c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      })
      .join("")
  );
}

function encodeToBinary(str: string): string {
  return btoa(
    encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (match, p1) {
      return String.fromCharCode(parseInt(p1, 16));
    })
  );
}

function App() {
  const [runEnvType, setRunEnvType] = useState<string>("web");
  const { Content } = Layout;

  useEffect(() => {
    // 在应用入口文件中配置（如 index.js 或 main.js）
    storage.getItem<"light" | "dark">("theme_mode").then((mode) => {
      if (!mode) {
        document.body.setAttribute("theme-mode", "light");
      } else {
        document.body.setAttribute("theme-mode", mode);
      }
    });
  }, []);

  return (
    <Content
      className={`flex-1 overflow-auto h-full semi-light-scrollbar`}

    >
      <AuthRouter />
      <GlobalLoading />
    </Content>
  );
}

const AuthRouter = () => {
  // const auth = useAuth();
  return (
    <RouterProvider
      router={router}
      defaultErrorComponent={(error) => {
        console.error(error);
        return <ErrorBoundary error={error.error} />;
      }}
      defaultOnCatch={(error) => {
        console.log(error);
      }}
      defaultNotFoundComponent={NotFound}
    // context={{ auth }}
    />
  );
};

export default App;
