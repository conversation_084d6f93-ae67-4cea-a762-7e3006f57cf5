// import { create } from 'zustand';
// import { immer } from 'zustand/middleware/immer';
// import { persist } from 'zustand/middleware';
// import { fetchStream } from '@/api/platform/chat';
// import snowflake from '@/utils/Snowflake';

// interface Message {
//     id: string;
//     role: string;
//     content: string;
//     createAt?: number;
// }

// interface ChatState {
//     conversationId: string | null;
//     messages: Map<string, StreamEvnetVO>;
//     loading: boolean;
//     responding: boolean;

//     setConversationId: (id: string) => void;
//     appendMessage: (message: StreamEvnetVO) => void;
//     updateLastMessage: (id: string, newContent: string) => void;
//     deleteMessageById: (id: string) => void;
//     clearMessages: () => void;
//     startStream: (prompt: string, botId: string) => void;
//     stopStream: () => void;
//     // setIsStreaming: (isStreaming: boolean) => void;
// }
// const conversationId = snowflake.nextId();
// export const useChatStore = create<ChatState>()(
//     persist(
//         immer((set, get) => ({
//             conversationId: conversationId,
//             messages: new Map<string, StreamEvnetVO>(),
//             responding: false,
//             setConversationId(id) {
//                 set((state) => {
//                     state.conversationId = id;
//                 });
//             },
//             appendMessage(stream) {
//                 set((state) => {
//                     messages: new Map(state.messages).set(stream.message.id, stream),
//                 });
//             },


//             // updateLastMessage(id, newContent) {
//             //     set((state) => {
//             //         const index = state.messages.findIndex((msg) => msg.id === id);
//             //         if (index > -1) {
//             //             const updatedMessage = {
//             //                 ...state.messages[index],
//             //                 content: state.messages[index].content + newContent,
//             //             };
//             //             state.messages = [
//             //                 ...state.messages.slice(0, index),
//             //                 updatedMessage,
//             //                 ...state.messages.slice(index + 1),
//             //             ];
//             //         }
//             //     });
//             // },

//             // deleteMessageById(id) {
//             //     set((state) => {
//             //         state.messages = state.messages.filter((msg) => msg.id !== id);
//             //     });
//             // },

//             // clearMessages() {
//             //     set((state) => {
//             //         state.messages = [];
//             //     });
//             // },

//             // startStream(prompt, botId) {
//             //     const state = get();
//             //     // const newConversationId = localStorage.getItem('conversationId') || snowflake.nextId();
//             //     // localStorage.setItem('conversationId', newConversationId);

//             //     const abortCtrl = new AbortController();
//             //     const stream = fetchStream({
//             //         prompt,
//             //         conversationId: newConversationId,
//             //         botId,
//             //     }, abortCtrl.signal);

//             //     let localMessages = [...state.messages];

//             //     // 🔒 开启流式请求锁
//             //     useChatStore.setState({ conversationId: newConversationId, loading: true, isStreaming: true });

//             //     (async () => {
//             //         try {
//             //             for await (const event of stream) {
//             //                 if (!event.data) continue;

//             //                 let parsedData;
//             //                 try {
//             //                     parsedData = JSON.parse(event.data);
//             //                 } catch (parseError) {
//             //                     console.error('❌ Failed to parse message:', event.data, parseError);
//             //                     continue;
//             //                 }

//             //                 const respMessage = parsedData?.message || {};
//             //                 const metadata = parsedData?.metadata || {};

//             //                 if (!respMessage.id) {
//             //                     console.warn("Received message without ID", respMessage);
//             //                     continue;
//             //                 }

//             //                 const lastMessage = localMessages[localMessages.length - 1];

//             //                 if (!lastMessage || lastMessage.id !== respMessage.id) {
//             //                     const newMsg = {
//             //                         ...respMessage,
//             //                         ...metadata,
//             //                         role: 'assistant',
//             //                         createAt: Date.now(),
//             //                         id: respMessage.id,
//             //                     };
//             //                     localMessages = [...localMessages, newMsg];
//             //                 } else {
//             //                     const updatedMessage = {
//             //                         ...lastMessage,
//             //                         content: lastMessage.content + (respMessage.content || ''),
//             //                     };
//             //                     localMessages = [...localMessages.slice(0, -1), updatedMessage];
//             //                 }

//             //                 useChatStore.setState((draft) => {
//             //                     draft.messages = [...localMessages];
//             //                 });

//             //                 // ✅ 在 status === 'completed' 时立即解锁
//             //                 if (respMessage.status === 'completed') {
//             //                     useChatStore.setState({ loading: false, isStreaming: false });
//             //                     abortCtrl.abort();
//             //                     break;
//             //                 }
//             //             }
//             //         } catch (err: any) {
//             //             if (err.name !== 'AbortError') {
//             //                 console.error('Stream error:', err);
//             //             }
//             //         } finally {
//             //             // ✅ 最后再次确保解锁（防止异常退出）
//             //             // useChatStore.setState({ loading: false, isStreaming: false });
//             //         }
//             //     })();
//             // },

//             // stopStream() {
//             //     useChatStore.setState({ loading: false, isStreaming: false });
//             // },

//             // setIsStreaming(isStreaming) {
//             //     set((state) => {
//             //         state.isStreaming = isStreaming;
//             //     });
//             // },
//         })),
//         {
//             name: 'agent-chat-storage',
//         }
//     )
// );



// const stopStream = () => {
//     useChatStore.setState({ loading: false, isStreaming: false });
// }