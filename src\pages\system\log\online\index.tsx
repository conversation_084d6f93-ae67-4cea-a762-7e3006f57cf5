import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import { IconDelete, IconEdit, IconMore, IconSync } from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Pagination,
  Select,
  Table,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState, useCallback } from "react";
import { useBoolean } from "@/hooks";
import { debounce } from "lodash-es";

// import {
//   dictDataFormTitle,
//   getBgClass,
//   getDictListClass,
// } from "./constants/dictonary";
import classNames from "classnames";
import { useTable } from "@/hooks/useTables";
import { getOssConfigList } from "@/api/system/oss-config";
import { getFileList } from "@/api/system/files";
import { getClientList } from "@/api/system/client";
import { getOnlineList } from "@/api/log/online";
import dayjs from "dayjs";
export default function OnlineUser() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_device_type",
    "sys_grant_type",
  ]);
  const [ossEnableOptions, setOssEnableOptions] = useState([]);
  useEffect(() => {
    setTimeout(() => {
      loadDictionary();
    }, 0);
  }, []);

  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getOnlineList,
    params: {},
    columns: [
      {
        title: "会话编号",
        dataIndex: "tokenId",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "登录名称",
        dataIndex: "userName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "客户端",
        dataIndex: "clientKey",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "设备类型",
        dataIndex: "deviceType",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_device_type"
              dictValue={text}
              dictionaryData={dictionaryData.sys_device_type || []}
            />
          );
        },
      },
      {
        title: "授权设备类型",
        dataIndex: "deviceType",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_device_type"
              dictValue={text}
              dictionaryData={dictionaryData.sys_device_type || []}
            />
          );
        },
      },
      {
        dataIndex: "deptName",
        title: "所属部门",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        dataIndex: "ipaddr",
        title: "主机",
      },
      {
        dataIndex: "loginLocation",
        title: "登录地点",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        dataIndex: "os",
        title: "操作系统",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        dataIndex: "browser",
        title: "浏览器",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        dataIndex: "loginTime",
        title: "登录时间",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? dayjs(text).format("yyyy-MM-dd HH:mm")  : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button theme="borderless">下线</Button>
            </div>
          );
        },
      },
    ],
  });
  // 防抖提交（300ms）
  const debouncedSubmit = useCallback(
    debounce((values: any) => {
      setSearchParams(values);
      refresh();
    }, 300),
    []
  );

  return (
    <div className=" w-full h-full py-2 px-2">
      <div className="w-full h-full flex items-stretch gap-2 sm:overflow-auto flex-col bg-semi-color-white rounded-md py-2 px-2">
        <div className="font-semibold text-xl px-2 my-2">在线用户</div>
        <div className="flex  flex-row items-center justify-between gap-2 px-2">
          <Form<typeof searchParams>
            initValues={searchParams}
            autoScrollToError={{ behavior: "smooth", block: "center" }}
            showValidateIcon
            onValueChange={(values) => {
              debouncedSubmit(values);
            }}
            onReset={() => {
              resetSearchParams();
              refresh();
            }}
            onSubmit={(values) => {
              setSearchParams(values);
              refresh();
            }}
          >
            <div className="flex gap-2">
              <Form.Input
                className="min-w-[250px]"
                showClear
                trigger={["change", "blur"]}
                noLabel
                field="fileName"
                placeholder={"请输入文件名"}
              />
            </div>
          </Form>
          <div className="flex flex-row items-center gap-2">
            <Button
              type="primary"
              theme="solid"
              onClick={() => {
                // handleAddForm();
              }}
            >
              新增字典
            </Button>
            <Button theme="solid" type="danger">
              批量删除
            </Button>
            <Tooltip content="刷新列表">
              <Button
                type="tertiary"
                icon={<IconSync />}
                onClick={() => refresh()}
              />
            </Tooltip>
          </div>
        </div>
        <div className="flex-1 sm:overflow-hidden">
          <Table
            rowSelection={rowSelection as any}
            columns={columns}
            className="h-full"
            dataSource={dataSource}
            loading={loading}
            size="small"
            scroll={{ x: 962, y: "calc(100vh - 300px)" }}
            pagination={false}
            rowKey="dictCode"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
          />
        </div>
        {pagination.total > 0 && (
          <div className="flex justify-end">
            <Pagination {...pagination}></Pagination>
          </div>
        )}
      </div>
    </div>
  );
}
