:root {
  --default-bg-color: #ffffff;
  --default-text-color: #000;
  --solid-border: 1px solid #e4e7eb;
  --primary-color: 42 137 255;
  --slider-move-btn-color: #ffffff;
  --slider-captcha-shadow: 0 0 11px 0 #999999;
}
#tianai-captcha-parent {
  box-shadow: var(--slider-captcha-shadow);
  width: 318px;
  height: 318px;
  overflow: hidden;
  position: relative;
  z-index: 997;
  box-sizing: border-box;
  border-radius: 5px;
  padding: 8px;
}
#tianai-captcha-parent #tianai-captcha-box {
  height: 260px;
  width: 100%;
  position: relative;
  overflow: hidden;
}
#tianai-captcha-parent #tianai-captcha-box .loading {
  width: 50px;
  height: 50px;
  text-align: center;
  display: block;
  z-index: 998;
  position: absolute;
  top: 105px;
  left: 126px;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  background-image: url(../../tac/images/loading.gif);
  background-size: cover;
}
#tianai-captcha-parent #tianai-captcha-box #tianai-captcha {
  transform-style: preserve-3d;
  will-change: transform;
  transition-duration: 0.45s;
  transform: translateX(-300px);
}
#tianai-captcha-parent #tianai-captcha-bg-img {
  background-color: var(--default-bg-color);
  background-position: top;
  background-size: cover;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: absolute;
  border-radius: 6px;
}
#tianai-captcha-parent .slider-bottom {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 19px;
  width: 100%;
}
#tianai-captcha-parent .slider-bottom .close-btn {
  width: 20px;
  height: 20px;
  background-image: url(data:image/png;base64,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);
  background-repeat: no-repeat;
  background-position: 0 -14px;
  float: right;
  margin-right: 2px;
  cursor: pointer;
}
#tianai-captcha-parent .slider-bottom .refresh-btn {
  width: 20px;
  height: 20px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAEJCAYAAACHYUcIAAAL40lEQVR4nO1dfYxU1RU/585by2LCWJpGqo1NbfwAgYjgPyYalSIFG6tbZd64KR8pojapJqCyJWH2ztpgEUuiNrEqLSWWZRZbkVYE/MA1TUwaIW0s4G79SIVgP2zSrn8IkZl7mjOc+3qZfTPvzc7bFeH9kidv733n/t79Ovf3zntzhRQpUqRIkSISGHbBk08+2XbkyJGbEfFmAJgFAOdJ1ocAsJeInj///POfv/POO48328TDCIvF4i2VSmUdAHwjwva9TCZzf3d397YREWqtFQD8xBhzvyQdUEptAICXAOCQpF0AADcYY5YCwGWcoJTim+vSWps4hJ5zXiVDxE+JaLlS6omQQg7yobV+zBhzNyKuZxul+F7hgdg1lGZ8jskQcZ7Wek8cY6319US0k4jOymQyHXGaV/EAkT4DrplLprWeEEIywTnfwzZ8zmVwWZGEPBplgHCfPWEzuru7HzXGvLFmzZov2zQ+5zTOCwo4YXOAy5CyGhPK0GfDDbbPuBZENJsHxrFjx15jIj74nNM4z9aUbWRwgS2rIaHMM5DRaJvq43Hjxl0nd14ltWScxnl8jVOOtZ0FEVDOpD7kXrpq1aqPXFKXjPNqirW258UhHFMocVcgkzqA22dSywNun9bcpLX9MOrmmXCvnN9gE3lA1PZZbZ/WTBlruxcioNgR8yXsrsS9VQcNIr7q9pnbp5xnBw3biKsDW1bDGrLXZ0fMd87uymYUi8V7lVJXuQOEzzmN82ya2HBLvCdlNcSYuzZ3tXg4hvO21yrrvJmMVwytdSzn7a4WXez1ZXn6GReotW64PBFRsDzFIYPPdAF2MZoSI0WKUx+h02LZsmVtQ0NDwbQgouq0QMRgWmSz2eefeuqp1pW37/u3AMA6Imo48RGRHf79pVJp5Mp7cHAwUN6IyMvQBqXUS9lsturahoaGLjDG3EBES4koUN6XXHJJ88rbIeMVY/mll17aUHkPDAzcTURV5T04OAhNKW9uRiJ6jsmUUvO2bNkSa3nK5/PXG2N2AsBZiNgRp3kVDxDuMzjRjMtdss7OzmHK203ja9lG/lwnZTUm5NHIA4T7jJvRZvi+/2ilUnljyZIlgWDic07jPJvGNmzLZXBZkYRWLfMAsX0mtZjNA+Po0aOvMREffC6DZbatKduwLTSrvHk02sTNmzd/3N7efp3ceZXUknEa5/E1QSH/t41W3nZS26FvsXHjxo9cUpeM89xrra0tK6qGYwol7qo6qV1it8/ECRxw+9S91trasqJqWFXL7EFsIg+I2j6r7VN3eji28ZU3uyurvGVAvOr2mdunnGcHDduwLcRV3uz12RHznbO7shmlUuneTCZzlTtA+JzTOM+miYvjlniPy4oiHHPXluH/7N+/f2D69OlnE9E1RJSfNm3af2699dZ9/f39FGbEzThp0qQfENEzTMYrRqlUejzOTY758vTZLcAuRlNipEhx6iONeacx72Rj3haFQoH4CCFMLuYdF4nEvGvR09ODfITlJRLzrkW9JnXQesy7SXwOY95NorWY9wjQWsy7GSQW846LNOZdizTmnca8U5yBSGPeacw72Zi3RS6XIz5q0xONecdFIjHvWvT19SEfYXmJxLxrUa9Jg0KSiHk3g89nzLsZtBzzbhYtx7ybQWIx77hIY96hNYQ05p3GvFOcUcD+/v55APALAPjKWFRcjSWZJRwzMvisgnunDOGnAMAvl78EABPl/NNWCb0Gefxw8pjzN5+38+uGVggb1fCZkLSnWyGLIhwVNCL8Xkja0lZvohHhwwBwDwB8UQ4+fzCivH8CwLdZjdS7AOsJpRGASa4GgL8AwI0AsN2KtLg1bAafAMB3hIyxg3/QEGafBGGFJSoAvF6TvgYAfjNSQm4uXlX+W5PO3fF9APhdiA3nLXJqHZuQa9ABALsA4Lvyt8WPAGBTA1tualZ+/26G8IcAYKX/Hvnb4uP6ZgHeZ7lrbzSK8Ak56qWxvL8pBil/939C0TeYFnvkjUslJC8jwffra6ZDFDbVq+G70m9hZOD0K1+XBYAXAeCrMQjvCiO0I7Kutwi5jsl+DwDjI2yO1hKWnTuPA7clLufgVJh3cVFLeI8zIuPCHbk3yUCqiyR9aSxwDf8+1oS8xv1jLElTpDi1gfv27bM3eIEsqPMB4FwAOCJO+S1x0E/PnDkzyr9Gwrq2uQDwZwA4Jqs6k+cA4Gvyfpd/bfnzJJqOny2+DgC9/GkwAOx08v4lC+fZAPBHALguKcKVAPBrh2yOLLwvy9PxFQBwDQCsSIpwvggdxjf5m2QA+KU8zCwU5ZUYPBkghx1CfgexSta4XQ5RJeLxLhaUPA9YefCKOPM1oje/JQHAK2TUtgwlQ3+RQ9gpNeHm3C3pC+W6luHJU9KfpPl2yWB52Sl4rhDOTKqG78uc46nxUymYn+tnAMB60Slc678lRQhSsxky57bJJxDbRYXNqBk8KVKc5gh9bwEnPlW6kYhuM8ZcDAB8eIj4gfyihOfsLq11udnmGUaotb6JiB4koukNDREPIeIdWuu6747DcNLDTKFQWGmM2c5kiPiuUmqF53mz2tvbJ44fP/4cz/P4Cek+AHibiPg91G6t9SPOzUZ+rRDUsLu7ey0RPYCIZSLqKhaL6xGx7ts1AFhujPkxAHxBKfUQRziMMWvrffh6EmFPT8/V5XL5dUSsyJd7r8RpnmKxONcY8wIRBetkFKH9KJkFEnLN4pLBiVbZjYi/jXs9w5Nv0y5rxsiC+8wYk2vGZsShLyFb26zdiDXKhAkTHo96vE4xJsBCofBJLZFS6mGttR6NG/DkXUQARBw1MggZpTuLxeLKZgooFAr3ImIXEQ1OnTp19oIFC+rF56o4aR4i4pxisTgnLhlfi4iPENG5nuetjiILCLkZ+YsD9omVSmWH1vo+IqrrEzmvUCisMMa8yDaIuK5QKPwhzk0irxK2GbXWXcaYNeLU31FKsY/tF7HMuBAArjXG3AUAFzG3UmqV1jr2+6jQBViWnWkNDRHfQsTVWuuwAHt8QpAm6+npmW+MWSDy4mLJ+isiDiLis93d3TuaIUqR4vODut7E931+B3ibnRbiUT7gH1URUe/kyZOTUd6+73NkPlJ5y08i7ujr6xu58vZ9fyURbRcyfiexIpPJzMpkMhOz2ew5bW1tlyul7kPEtyUetzuXywXKO5/Px1feuVxurby7Lyulunp7exsq74GBgeVEVFXeiPgQIlaVd70PX08i9H3/aiLiN5yVTCYzr7e3N5YYvv322+dWKpUX3HU1irD6UTIRVZW31Cy28u7t7W1aeTe8myhwn9WK4cgaJkkWByNW3pMmTUqV96kBzOVyw5Q3q7hSqTQ2ypvjp6NFBrWjFBF3lkqlppR3Lpfjb6W6WFx1dHQ0p7yJaE4+n4+tvOXaRyRQH195czPKr+w8Itrh+36k8s7n81XlLa3EHyXHU968SvT19VWbMZfLdUlEHxHxHfaxbW1t/Z7nVZV3uVy+8Pjx49ci4l1EdJF8UbKqr69v5MqbF2BZdiKVNzdjqVRKRnnn8/n5RBSqvAHg2VKplCrvFKcp0o040o04RmcjDq31eK31r0LSk9+IQ2s9hYjeNMYsctKu3bp1a/XrrkQ34tBaL2YyIppi0yTktWf//v0LYQQbcXhhG3HYTTeMGT7wlFKHjTHc90t4v7Ea21HZiKPax4g42Uk7jTbiMMbMltODTnJrG3E4u7UsRsSTlDkiTpV/NzrJyWzE0dPTswkRr0TEoDbFYnE1T/IpU6ZUP61PfCMOrfVBJlVKBd8Cs0exojfdiKMW6UYc6UYcKc5ApBtxpBtxjM5GHMuWLRvv+/4w5T0qG3F0dnZOGRoaYjEcKG/f9wPlnehGHL7vLy6Xy28CQKC8OeRFRHu2bdsWKO9mNuLwwjbisJtu8AIbgsPysU+gvNlWVPqobMRh+zhQ3qfXRhzyv/ThfwOt2vJGHHa3FqXUYvn9mYthyjuxjTi2bNmyyfO8K93niFKptJoneUdHR6C8E92IY/PmzQez2SzL/UB5s0exyjvdiCO0hpBuxJGUxACA/wF3j/pSC0uOLAAAAABJRU5ErkJggg==);
  background-position: 0 -167px;
  background-repeat: no-repeat;
  float: right;
  margin-right: 10px;
  cursor: pointer;
}
#tianai-captcha-parent .slider-bottom .logo-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
#tianai-captcha-parent .slider-bottom .logo {
  height: 24px;
  float: left;
}
#tianai-captcha-parent .slider-bottom .logo-text {
  color: #3863f6;
  margin-left: 3px;
}
#tianai-captcha-parent .slider-move-shadow {
  animation: myanimation 2s infinite;
  height: 100%;
  width: 5px;
  background-color: #fff;
  position: absolute;
  top: 0;
  left: 0;
  filter: opacity(0.5);
  box-shadow: 1px 1px 1px #fff;
  border-radius: 50%;
}
#tianai-captcha-parent #tianai-captcha-slider-move-track-mask {
  border-width: 1px;
  border-style: solid;
  border-color: rgb(var(--primary-color));
  width: 0;
  height: 40px;
  background-color: #a9ffe5;
  opacity: 0.5;
  position: absolute;
  top: -1px;
  left: -1px;
  border-radius: 5px;
}
#tianai-captcha {
  text-align: left;
  box-sizing: content-box;
  width: 300px;
  height: 260px;
  z-index: 999;
}
#tianai-captcha .slider-bottom .logo {
  height: 25px;
}
#tianai-captcha .slider-bottom {
  height: 19px;
  width: 100%;
}
#tianai-captcha .content .tianai-captcha-tips {
  height: 25px;
  width: 100%;
  position: absolute;
  bottom: -25px;
  left: 0;
  z-index: 999;
  font-size: 15px;
  line-height: 25px;
  color: #fff;
  text-align: center;
  transition: bottom 0.3s ease-in-out;
}
#tianai-captcha .content .tianai-captcha-tips.tianai-captcha-tips-error {
  background-color: #ff5d39;
}
#tianai-captcha .content .tianai-captcha-tips.tianai-captcha-tips-success {
  background-color: #39c522;
}
#tianai-captcha .content .tianai-captcha-tips.tianai-captcha-tips-on {
  bottom: 0;
}
#tianai-captcha .content #tianai-captcha-loading {
  z-index: 9999;
  background-color: #f5f5f5;
  text-align: center;
  height: 100%;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}
#tianai-captcha .content #tianai-captcha-loading img {
  display: block;
  width: 45px;
  height: 45px;
}
#tianai-captcha #tianai-captcha-slider-bg-canvas {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border-radius: 5px;
}
@keyframes myanimation {
  from {
    left: 0;
  }
  to {
    left: 289px;
  }
}
#tianai-captcha.tianai-captcha-slider {
  z-index: 999;
  position: absolute;
  left: 0;
  top: 0;
  user-select: none;
}
#tianai-captcha.tianai-captcha-slider .content {
  width: 100%;
  height: 180px;
  position: relative;
}
#tianai-captcha.tianai-captcha-slider .bg-img-div {
  width: 100%;
  height: 100%;
  position: absolute;
  transform: translate(0px, 0px);
}
#tianai-captcha.tianai-captcha-slider .bg-img-div img {
  height: 100%;
  width: 100%;
  border-radius: 5px;
}
#tianai-captcha.tianai-captcha-slider .slider-img-div {
  height: 100%;
  position: absolute;
  transform: translate(0px, 0px);
}
#tianai-captcha.tianai-captcha-slider .slider-img-div #tianai-captcha-slider-move-img {
  height: 100%;
}
#tianai-captcha.tianai-captcha-slider .slider-move {
  height: 34px;
  width: 100%;
  margin: 11px 0;
  position: relative;
}
#tianai-captcha.tianai-captcha-slider .slider-move-track {
  position: relative;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: var(--n-color-segment);
  color: #999;
  transition: 0s;
  font-size: 14px;
  box-sizing: content-box;
  border: var(--solid-border);
  border-radius: 4px;
}
#tianai-captcha.tianai-captcha-slider .refresh-btn,
#tianai-captcha.tianai-captcha-slider .close-btn {
  display: inline-block;
}
#tianai-captcha.tianai-captcha-slider .slider-move {
  line-height: 38px;
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  color: #88949d;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;
  filter: opacity(0.8);
}
#tianai-captcha.tianai-captcha-slider .slider-move .slider-move-btn {
  transform: translate(0px, 0px);
  position: absolute;
  top: 1px;
  left: 0;
  width: 59px;
  height: 39px;
  background-color: var(--slider-move-btn-color);
  background-repeat: no-repeat;
  background-size: contain;
  border-radius: 5px;
}
#tianai-captcha.tianai-captcha-slider .slider-tip {
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 15px;
  line-height: normal;
  color: var(--default-text-color);
}
#tianai-captcha.tianai-captcha-slider .slider-move-btn:hover {
  cursor: pointer;
  border: 1px solid rgb(var(--primary-color));
}
#tianai-captcha.tianai-captcha-slider .slider-move-btn .img-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(13deg);
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
#tianai-captcha.tianai-captcha-slider .slider-move-btn:hover .img-wrapper {
  transform: translateX(5px) rotate(49deg);
  animation: fly-1 0.6s ease-in-out infinite alternate;
}
#tianai-captcha.tianai-captcha-slider .slider-move-btn:hover img {
  transform: translateX(5px) rotate(49deg);
  transition: transform 0.5s cubic-bezier(0.76, 0, 0.24, 1);
}
@keyframes fly-1 {
  from {
    transform: translateY(0.1em);
  }
  to {
    transform: translateY(-0.1em);
  }
}
#tianai-captcha.tianai-captcha-rotate .rotate-img-div {
  height: 100%;
  text-align: center;
}
#tianai-captcha.tianai-captcha-rotate .rotate-img-div img {
  height: 100%;
  transform: rotate(0deg);
  display: inline-block;
}
#tianai-captcha.tianai-captcha-concat .tianai-captcha-slider-concat-img-div {
  background-size: 100% 180px;
  position: absolute;
  transform: translate(0px, 0px);
  z-index: 1;
  width: 100%;
}
#tianai-captcha.tianai-captcha-concat .tianai-captcha-slider-concat-bg-img {
  width: 100%;
  height: 100%;
  position: absolute;
  transform: translate(0px, 0px);
  background-size: 100% 180px;
}
#tianai-captcha.tianai-captcha-word-click {
  position: relative;
  box-sizing: border-box;
}
#tianai-captcha.tianai-captcha-word-click .click-tip {
  position: relative;
  height: 40px;
  width: 100%;
}
#tianai-captcha.tianai-captcha-word-click .click-tip .tip-img {
  width: 130px;
  position: absolute;
  right: 15px;
}
#tianai-captcha.tianai-captcha-word-click .click-tip #tianai-captcha-click-track-font {
  font-size: 18px;
  display: inline-block;
  height: 40px;
  line-height: 40px;
  position: absolute;
}
#tianai-captcha.tianai-captcha-word-click .slider-bottom {
  position: relative;
  top: 6px;
}
#tianai-captcha.tianai-captcha-word-click .content #bg-img-click-mask {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
#tianai-captcha.tianai-captcha-word-click .content #bg-img-click-mask .click-span {
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 50px;
  background-color: #409eff;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  color: #fff;
  border: 2px solid #fff;
  box-sizing: content-box;
}
