import {
  Resize<PERSON><PERSON>,
  Resize<PERSON><PERSON>ler,
  ResizeItem,
} from "@douyinfe/semi-ui";
import { useState } from "react";
import { UserList } from "./user-list";
import { DeptList } from "../dept";

export default function User() {
  const [deptId, setDeptId] = useState(null);
  const [deptData, setDeptData] = useState<any>([]);
  return (
    <div className="p-2 w-full h-full">
      <ResizeGroup direction="horizontal">
        <ResizeItem defaultSize={"20%"} min={"15%"} max={"20%"}>
          <div className="px-2 bg-semi-color-white w-full h-full rounded-md">
            <div className="text-semi-color-text-0 font-semibold px-3 pt-4">
              部门与用户
            </div>
            {/* <div className="h-20 w-full flex items-center justify-center">
              <Button theme="solid" size="large">邀请成员</Button>
            </div> */}
            <DeptList
              onDeptSelect={(id: any) => {
                setDeptId(id);
              }}
              onDeptData={(data: any) => {
                setDeptData(data);
              }}
            />
          </div>
        </ResizeItem>
        <ResizeHandler
          style={{
            backgroundColor: "transparent",
            zIndex: 0
          }}
        ></ResizeHandler>
        <ResizeItem defaultSize={"80%"}>
          <div className="bg-semi-color-white w-full h-full rounded-md">
            <UserList deptId={deptId} deptData={deptData} />
          </div>
        </ResizeItem>
      </ResizeGroup>
    </div>
  );
}



