import { addOssConfig, editOssConfig, getOssConfig } from '@/api/system/oss-config';
import { Col, Form, Modal, Row, Spin, Toast } from '@douyinfe/semi-ui'
import React, { useEffect } from 'react'

export default function ConfigForm({ open, onCancel, id, onRefresh, sys_oss_config_access, sys_is_http }: any) {
    const formApiRef = React.useRef(null);
    const [loading, setLoading] = React.useState(false);
    const [loadLoading, setLoadLoading] = React.useState(false);
    useEffect(() => {
        if (id && open) {
            featchData();
        }
    }, [id, open])
    const getFormApi = (formApi: any) => {
        formApiRef.current = formApi;
    };

    const featchData = async () => {
        setLoadLoading(true);
        await getOssConfig(id).then((res) => {
            setLoadLoading(false);
            setTimeout(() => {
                // @ts-expect-error
                formApiRef.current?.setValues({
                    ...res.data
                });
            }, 0);
        }).catch(() => { setLoadLoading(false) })
    }

    const handleAddSubmit = async (values: any) => {
        try {
            setLoading(true);
            const res = await addOssConfig({
                ...values,
            });
            Toast.success(res.msg);
            setLoading(false);
            onRefresh();
            handleReset();
        } catch (err) {
            setLoading(false);
            throw err;
        }
    };
    const handleEditSubmit = async (values: any) => {
        try {
            setLoading(true);
            const res = await editOssConfig({
                ...values,
                ossConfigId: id,
            });
            onRefresh();
            Toast.success(res.msg);
            setLoading(false);
            handleReset();
        } catch (err) {
            setLoading(false);
            // throw err;
        }
    };
    const submitForm = async () => {
        try {
            // @ts-expect-error
            const values = await formApiRef.current?.validate();
            id ? await handleEditSubmit(values) : await handleAddSubmit(values);
        } catch (errors) {
            console.error(errors);
        }
    }
    const handleReset = () => {
        // @ts-expect-error
        formApiRef.current?.reset();
        onCancel();
    };
    return (
        <Modal
            width={800}
            visible={open}
            centered
            title={
                id ? "修改配置" : "新增配置"
            }
            className="semi-light-scrollbar"
            bodyStyle={{
                height: 400,
                overflowY: "auto",
                overflowX: "hidden", // 隐藏水平滚动条，防止滚动条影响布局
                // margin: "0",
                position: "relative",
                // padding: 0
            }}
            okButtonProps={{
                disabled: loadLoading,
            }}
            closeOnEsc={false}
            onOk={submitForm}
            onCancel={handleReset}
            confirmLoading={loading}
        >

            <div className='w-full h-full'>
                {
                    loadLoading ? <div className='flex flex-col items-center w-full h-[300px] justify-center'>
                        <Spin spinning size="large" />
                        <span className="mt-2 text-gray-500 text-sm">加载中...</span>
                    </div>
                        : <Form getFormApi={getFormApi}>
                            <Row gutter={10}>
                                <Col span={12}>
                                    <Form.Input
                                        rules={[
                                            { required: true, message: "请输入配置key" },
                                            {
                                                min: 2,
                                                max: 100,
                                                message: "配置key长度必须介于2和100之间",
                                            },
                                        ]}
                                        showClear
                                        placeholder={"请输入配置key"} label="配置key" field='configKey'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input
                                        rules={[
                                            { required: true, message: "请输入访问站点" },
                                            {
                                                min: 2,
                                                max: 100,
                                                message: "访问站点长度必须介于2和100之间",
                                            }
                                        ]}
                                        showClear
                                        addonBefore="http://"
                                        placeholder={"请输入访问站点"} label="访问站点" field='endpoint'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input
                                        showClear
                                        addonBefore="http://"
                                        placeholder={"请输入自定义域名"} label="自定义域名" field='domain'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input showClear field='accessKey' label="AccessKey" placeholder="请输入AccessKey" rules={[
                                        { required: true, message: "请输入AccessKey" },
                                        {
                                            min: 2,
                                            max: 100,
                                            message: "AccessKey长度必须介于2和100 之间",
                                        },
                                    ]} />
                                </Col>
                                <Col span={12}>
                                    <Form.Input
                                        placeholder={"请输入SecretKey"}
                                        rules={[
                                            { required: true, message: "请输入SecretKey" },
                                            {
                                                min: 2,
                                                max: 100,
                                                message: "SecretKey长度必须介于2和100 之间",
                                            }
                                        ]}
                                        mode='password'
                                        showClear
                                        label="SecretKey" field='secretKey'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input rules={[
                                        { required: true, message: "请输入桶名称" },
                                        {
                                            min: 2,
                                            max: 100,
                                            message: "桶名称长度必须介于2和100 之间",
                                        }

                                    ]} showClear placeholder={"请输入桶名称"} label="桶名称" field='bucketName'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input showClear placeholder={"请输入前缀"} label="前缀" field='prefix'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.RadioGroup
                                        rules={[{
                                            required: true, message: "请选择是否HTTPS"
                                        }]}
                                        initValue={"Y"} field="isHttps" label='是否HTTPS' >
                                        {
                                            sys_is_http && sys_is_http.map((item: any) => {
                                                return <Form.Radio value={item.dictValue}>{item.dictLabel}</Form.Radio>
                                            })
                                        }
                                    </Form.RadioGroup>
                                </Col>
                                <Col span={24}>
                                    <Form.Input showClear placeholder={"请输入域"} label="域" field='region'></Form.Input>
                                </Col>
                                <Col span={24}>
                                    <Form.RadioGroup
                                        rules={[{
                                            required: true, message: "请选择桶权限类型"
                                        }]}
                                        initValue={"0"} field="accessPolicy" label='桶权限类型' >
                                        {
                                            sys_oss_config_access && sys_oss_config_access.map((item: any) => {
                                                return <Form.Radio value={item.dictValue}>{item.dictLabel}</Form.Radio>
                                            })
                                        }
                                    </Form.RadioGroup>
                                </Col>
                            </Row>
                        </Form>
                }

            </div>
        </Modal >
    )
}
