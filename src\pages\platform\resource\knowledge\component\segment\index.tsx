import { getDataset, updateStatus } from "@/api/platform/dataset";
import { getDatasetSegmentList } from "@/api/platform/datasetSegment";
import DictTag from "@/components/DictTag";
import DocIcon from "@/components/icon/DocIcon";
import WordIcon from "@/components/icon/WordIcon";
import { MarkdownBox } from "@/components/Markdown/MarkdownBox";
import useDictionary from "@/hooks/useDictionary";
import {
  IconBox,
  IconCamera,
  IconChevronLeft,
  IconDelete,
  IconEdit,
  IconMore,
  IconPlus,
} from "@douyinfe/semi-icons";
import {
  Badge,
  Button,
  Card,
  Col,
  Descriptions,
  Dropdown,
  Input,
  List,
  Pagination,
  Row,
  Switch,
  Toast,
  Typography,
} from "@douyinfe/semi-ui";
import {
  getRouteApi,
  useLoaderData,
  useLoaderDeps,
  useNavigate,
  useParams,
  useRouter,
} from "@tanstack/react-router";
import React, { useEffect, useState } from "react";

function Segment() {
  const { detailId, id } = useParams({ strict: false });
  const [dataset, setDataset] = React.useState<any>({});
  const initPageInfo = {
    page: 1,
    pageSize: 15,
  };
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dataSource, setData] = useState<any>([]);
  const { history } = useRouter();
  // const routeApi = getRouteApi(/platform/knowledge/details/$detailId/segment/$id"")
  // const data = routeApi.useLoaderData()
  // const data =  useLoaderData();
  // const data = useLoaderDeps({
  //   from: "/platform/knowledge/details/$detailId/segment/$id",
  // });
  // console.log("data", data);

  // useEffect(() => {
  //   if (data) {
  //     setDataset(data);
  //     setOpen(data?.status === 0 ? true : false);
  //   }

  // }, [data]);
  const handleGoBack = () => {
    history.go(-2);
  };
  const [open, setOpen] = useState<boolean>(
    dataset?.status === 0 ? true : false
  );
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_dataset_origin",
    "sys_normal_disable",
    "ai_chuck_type",
    "ai_chunk_status",
  ]);
  useEffect(() => {
    if (id) {
      getDetails(id);
      console.log(detailId, id);
      fetchData();
    }
  }, [id]);

  const fetchData = (
    currentPage = 1,
    currentPageSize = 15,
    formValues = {}
  ) => {
    setLoading(true);
    setPage(currentPage);
    setPageSize(currentPageSize);
    getDatasetSegmentList(id, {
      pageNum: currentPage,
      pageSize: currentPageSize,
      ...formValues,
    })
      .then((res) => {
        setLoading(false);
        setData(res.rows);
        setTotal(res.total);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handlePageChange = (page: number) => {
    fetchData(page, pageSize);
  };
  const handlePageSizeChange = (pageSize: number) => {
    fetchData(page, pageSize);
  };
  const getDetails = (idParams: any) => {
    getDataset(idParams).then(({ data }) => {
      setDataset(data);
      setOpen(data?.status === 0 ? true : false);
      // formRef.current.setValues({
      //   embedModel: data?.embedModel,
      // });
    });
  };
  const handleChangeStatus = (checked: any) => {
    //修改状态
    updateStatus(id).then(({ msg }) => {
      Toast.success(msg);
      setOpen(checked);
    });
  };
  return (
    <div className="w-full py-2 px-2 flex h-full rounded-md">
      <Row gutter={10} className="h-full flex-1">
        <Col span={19} className="h-full ">
          <Card
            bodyStyle={{ height: "100%", paddingTop: 0 }}
            className="h-full "
            headerLine={false}
            headerExtraContent={
              <div className="flex gap-3 items-center justify-center">
                <Button icon={<IconPlus />}>添加分段</Button>
                <div className="flex gap-2 text-sm items-center border-solid border py-2 px-2 rounded-lg border-semi-color-border">
                  <div>
                    {open ? (
                      <span>
                        <Badge dot type="success" /> 已启用
                      </span>
                    ) : (
                      <span>
                        <Badge dot type="danger" /> 已停用
                      </span>
                    )}
                  </div>
                  <Switch
                    checked={open}
                    size="small"
                    onChange={handleChangeStatus}
                  />
                </div>
                <Dropdown
                  position="bottom"
                  trigger={"click"}
                  render={
                    <Dropdown.Menu className="w-[180px]">
                      <Dropdown.Item icon={<IconEdit />}>
                        文档重命名
                      </Dropdown.Item>
                      <Dropdown.Divider />
                      <Dropdown.Item type="danger" icon={<IconDelete />}>
                        删除
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  }
                >
                  <Button icon={<IconMore />} aria-label="操作" />
                </Dropdown>
              </div>
            }
            title={
              <div>
                <div className="flex items-center gap-2">
                  <Button
                    type="primary"
                    icon={<IconChevronLeft />}
                    iconPosition="left"
                    theme="outline"
                    onClick={handleGoBack}
                  ></Button>
                  <WordIcon />
                  <div className="text-md font-bold">{dataset?.name}</div>
                </div>
              </div>
            }
          >
            <div className="flex w-full justify-between items-center px-5 mb-1">
              <div className="text-md font-semibold">{dataset?.chunkNum}段</div>
              <div className="w-[300px]">
                <Input
                  showClear
                  onClear={() => {
                    fetchData(1, pageSize);
                  }}
                  placeholder={"搜索文档"}
                  onEnterPress={(e: any) => {
                    fetchData(1, pageSize, { content: e.target?.value });
                  }}
                ></Input>
              </div>
            </div>
            <List
              style={{ height: "calc(100% - 146px)" }}
              className="overflow-y-auto"
              loading={loading}
              // footer={
              //   <div>
              //     <Pagination
              //       total={80}
              //       showTotal
              //       style={{ marginBottom: 12 }}
              //     ></Pagination>
              //   </div>
              // }
              dataSource={dataSource}
              renderItem={(item: any, index: any) => (
                <List.Item
                  key={index}
                  className="w-full cursor-pointer rounded-sm hover:bg-semi-color-fill-0"
                  style={{}}
                >
                  <MarkdownBox content={item.content} />
                </List.Item>
              )}
            />
            <div className="py-2 flex items-center justify-end">
              <Pagination
                currentPage={page}
                pageSize={pageSize}
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                total={total}
                // onChange={handlePageChange}
                showTotal
              />
            </div>
          </Card>
        </Col>
        <Col span={5} className="h-full">
          <Card title="文档元数据" headerLine={false} className="h-full">
            <Descriptions className="w-full" layout="vertical" align="left">
              <Descriptions.Item itemKey="文件名称">
                <Typography.Paragraph copyable ellipsis={{ showTooltip: true }}>
                  {dataset?.name}
                </Typography.Paragraph>
              </Descriptions.Item>
              <Descriptions.Item itemKey="来源来源">
                <DictTag
                  dictType="ai_dataset_origin"
                  dictValue={dataset?.origin}
                  dictionaryData={dictionaryData.ai_dataset_origin || []}
                />
              </Descriptions.Item>
              <Descriptions.Item itemKey="大小">3级</Descriptions.Item>
              <Descriptions.Item itemKey="分片大小">
                {dataset?.wordCount}
              </Descriptions.Item>
              {/* <Descriptions.Item itemKey="分片数量">未认证</Descriptions.Item> */}
              <Descriptions.Item itemKey="分片数量">
                {dataset?.chunkNum}
              </Descriptions.Item>
              <Descriptions.Item itemKey="嵌入耗费Token数">
                {dataset?.tokens}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
}
export default Segment;
