import React from "react";
import { Editor } from "./Editor";
import { Avatar, Button, Tooltip } from "@douyinfe/semi-ui";
import { IconArrowLeft, IconEdit, IconHistory } from "@douyinfe/semi-icons";

export default function FlowGram() {
  // absolute top-0
  return (
    <div className="w-full flex flex-col bg-semi-color-primary-light-default relative h-full">
      <div className="h-[60px] px-4 bg-semi-color-white shadow-sm z-20 border-b border-solid border-semi-color-border  w-full flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            icon={<IconArrowLeft />}
            theme="borderless"
            className="text-semi-color-text-light-default hover:bg-semi-color-bg-light-default"
          />
          <div className="flex items-center gap-2">
            <Avatar shape="square" size="small" color="blue">
              H
            </Avatar>
            <div className="flex flex-col gpa-1">
              <div className="text-md font-semibold">天气流程图</div>
              <div className="text-[12px] text-semi-color-text-2">123</div>
            </div>
            <div>
              <Tooltip content="编辑" position="bottom">
                <Button
                  theme="borderless"
                  type="tertiary"
                  size="small"
                  icon={<IconEdit />}
                ></Button>
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Tooltip content="历史记录" position="bottom">
            <Button icon={<IconHistory />} type="tertiary" />
          </Tooltip>
          <Button theme="solid">发布</Button>
        </div>
      </div>
      <div className="flex-1 flowgram-editor " id="flowgram-editor">
        <Editor />
      </div>
    </div>
  );
}
