import { IconM<PERSON>, IconSearch } from "@douyinfe/semi-icons";
import {
  Ava<PERSON>,
  Badge,
  Button,
  Card,
  Divider,
  Dropdown,
  Empty,
  Input,
  List,
  Pagination,
  Select,
  Tag,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState } from "react";
import AppModelForm from "./components/AppModelForm";
import { useBoolean } from "@/hooks";
import { useNavigate } from "@tanstack/react-router";
import { getAppList } from "@/api/platform/agent";
import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import EmtpyBox from "@/components/icon/EmtpyBox";

export default function AppIndex() {
  const [open, { setTrue: openForm, setFalse: closeLoading }] =
    useBoolean(false);
  const navigator = useNavigate();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [dataSource, setData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    fetchData();
  }, []);
  const fetchData = (
    currentPage = 1,
    currentPageSize = 10,
    formValues = {}
  ) => {
    setLoading(true);
    setPage(currentPage);
    setPageSize(currentPageSize);
    getAppList({
      pageNum: currentPage,
      pageSize: currentPageSize,
      ...formValues,
    })
      .then((res) => {
        setLoading(false);
        setData(res.rows);
        setTotal(res.total);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handlePageChange = (page: number) => {
    fetchData(page, pageSize);
  };
  const handlePageSizeChange = (pageSize: number) => {
    fetchData(page, pageSize);
  };
  return (
    <div className="h-full relative p-4 flex flex-col overflow-hidden">
      <div className="text-nowrap font-extrabold pt-2 text-lg mb-2 flex items-center justify-between ">
        <div>应用管理</div>
        <div className="flex gap-2 items-center">
          <Button theme="solid" type="primary" onClick={() => openForm()}>
            创建应用
          </Button>
        </div>
      </div>
      <div className="w-full flex flex-row items-center gap-2 justify-between">
        <Select defaultValue="douyin" className="w-[200px]">
          <Select.Option value="douyin">抖音</Select.Option>
          <Select.Option value="ulikecam">轻颜相机</Select.Option>
          <Select.Option value="jianying" disabled>
            剪映
          </Select.Option>
          <Select.Option value="xigua">西瓜视频</Select.Option>
        </Select>
        <div className="w-[300px]">
          <Input prefix={<IconSearch />} className="w-full" showClear />
        </div>
      </div>
      <div className="mt-2 flex-1">
        <List
          dataSource={dataSource}
          layout="horizontal"
          loading={loading}
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          emptyContent={
            <Empty
              image={<EmtpyBox />}
              darkModeImage={<IllustrationNoContentDark />}
              description="暂无数据"
            />
          }
          renderItem={(item) => {
            return (
              <List.Item
                className="w-full"
                onClick={() => {
                  navigator({
                    to: `/platform/app/detail/${item.id}`,
                  });
                }}
              >
                <Card
                  className="w-full"
                  bodyStyle={{
                    width: "100%",
                    padding: "20px 20px 20px 20px",
                  }}
                  shadows="hover"
                >
                  <div
                    className="flex flex-col gap-2"
                    style={{
                      width: "100%",
                    }}
                  >
                    <div
                      className="py-1  flex flex-row gap-2"
                      style={{
                        width: "calc(100% - 48px)",
                      }}
                    >
                      <div className="w-[48px]">
                        <Avatar size="medium" src={item.logo} shape="square" />
                      </div>
                      <div style={{ width: "calc(100% - 48px)" }}>
                        <div className="font-semibold text-md text-semi-color-text-0">
                          {item.name}
                        </div>
                        <Tooltip content={item.description}>
                          <div className="text-semi-color-text-2 truncate">
                            {item.description}
                          </div>
                        </Tooltip>
                      </div>
                    </div>
                    <div className="mb-2 flex flex-row gap-1">
                      {item.tags.split(",").map((tag: any,index: number) => (
                        <Tag key={index}>{tag}</Tag>
                      ))}
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2 text-sm text-semi-color-text-2">
                        {item.onwer?.avatar ? (
                          <Avatar  size="small" src={item.onwer?.avatar}></Avatar>
                        ) : (
                          <Avatar
                            className="text-semi-color-white"
                            style={{ backgroundColor: "#a5d8ff" }}
                            size="small"
                          >
                            {item?.onwer?.name
                              ? item?.onwer?.name.substring(0, 1)
                              : "-"}
                          </Avatar>
                        )}

                        <div>{item?.onwer?.name ? item?.onwer?.name : "-"}</div>
                        <div className="flex gap-1 items-center">
                          <span>更新时间：{item.updatedAt}</span>
                        </div>
                      </div>
                      <div>
                        <Dropdown
                          className="min-w-[150px]"
                          trigger={"click"}
                          position={"bottomLeft"}
                          render={
                            <Dropdown.Menu>
                              <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                编辑信息
                              </Dropdown.Item>
                              <Divider />
                              {/* 横线 */}
                              <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                复制
                              </Dropdown.Item>
                              <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                导出智能体
                              </Dropdown.Item>
                              <Divider />
                              <Dropdown.Item
                                type="danger"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                删除
                              </Dropdown.Item>
                            </Dropdown.Menu>
                          }
                        >
                          <Button
                            theme="borderless"
                            onClick={(e) => e.stopPropagation()}
                            type="tertiary"
                            icon={<IconMore />}
                          />
                        </Dropdown>
                      </div>
                    </div>
                  </div>
                </Card>
              </List.Item>
            );
          }}
        />
      </div>
      <div className="flex justify-end mb-2  px-2 py-2  rounded-lg">
        <Pagination
          total={total}
          showSizeChanger
          currentPage={page}
          pageSize={pageSize}
          onPageChange={(page) => handlePageChange(page)}
          onPageSizeChange={(pageSize) => handlePageSizeChange(pageSize)}
        />
      </div>
      <AppModelForm title="新建智能体" open={open} onCancel={closeLoading} />
    </div>
  );
}
