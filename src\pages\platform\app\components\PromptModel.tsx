import { generatePrompt } from "@/api/platform/prompt";
import MofaIcon from "@/components/icon/MofaIcon";
import { MarkdownBox } from "@/components/Markdown/MarkdownBox";
import {
  IconCode,
  IconLanguage,
  IconList,
  IconMicrophone,
  IconSend,
} from "@douyinfe/semi-icons";
import {
  Button,
  Divider,
  Form,
  Icon,
  Modal,
  Spin,
  Toast,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import classNames from "classnames";
import React, { useState } from "react";

export default function PromptModel({ open, onCancel, onOk }: any) {
  const formApiRef = React.useRef(null);
  const [prompt, setPrompt] = useState("");
  const [loading, setLoading] = useState(false);

  // 添加清除状态的函数
  const resetState = () => {
    setPrompt("");
    // @ts-expect-error
    formApiRef.current?.reset();
  };
  // 修改 Modal 的 onOk 和 onCancel 处理
  const handleOk = () => {
    if (prompt) {
      onOk(prompt);
      resetState(); // 清除状态
    } else {
      Toast.warning("请先生成提示词");
      return;
    }
  };
  const handleCancel = () => {
    resetState(); // 清除状态
    onCancel();
  };
  const getPrompt = async () => {
    setLoading(true);
    // @ts-expect-error
    const values = await formApiRef.current?.validate();
    if (values.cmd === "" || values.cmd === undefined) {
      Toast.error("描述不能为空");
      return Promise.reject();
    }
    return generatePrompt({
      ...values,
    })
      .then(({ data }) => {
        setPrompt(data);
        setLoading(false);
        return Promise.resolve();
      })
      .catch(() => {
        setLoading(false);
        return Promise.reject();
      });
  };

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const cmds = [
    {
      label: "SQL生成",
      icon: <IconCode />,
      value: `你是一个专业的 SQL 转换助手，能够精准将自然语言描述的需求转化为符合标准 SQL 语法的查询语句。`,
    },
    {
      label: "翻译",
      icon: <IconLanguage />,
      value: `你是专业的多语言翻译专家，需根据用户提供的原文内容，生成准确、流畅且符合目标语言习惯的译文。`,
    },
    {
      label: "总结会议纪要",
      icon: <IconMicrophone />,
      value: "将会议内容提炼总结，包括讨论主题、关键要点和待办事项",
    },
    {
      label: "Excel 公式专家",
      icon: <IconList />,
      value: `你是专业的 Excel 公式专家，需根据用户描述的表格数据需求，生成准确、高效的 Excel 公式。`,
    },
    {
      label: "旅行规划助手",
      icon: <IconSend />,
      value: `你是专业的旅行规划助手，需根据用户需求生成个性化、实用的旅行方案。`,
    },
  ];
  return (
    <Modal
      title="模型选择"
      header={
        <div className="py-4 px-2">
          <div className="text-lg font-semibold">提示词生成器</div>
          <div className="text-sm text-semi-color-text-2">
            提示词生成器使用配置的模型来优化提示词，以获得更高的质量和更好的结构。请写出清晰详细的说明。
          </div>
        </div>
      }
      bodyStyle={{
        height: 500,
        display: "flex",
        position: "relative",
      }}
      centered
      width={1200}
      visible={open}
      onCancel={handleCancel}
      closeOnEsc={true}
      onOk={handleOk}
    >
      <div className="flex flex-1 w-full h-full flex-row gap-2 box-border">
        <div className="flex h-full flex-1 flex-col box-border">
          <Form getFormApi={getFormApi}>
            <Divider margin="12px" align="left">
              预设指令
            </Divider>
            <div className="flex flex-row flex-wrap gap-1">
              {cmds.map((item, index) => {
                return (
                  <Button
                    theme="light"
                    icon={item.icon}
                    key={index}
                    onClick={() => {
                      formApiRef.current?.setValue("cmd", item.value);
                    }}
                    type="tertiary"
                  >
                    {item.label}
                  </Button>
                );
              })}
            </div>
            <div>
              <Form.TextArea
                // rules={[
                //   {
                //     required: true,
                //     message: "请填写指令",
                //   },
                // ]}
                field="cmd"
                label="描述"
                rows={12}
              ></Form.TextArea>
            </div>
            <div className="flex justify-end">
              <Button
                type="primary"
                theme="solid"
                onClick={getPrompt}
                style={{ marginTop: "12px" }}
              >
                生成提示词
              </Button>
            </div>
          </Form>
        </div>
        <div
          className={classNames(
            "flex-1  h-full w-full box-border ",
            loading ? " flex justify-center items-center" : ""
          )}
        >
          {loading && (
            <div className="w-full flex justify-center items-center ">
              <Spin
                size="large"
                tip="正在生成中..."
                style={{ width: "200px" }}
              />
            </div>
          )}
          {!loading && (
            <>
              {prompt ? (
                <div className="flex flex-col px-4 py-2 h-full bg-semi-color-fill-0 rounded-md">
                  <div className="font-semibold py-2">生成的提示词</div>
                  <div className="flex-1 overflow-auto semi-light-scrollbar">
                    {/* <pre className="whitespace-pre-wrap"> */}
                    <MarkdownBox content={prompt}></MarkdownBox>
                    {/* </pre> */}
                  </div>
                </div>
              ) : (
                <div className="flex justify-center flex-col h-full items-center">
                  <Icon svg={<MofaIcon />} size="extra-large" />
                  <div className="text-semi-color-text-2 ">
                    在左侧输入你需求描述， 预览提示词将在此处显示。
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}
