import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const BROKEFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.background"
          label="背景"
          rows={6}
          placeholder="背景说明背景、脉络补充足够信息"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.role"
          label="角色"
          rows={6}
          placeholder="角色明确ChatGPT扮演的角色"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.objective"
          label="目标"
          rows={6}
          placeholder="目标描述需要实现的目标"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.keyResult"
          label="关键结果"
          rows={6}
          placeholder="关键结果具体效果试验并调整"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.evolve"
          label="改进"
          rows={6}
          placeholder="改进试错并改进：三种改进方法自由组合"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default BROKEFields;