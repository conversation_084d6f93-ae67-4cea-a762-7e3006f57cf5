import React, { useState, useRef, useEffect } from "react";
import { But<PERSON>, Toast, Tooltip, Icon, Dropdown } from "@douyinfe/semi-ui";
import { Copy, Check, Maximize, Minimize, Palette } from "lucide-react";
import classNames from "classnames";
import { IconChevronDown, IconChevronUp } from "@douyinfe/semi-icons";
import hljs from "highlight.js";
import "highlight.js/styles/stackoverflow-light.css"; // 浅色主题
import "highlight.js/styles/stackoverflow-dark.css"; // 深色主题

interface CodeBlockProps {
  children: React.ReactNode;
  className?: string;
  language?: string;
  elementId: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  children,
  language = "javascript",
  elementId,
  className,
}) => {
  const [open, setOpen] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [codeTheme, setCodeTheme] = useState<"light" | "dark">("light");
  const codeRef = useRef<HTMLDivElement>(null);
  const lineNumbersRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const [lineCount, setLineCount] = useState(1);
  const [highlightedCode, setHighlightedCode] = useState<string>("");

  // 代码字体
  const codeFont = "Consolas,Monaco,Monospace,'Cascadia Code', 'Source Code Pro', 'Fira Code', 'SF Mono', Monaco, Menlo, Consolas, 'Courier New'";

  // 高亮代码
  useEffect(() => {
    if (typeof children === "string") {
      const langName = language?.replace("language-", "") || "";
      try {
        if (langName && hljs.getLanguage(langName)) {
          const highlighted = hljs.highlight(children, { language: langName }).value;
          setHighlightedCode(highlighted);
        } else {
          // 如果没有指定语言或语言不支持，使用自动检测
          const highlighted = hljs.highlightAuto(children).value;
          setHighlightedCode(highlighted);
        }
      } catch (error) {
        console.error("Highlight error:", error);
        setHighlightedCode(children); // 回退到原始代码
      }
    }
  }, [children, language, codeTheme]);

  // 计算行数并设置状态
  useEffect(() => {
    // 使用 setTimeout 确保 DOM 已经渲染
    const timer = setTimeout(() => {
      if (codeRef.current) {
        const text = codeRef.current.textContent || "";
        const lines = text.split("\n");
        // 过滤掉空行
        const nonEmptyLines = lines.filter((line) => line.trim() !== "");
        setLineCount(Math.max(nonEmptyLines.length, 1));
      }
    }, 0);

    return () => clearTimeout(timer);
  }, [children, open]);

  // 同步行号和代码内容的垂直滚动
  useEffect(() => {
    const codeElement = codeRef.current;
    const lineNumbersElement = lineNumbersRef.current;

    if (!codeElement || !lineNumbersElement) return;

    const handleScroll = () => {
      lineNumbersElement.scrollTop = codeElement.scrollTop;
    };

    codeElement.addEventListener("scroll", handleScroll);
    return () => {
      codeElement.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Toggle collapse state
  const toggleCollapse = () => {
    setOpen(!open);
  };

  // Toggle fullscreen state
  const toggleFullscreen = () => {
    if (isFullscreen) {
      document.exitFullscreen?.();
    } else {
      containerRef.current?.requestFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      if (!document.fullscreenElement) {
        setIsFullscreen(false);
      }
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
    };
  }, []);

  // 获取代码内容的纯文本用于复制功能
  const getCodeText = () => {
    if (typeof children === "string") {
      return children;
    }
    if (codeRef.current) {
      return codeRef.current.textContent || "";
    }
    return "";
  };

  // 切换代码块主题
  const switchCodeTheme = (theme: "light" | "dark") => {
    setCodeTheme(theme);

    // 切换 highlight.js 主题
    const styleSheets = document.styleSheets;
    for (let i = 0; i < styleSheets.length; i++) {
      const sheet = styleSheets[i];
      try {
        const rules = sheet.cssRules || sheet.rules;
        for (let j = 0; j < rules.length; j++) {
          const rule = rules[j];
          if (rule instanceof CSSStyleRule) {
            // 启用/禁用相应的主题样式
            if (rule.selectorText.includes('.hljs') || rule.selectorText.includes('.hljs-')) {
              if (theme === 'dark' && sheet.href?.includes('stackoverflow-light.css')) {
                (rule as any).disabled = true;
              } else if (theme === 'light' && sheet.href?.includes('stackoverflow-dark.css')) {
                (rule as any).disabled = true;
              } else {
                (rule as any).disabled = false;
              }
            }
          }
        }
      } catch (e) {
        // 跨域样式表可能会抛出安全错误，忽略
      }
    }
  };

  // 根据主题获取样式
  const getThemeStyles = () => {
    if (codeTheme === "dark") {
      return {
        container: "bg-gray-900 border-gray-700",
        textColor: "text-semi-color-white",
        header: "bg-gray-800 border-gray-700",
        lineNumbers: "bg-gray-800 text-gray-400 border-gray-700",
        codeArea: "bg-gray-900 text-gray-200",
      };
    }
    return {
      container: "bg-white border-semi-color-border",
      header: "bg-semi-color-fill-1 border-semi-color-border",
      textColor: "",
      lineNumbers: "bg-semi-color-fill-0 text-semi-color-text-2 border-semi-color-border",
      codeArea: "bg-[#f6f6f6] text-semi-color-text-0",
    };
  };

  const themeStyles = getThemeStyles();

  // 添加自定义样式到 head
  useEffect(() => {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.id = 'code-block-theme-styles';

    // 定义深色和浅色主题的样式
    const styleContent = `
      /* 导入代码字体 */
    //   @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500&display=swap');
      
      /* 深色主题样式 */
      .code-block-dark .hljs {
        display: block;
        overflow-x: auto;
        padding: 0;
        color: #abb2bf;
        background: transparent !important;
        font-size: 0.9rem;
        font-family: ${codeFont};
        font-feature-settings: "liga" 0, "calt" 0;
        letter-spacing: -0.025em;
        width: 100%;
        max-width: 100%;
      }
      
      /* 浅色主题样式 */
      .code-block-light .hljs {
        display: block;
        overflow-x: auto;
        padding: 0;
        color: #383a42;
        background: transparent !important;
        font-size: 0.9rem;
        font-family: ${codeFont};
        font-feature-settings: "liga" 0, "calt" 0;
        letter-spacing: -0.025em;
        width: 100%;
        max-width: 100%;
      }
      
      /* 确保主题切换时的平滑过渡 */
      .code-block-container {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        width: 100% !important;
        max-width: 100% !important;
      }
      
      /* 自定义滚动条样式 */
      .code-block-container pre::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      .code-block-dark pre::-webkit-scrollbar-thumb {
        background-color: #4b5563;
        border-radius: 4px;
      }
      
      .code-block-light pre::-webkit-scrollbar-thumb {
        background-color: #d1d5db;
        border-radius: 4px;
      }
      
      /* 确保代码元素没有背景色 */
      .code-block-container pre code {
        background: transparent !important;
        font-size: 0.9rem;
        line-height: 1.5;
        font-family: ${codeFont};
        font-feature-settings: "liga" 0, "calt" 0;
        letter-spacing: -0.025em;
        width: 100% !important;
        max-width: 100% !important;
        display: block;
      }
      
      /* 确保表格样式正确 */
      .code-block-container table {
        width: 100% !important;
        max-width: 100% !important;
        display: table;
        table-layout: fixed;
      }
      
      /* 行号样式 */
      .line-number {
        font-size: 0.9rem;
        line-height: 1.5;
        font-family: ${codeFont};
        font-feature-settings: "tnum" on, "lnum" on;
      }
      
      /* 确保代码高亮元素没有背景色 */
      .code-block-container .hljs-section,
      .code-block-container .hljs-name,
      .code-block-container .hljs-selector-tag,
      .code-block-container .hljs-deletion,
      .code-block-container .hljs-subst,
      .code-block-container .hljs-formula,
      .code-block-container .hljs-attr,
      .code-block-container .hljs-variable,
      .code-block-container .hljs-template-variable,
      .code-block-container .hljs-tag .hljs-attr,
      .code-block-container .hljs-template-tag,
      .code-block-container .hljs-comment,
      .code-block-container .hljs-quote,
      .code-block-container .hljs-addition,
      .code-block-container .hljs-meta,
      .code-block-container .hljs-string,
      .code-block-container .hljs-regexp,
      .code-block-container .hljs-number,
      .code-block-container .hljs-literal,
      .code-block-container .hljs-type,
      .code-block-container .hljs-selector-id,
      .code-block-container .hljs-selector-class,
      .code-block-container .hljs-title,
      .code-block-container .hljs-symbol,
      .code-block-container .hljs-bullet,
      .code-block-container .hljs-built_in,
      .code-block-container .hljs-keyword,
      .code-block-container .hljs-selector-pseudo,
      .code-block-container .hljs-doctag {
        background: transparent !important;
      }
    `;

    styleElement.textContent = styleContent;

    // 将样式添加到 head
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      const existingStyle = document.getElementById('code-block-theme-styles');
      if (existingStyle) {
        document.head.removeChild(existingStyle);
      }
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`w-full flex flex-col rounded-md border border-solid overflow-hidden hover:shadow-sm code-block-container ${themeStyles.container} ${isFullscreen ? "h-full" : ""} code-block-${codeTheme}`}
      style={{ fontFamily: codeFont, width: '100%' }}
    >
      <div className={`h-[32px] w-full p-1 flex justify-between items-center px-2 border-b ${themeStyles.header}`}>
        <div className="flex items-center space-x-2">
          <Button
            onClick={toggleCollapse}
            size="small"
            type="tertiary"
            theme="borderless"
            className={`${themeStyles.textColor}`}
            icon={
              open ? (
                <Icon className={`${themeStyles.textColor}`} svg={<IconChevronUp />} />
              ) : (
                <Icon className={`${themeStyles.textColor}`} svg={<IconChevronDown />} />
              )
            }
          >
            <span className={`${themeStyles.textColor}`} >
              {language?.replace("language-", "") || "text"}

            </span>
          </Button>
          {isFullscreen && (
            <span className="text-xs text-semi-color-text-2">全屏模式</span>
          )}
        </div>
        <div className="flex space-x-1">
          <Dropdown
            showTick
            trigger={"click"}
            render={
              <Dropdown.Menu>
                <Dropdown.Item
                  active={codeTheme === "light"}
                  onClick={() => switchCodeTheme("light")}
                >
                  浅色模式
                </Dropdown.Item>
                <Dropdown.Item
                  active={codeTheme === "dark"}
                  onClick={() => switchCodeTheme("dark")}
                >
                  深色模式
                </Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <Button
              theme="borderless"
              type="tertiary"
              size="small"
              icon={
                <Icon
                  className={`${themeStyles.textColor}`}
                  svg={
                    <Palette size="1em" />
                  }
                />
              }
              aria-label={"主题模式"}
            />
          </Dropdown>
          <CopyButton textClass={`${themeStyles.textColor}`} getTextFn={getCodeText} />
          <Tooltip content={isFullscreen ? "退出全屏" : "全屏"}>
            <Button
              theme="borderless"
              type="tertiary"
              size="small"
              icon={
                <Icon
                  svg={
                    isFullscreen ? (
                      <Minimize className={`${themeStyles.textColor}`} size="1em" />
                    ) : (
                      <Maximize className={`${themeStyles.textColor}`} size="1em" />
                    )
                  }
                />
              }
              aria-label={isFullscreen ? "退出全屏" : "全屏"}
              onClick={toggleFullscreen}
            />
          </Tooltip>
        </div>
      </div>

      <div
        ref={contentRef}
        className="transition-all duration-300 ease-in-out overflow-hidden"
        style={{
          height: open ? (isFullscreen ? "100%" : "400px") : "0px",
          opacity: open ? 1 : 0,
        }}
      >
        <div className={`flex h-full ${themeStyles.codeArea}`} id={elementId}>
          {/* 行号区域 - 固定在左侧 */}
          <div
            ref={lineNumbersRef}
            className={`py-2 pl-2 pr-3 text-right select-none border-r overflow-y-auto overflow-x-hidden ${themeStyles.lineNumbers}`}
            style={{ width: "3rem", flexShrink: 0 }}
          >
            {Array.from({ length: lineCount }, (_, i) => (
              <div key={i} className="line-number whitespace-nowrap">
                {i + 1}
              </div>
            ))}
          </div>

          {/* 代码内容区域 - 可以水平和垂直滚动 */}
          <div
            ref={codeRef}
            className="overflow-auto flex-1 h-full w-full"
            style={{ width: '100%', minWidth: '100%' }}
            onScroll={(e) => {
              if (lineNumbersRef.current) {
                lineNumbersRef.current.scrollTop = e.currentTarget.scrollTop;
              }
            }}
          >
            <pre className="w-full m-0 p-0" style={{ width: '100%', minWidth: '100%' }}>
              {typeof children === "string" ? (
                <code
                  className={classNames("text-md w-full p-2 block", className, {
                    "dark-code": codeTheme === "dark",
                  })}
                  style={{
                    width: '100%',
                    minWidth: '100%',
                    display: 'block',
                    boxSizing: 'border-box'
                  }}
                  dangerouslySetInnerHTML={{ __html: highlightedCode }}
                />
              ) : (
                <code
                  className={classNames("text-md w-full p-2 block", className, {
                    "dark-code": codeTheme === "dark",
                  })}
                  style={{
                    width: '100%',
                    minWidth: '100%',
                    display: 'block',
                    boxSizing: 'border-box'
                  }}
                >
                  {children}
                </code>
              )}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

// Updated Copy button component
interface CopyButtonProps {
  getTextFn: () => string;
  textClass?: string;
}

const CopyButton: React.FC<CopyButtonProps> = ({ getTextFn, textClass }) => {
  const [copied, setCopied] = useState<boolean>(false);

  const onCopy = async () => {
    try {
      setCopied(true);
      const text = getTextFn();
      await navigator.clipboard.writeText(text);
      Toast.success({
        content: "复制成功",
        showClose: false,
        duration: 1,
      });

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (error) {
      console.log(error);
      Toast.error({
        content: "复制失败",
        showClose: false,
        duration: 1,
      });
      setCopied(false);
    }
  };

  return (
    <Tooltip content="复制">
      <Button
        theme="borderless"
        type="tertiary"
        size="small"
        icon={
          !copied ? (
            <Icon svg={<Copy className={`${textClass}`} size="1em" />} />
          ) : (
            <Icon
              svg={<Check size="1em" />}
              style={{ color: "var(--semi-color-success)" }}
            />
          )
        }
        aria-label="复制"
        onClick={onCopy}
      />
    </Tooltip>
  );
};

export default CodeBlock;
