// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/%E6%A1%8C%E9%9D%A2/aigc/node_modules/.pnpm/vite@5.4.19_@types+node@20.17.30_less@4.2.2_sass@1.87.0_terser@5.39.0/node_modules/vite/dist/node/index.js";
import react from "file:///D:/%E6%A1%8C%E9%9D%A2/aigc/node_modules/.pnpm/@vitejs+plugin-react@4.4.1_vite@5.4.19_@types+node@20.17.30_less@4.2.2_sass@1.87.0_terser@5.39.0_/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { createHtmlPlugin } from "file:///D:/%E6%A1%8C%E9%9D%A2/aigc/node_modules/.pnpm/vite-plugin-html@3.2.2_vite@5.4.19_@types+node@20.17.30_less@4.2.2_sass@1.87.0_terser@5.39.0_/node_modules/vite-plugin-html/dist/index.mjs";

// src/utils/envUtils.ts
import dotenv from "file:///D:/%E6%A1%8C%E9%9D%A2/aigc/node_modules/.pnpm/dotenv@16.5.0/node_modules/dotenv/lib/main.js";
function wrapperEnv(envConf) {
  const ret = {};
  for (const envName of Object.keys(envConf)) {
    let realName = envConf[envName].replace(/\\n/g, "\n");
    realName = realName === "true" ? true : realName === "false" ? false : realName;
    if (envName === "VITE_PORT") {
      realName = Number(realName);
    }
    if (envName === "VITE_PROXY") {
      try {
        realName = JSON.parse(realName);
      } catch (error) {
        console.log(error);
      }
    }
    ret[envName] = realName;
    process.env[envName] = realName;
  }
  return ret;
}

// vite.config.ts
import path2 from "path";

// semi.ts
import { readFileSync, existsSync } from "fs";
import path from "path";
import { platform } from "os";
import { pathToFileURL } from "url";
import { createRequire } from "module";
var __vite_injected_original_import_meta_url = "file:///D:/%E6%A1%8C%E9%9D%A2/aigc/semi.ts";
var _require = createRequire(__vite_injected_original_import_meta_url);
var isWindows = platform() === "win32";
function semiTheming({ theme, ...options }) {
  const include = options.include && normalizePath(options.include);
  const variables = convertMapToString(options.variables || {});
  return {
    name: "semi-theme",
    enforce: "post",
    async load(id) {
      const filePath = normalizePath(id);
      if (/@douyinfe\/semi-(ui|icons|foundation)\/lib\/.+\.css$/.test(filePath)) {
        const scssFilePath = filePath.replace(/\.css$/, ".scss");
        const resolveCssImport = createCssImportResolver(scssFilePath);
        const sass = await import("file:///D:/%E6%A1%8C%E9%9D%A2/aigc/node_modules/.pnpm/sass@1.87.0/node_modules/sass/sass.node.mjs");
        return sass.compileString(
          loader(readFileSync(scssFilePath, "utf-8"), {
            ...options,
            name: theme,
            include,
            variables
          }),
          {
            importers: [
              {
                findFileUrl(url) {
                  return resolveCssImport(url);
                }
              }
            ],
            logger: sass.Logger.silent
          }
        ).css;
      }
    }
  };
}
var semi_default = semiTheming;
function loader(source, options) {
  const theme = options.name || "@douyinfe/semi-theme-default";
  const scssVarStr = `@import "~${theme}/scss/index.scss";
`;
  const cssVarStr = `@import "~${theme}/scss/global.scss";
`;
  let animationStr = `@import "~${theme}/scss/animation.scss";
`;
  let componentVariables;
  let fileStr = source;
  try {
    _require.resolve(`${theme}/scss/animation.scss`);
  } catch (e) {
    animationStr = "";
  }
  try {
    componentVariables = _require.resolve(`${theme}/scss/local.scss`);
  } catch (e) {
  }
  if (options.include || options.variables || componentVariables) {
    let localImport = "";
    if (componentVariables) {
      localImport += `
@import "~${theme}/scss/local.scss";`;
    }
    if (options.include) {
      localImport += `
@import "${options.include}";`;
    }
    if (options.variables) {
      localImport += `
${options.variables}`;
    }
    try {
      const regex = /(@import '.\/variables.scss';?|@import ".\/variables.scss";?)/g;
      const fileSplit = fileStr.split(regex).filter((item) => Boolean(item));
      if (fileSplit.length > 1) {
        fileSplit.splice(fileSplit.length - 1, 0, localImport);
        fileStr = fileSplit.join("");
      }
    } catch (error) {
    }
  }
  const prefixCls = options.prefixCls || "semi";
  const prefixClsStr = `$prefix: '${prefixCls}';
`;
  if (source.includes("semi-base")) {
    return `${animationStr}${cssVarStr}${scssVarStr}${prefixClsStr}${fileStr}`;
  } else {
    return `${scssVarStr}${prefixClsStr}${fileStr}`;
  }
}
function convertMapToString(map) {
  return Object.keys(map).reduce(function(res, cur) {
    return res + `${cur}: ${map[cur]};
`;
  }, "");
}
function normalizePath(p) {
  return path.posix.normalize(isWindows ? p.replace(/\\/g, "/") : p);
}
function createCssImportResolver(importer) {
  const _require2 = createRequire(importer);
  return (id) => {
    if (id.startsWith("~")) {
      const resolved = _require2.resolve(id.substring(1));
      if (existsSync(resolved)) {
        return pathToFileURL(resolved);
      }
      return null;
    }
    const filePath = path.resolve(path.dirname(importer), id);
    if (existsSync(filePath)) {
      return pathToFileURL(filePath);
    }
    return null;
  };
}

// vite.config.ts
var __vite_injected_original_dirname = "D:\\\u684C\u9762\\aigc";
console.log("path", path2.resolve(__vite_injected_original_dirname, "./src/styles.scss"));
var config = async (mode) => {
  const env = loadEnv(mode.mode, process.cwd());
  const viteEnv = wrapperEnv(env);
  return {
    plugins: [
      react(),
      // TanStackRouterVite(),
      createHtmlPlugin({
        inject: {
          data: {
            title: viteEnv.VITE_PAGE_TITLE
          }
        }
      }),
      semi_default({
        theme: "@semi-bot/semi-theme-herther-1"
      })
    ],
    resolve: {
      alias: {
        "~@semi": path2.join(__vite_injected_original_dirname, "./node_modules/@semi-bot"),
        "@assets": path2.join(__vite_injected_original_dirname, "./src/assets"),
        "@": path2.join(__vite_injected_original_dirname, "./src")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler"
          // or "modern", "legacy"
        }
      }
    },
    clearScreen: false,
    server: {
      host: "0.0.0.0",
      // 服务器主机名，如果允许外部访问，可设置为"0.0.0.0"
      port: 1420,
      cors: true,
      strictPort: true,
      proxy: {
        "/api": {
          target: viteEnv.VITE_GLOB_API_URL,
          // easymock
          changeOrigin: true,
          rewrite: (path3) => path3.replace(/^\/api/, "")
        }
      }
    },
    envPrefix: ["VITE_", "TAURI_"]
  };
};
var vite_config_default = defineConfig(config);
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
