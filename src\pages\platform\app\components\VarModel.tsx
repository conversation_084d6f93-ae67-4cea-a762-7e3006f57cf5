import Icon, {
  IconDescend2,
  IconFavoriteList,
  IconHandle,
  IconHash,
  IconMark,
  IconMinusCircle,
  IconPlusCircle,
} from "@douyinfe/semi-icons";
import {
  Divider,
  Form,
  Typography,
  Button,
  Modal,
  Row,
  Toast,
  Col,
  ArrayField,
} from "@douyinfe/semi-ui";
import classNames from "classnames";
import React, { useEffect, useRef, useState } from "react";
import { Plug, Plus } from "lucide-react";
import { motion } from "framer-motion";

export default function VarModel({
  open,
  onCancel,
  onSubmit,
  variable,
  allVariables,
  existingKeys = [],
}: any) {
  console.log("VarModel rendered with:", {
    open,
    variable,
    allVariablesCount: allVariables?.length
  });

  const formApiRef = useRef<any>(null);
  const [highlightedIndex, setHighlightedIndex] = useState<number | null>(null);
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [initialValues, setInitialValues] = useState<any>(null);

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };

  // 当modal打开或variable变化时，设置表单初始值
  useEffect(() => {
    if (open && formApiRef.current) {
      // 重置表单变化状态
      setIsFormChanged(false);
      setIsSaving(false);
      // 无论是编辑还是添加模式，都填充所有现有变量
      setTimeout(() => {
        // 将所有变量数据填充到表单中
        const formValues = allVariables.map(v => ({
          key: v.key || "",
          default: v.default || "",
          description: v.description || ""
        }));

        formApiRef.current?.setValue("variables", formValues);

        // 保存初始值，用于后续比较
        setInitialValues(JSON.stringify(formValues));

        // 如果是添加模式且没有现有变量，添加一个空行
        if (!variable && allVariables.length === 0) {
          const emptyRow = [{
            key: "",
            default: "",
            description: ""
          }];
          formApiRef.current?.setValue("variables", emptyRow);
          setInitialValues(JSON.stringify(emptyRow));
        }

        // 如果是编辑模式，找到并高亮对应的变量行
        if (variable && variable.key) {
          // 找到变量在数组中的索引
          const index = allVariables.findIndex(v => v.key === variable.key);
          if (index !== -1) {
            console.log("Setting highlighted index to:", index);
            setHighlightedIndex(index);

            // 3秒后取消高亮
            setTimeout(() => {
              setHighlightedIndex(null);
            }, 3000);
          }
        }

        // 验证数据是否设置成功
        console.log("Form values after set:", formApiRef.current?.getValues());
      }, 100);
    }
  }, [open, variable, allVariables]);

  // 监听表单值变化
  const handleFormValueChange = () => {
    if (!formApiRef.current || !initialValues) return;

    const currentValues = JSON.stringify(formApiRef.current.getValue("variables"));
    const hasChanged = currentValues !== initialValues;

    setIsFormChanged(hasChanged);
  };

  const handleSubmit = async (continueAdding: boolean) => {
    try {
      // 设置保存中状态
      setIsSaving(true);
      console.log("Setting isSaving to true");

      // 验证表单
      const formValues = await formApiRef.current?.validate();

      if (formValues && formValues.variables && formValues.variables.length > 0) {
        // 获取所有变量
        const variableDataList = formValues.variables;

        // 检查是否有空的变量行（可能是用户添加后未填写）
        const validVariables = variableDataList.filter(v => v.key && v.key.trim() !== '');

        if (validVariables.length === 0) {
          Toast.error({ content: "请至少添加一个有效变量" });
          setIsSaving(false);
          return;
        }

        // 检查变量key是否重复
        const keyMap = new Map();
        for (const v of validVariables) {
          const key = v.key.trim().toLowerCase();
          if (keyMap.has(key)) {
            Toast.error({ content: `变量名 "${v.key}" 重复，请确保每个变量名唯一` });
            setIsSaving(false);
            return;
          }
          keyMap.set(key, true);
        }

        // 检查是否与现有变量重复（如果是编辑模式，需要排除当前编辑的变量）
        if (existingKeys && existingKeys.length > 0) {
          for (const v of validVariables) {
            const key = v.key.trim().toLowerCase();
            // 如果是编辑模式，需要排除当前编辑的变量
            if (variable && variable.key && variable.key.toLowerCase() === key) {
              continue;
            }
            if (existingKeys.some(existingKey => existingKey.toLowerCase() === key)) {
              Toast.error({ content: `变量名 "${v.key}" 已存在，请使用不同的名称` });
              setIsSaving(false);
              return;
            }
          }
        }

        // 格式化变量数据，确保属性名称一致
        const formattedVariables = validVariables.map(v => ({
          key: v.key,
          defaultValue: v.default || v.defaultValue || "",
          description: v.description || ""
        }));


        try {
          // 调用父组件的onSubmit方法，传递所有有效变量
          await onSubmit(formattedVariables, continueAdding);

          // 如果继续添加，则重置表单
          if (continueAdding) {
            const emptyRow = [{
              key: "",
              default: "",
              description: ""
            }];
            formApiRef.current?.setValue("variables", emptyRow);
            setInitialValues(JSON.stringify(emptyRow));
            setIsFormChanged(false);
          }
        } catch (error) {
          Toast.error({ content: "保存变量失败" });
        } finally {
          // 无论成功还是失败，都重置保存状态
          console.log("Setting isSaving to false");
          setIsSaving(false);
        }
      } else {
        setIsSaving(false);
      }
    } catch (error) {
      Toast.error({ content: "请完成必填项" });
      setIsSaving(false);
    }
  };

  return (
    <Modal
      width={"45%"}
      title={variable ? "编辑变量" : "添加变量"}
      visible={open}
      centered
      footer={
        <div className="flex gap-1 justify-end mx-1 my-2">
          <Button onClick={() => onCancel()}>取消</Button>
          <Button
            type="primary"
            theme="solid"
            loading={isSaving}
            disabled={!isFormChanged}
            onClick={() => handleSubmit(false)}
          >
            保存
          </Button>
        </div>
      }
      className="semi-light-scrollbar"
      onCancel={onCancel}
      bodyStyle={{
        height: 400,
        overflow: 'hidden' // 防止 Modal body 出现滚动条
      }}
    >
      <div className="w-full h-full overflow-y-auto pr-2 custom-scrollbar">
        <div>
          <div className="font-semibold">变量</div>
          <div className="text-semi-color-text-1 mb-4"> {/* 添加底部边距，使布局更加宽松 */}
            这些变量将替换掉提示词中的占位符
          </div>
        </div>
        <Form
          className="w-full"
          allowEmpty
          getFormApi={getFormApi}
          onChange={handleFormValueChange}
        >
          <ArrayField field="variables">
            {({ add, arrayFields, addWithInitValue }) => (
              <React.Fragment>
                {arrayFields.map(({ field, key, remove }, i) => (
                  <div
                    key={key}
                    className="w-full flex flex-row gap-2 flex-wrap relative variable-row mb-2" // 添加底部边距
                  >
                    {/* 高亮覆盖层 */}
                    {highlightedIndex === i && (
                      <motion.div
                        className="absolute inset-0 bg-blue-400/30 dark:bg-blue-600/30 rounded-md z-10 pointer-events-none border-2 border-blue-500/50 dark:border-blue-400/50"
                        animate={{
                          opacity: [0.3, 0.7, 0.3],
                          scale: [1, 1.01, 1]
                        }}
                        transition={{
                          repeat: Infinity,
                          duration: 0.8
                        }}
                      />
                    )}

                    <div className="w-full flex-1 grid grid-cols-3 gap-2 items-center auto-rows-min">
                      {/* 调整每个输入框的宽度，确保总宽度不会超出容器 */}
                      <div className="w-full overflow-hidden">
                        <Form.Input
                          field={`${field}[key]`}
                          placeholder={"请输入变量名"}
                          noLabel={i !== 0}
                          label={i === 0 ? "变量名" : ""}
                          className="w-full"
                          rules={[
                            { required: true, message: "请输入变量名" },
                            {
                              pattern: /^[a-zA-Z0-9_]+$/,
                              message: "变量名只能包含字母、数字和下划线"
                            }
                          ]}
                        />
                      </div>
                      <div className="w-full overflow-hidden">
                        <Form.Input
                          field={`${field}[description]`}
                          label={i === 0 ? "描述" : ""}
                          noLabel={i !== 0}
                          placeholder={"请输入描述"}
                          className="w-full"
                        />
                      </div>
                      <div className="w-full overflow-hidden">
                        <Form.Input
                          field={`${field}[defaultValue]`}
                          label={i === 0 ? "默认值" : ""}
                          noLabel={i !== 0}
                          placeholder={"请输入默认值"}
                          className="w-full"
                        />
                      </div>
                    </div>

                    <Button
                      className={i === 0 ? "mt-[35px]" : "mt-[4px]"}
                      type="danger"
                      theme="borderless"
                      icon={<IconMinusCircle />}
                      onClick={() => {
                        remove();
                        handleFormValueChange();
                      }}
                    />
                  </div>
                ))}

                {/* 添加新变量按钮 */}
                <Button
                  theme="light"
                  size="large"
                  block
                  loading={isSaving}
                  type="primary"
                  icon={<IconPlusCircle />}
                  onClick={() => {
                    add();
                    handleFormValueChange();
                  }}
                  className="mt-4" // 添加顶部边距
                >
                  添加变量
                </Button>
              </React.Fragment>
            )}
          </ArrayField>
        </Form>
      </div>
    </Modal>
  );
}

