import { useAuth } from "@/components/Auth";
import NotionIcon from "@/components/icon/NotionIcon";
import RemoteIcon from "@/components/icon/RemoteIcon";
import { TextIcon } from "@/components/icon/TextIcon";
import { ResultEnum } from "@/enums/httpEnum";
import { getAccessToken } from "@/utils/auth";
import {
  Checkbox,
  Col,
  Divider,
  Form,
  Icon,
  Modal,
  Row,
  Steps,
  Toast,
  Upload,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { addKnowledge } from "@/api/platform/knowledge";
const KnowledgeForm: React.FC<KnowledgeFormProps> = ({
  open,
  onCancel,
  onOK,
}) => {
  const [current, setCurrent] = useState<number>(0);
  const selectDataSourceRef = React.useRef(null);

  const handleCancel = () => {
    setCurrent(0);
    onCancel();
  };
  const addFromDatasets = (values: any) => {
    return addKnowledge({
      name: values.name,
      description: values.description,
      ossId: values.files[0].response?.data?.ossId,
    })
      .then((res) => {
        Toast.success(res.msg);
        setCurrent(1);
        return Promise.resolve(res);
      })
      .catch((errr) => {
        return Promise.reject(errr);
      });
  };
  const handleOnOk = async () => {
    // @ts-expect-error
    return await selectDataSourceRef?.current
      .submitForm()
      .then(async (values) => {
        debugger;
        return await addFromDatasets(values);
        // setCurrent(current + 1);
      });
  };

  return (
    <>
      <Modal
        title="新增知识库"
        fullScreen
        className="semi-light-scrollbar"
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
          height: "60%",
        }}
        onOk={handleOnOk}
        visible={open}
        onCancel={handleCancel}
        closeOnEsc={false}
        okText={"下一步"}
        cancelText={"取消"}
      >
        <div className="px-2">
          <Steps
            type="basic"
            current={current}
            onChange={(i) => {
              Toast.warning("请选择数据源");
            }}
          >
            <Steps.Step
              title="填写基本信息"
              description="填写知识库基本信息，以及选择数据源"
            />
            <Steps.Step
              title="文本分段清洗"
              description="This is a description"
            />
            <Steps.Step title="处理" description="This is a description" />
          </Steps>
        </div>

        {current === 0 && (
          <div
            className="flex items-center  mt-2 overflow-auto justify-center"
            style={{ maxHeight: "calc(100vh - 44px+32px)" }}
          >
            <SelectDataSource ref={selectDataSourceRef} />
          </div>
        )}
        {current === 1 && (
          <div style={{ maxHeight: "calc(100vh - 44px+32px)" }}>
            <DataCleaning />
          </div>
        )}
      </Modal>
    </>
  );
};

const SelectDataSource: React.FC<any> = forwardRef((props, ref) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const formApiRef = React.useRef(null);

  const tabButtonData = [
    {
      label: "本地文件",
      icon: <TextIcon />,
      content:
        "已支持 TXT、 MARKDOWN、 MDX、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 MD、 HTM，每个文件不超过 15MB。",
    },
    // {
    //   label: "Notion文档数据",
    //   icon: <NotionIcon />,
    //   content: "同步 Notion ",
    // },
    // {
    //   label: "在线数据",
    //   icon: <RemoteIcon />,
    //   content: "获取在线网页内容",
    // },
  ];
  const handleTabClick = (index: number) => {
    setCurrentIndex(index);
  };
  const { Label, ErrorMessage } = Form;
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };

  const submitForm = async () => {
    if (formApiRef.current) {
      // @ts-expect-error
      return formApiRef.current
        ?.validate()
        .then(async (values: any) => {
          return Promise.resolve(values);
        })
        .catch((errors: any) => {
          return Promise.reject(errors);
        });
    }
  };

  useImperativeHandle(ref, () => ({
    submitForm,
  }));

  return (
    <div className="pl-2 py-4 flex flex-col gap-4 lg:w-[70%] md:w-[100%] xl:w-[50%] w-full">
      <Form getFormApi={getFormApi} style={{ padding: 10, width: "100%" }}>
        <Section text={"基本信息"}>
          <Row>
            <Col span={12}>
              <Form.Input
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="name"
                label="知识库名称"
                trigger="blur"
              />
            </Col>
            <Col span={12}></Col>
            <Col span={24}>
              <Form.TextArea
                field="description"
                label="知识库描述"
                rules={[{ required: true, message: "请填写知识库描述" }]}
                trigger="blur"
              />
            </Col>
          </Row>
        </Section>
        <Section text={"选择数据源"}>
          <Row>
            <Col span={24} className="mt-2">
              <Label>导入类型</Label>
              <div className="flex flex-row w-full  flex-wrap gap-5 mt-2">
                {tabButtonData.map((tab, index) => {
                  return (
                    <div
                      className={`flex  flex-row items-center gap-2 py-2 px-3 w-[200px] hover:bg-semi-color-fill-0 cursor-pointer h-[70px] rounded-lg `}
                      style={
                        currentIndex === index
                          ? {
                              border: "1px solid var(--semi-color-primary)",
                              background: "rgba(var(--semi-brand-5),0.1)",
                            }
                          : { border: "1px solid var(--semi-color-border)" }
                      }
                      onClick={() => handleTabClick(index)}
                    >
                      <Icon svg={tab.icon} />
                      <div className="text-base">{tab.label}</div>
                    </div>
                  );
                })}
              </div>
            </Col>
            <Col span={24}>
              <Form.Upload
                label="上传文本文件"
                action={
                  location.hash +
                  import.meta.env.VITE_GLOB_API_URL_PREFIX +
                  "/resource/oss/upload"
                }
                rules={[{ required: true, message: "请上传文件" }]}
                maxSize={15 * 1024 * 1024}
                limit={1}
                draggable={true}
                fileName="file"
                afterUpload={({ response, file }) => {
                  if (response.code == ResultEnum.SUCCESS) {
                    const { data } = response;
                    return {
                      autoRemove: false,
                      status: "success",
                      name: data.fileName,
                      url: data.url,
                      ...data,
                    };
                  }
                  return {};
                }}
                field="files"
                headers={{
                  clientid: import.meta.env.VITE_GLOB_CLIENT_ID,
                  Authorization: `Bearer ${getAccessToken()}`,
                }}
                dragMainText={"点击上传文件或拖拽文件到这里"}
                accept={
                  ".txt, .markdown, .mdx, .pdf, .html, .xlsx, .xls, .docx, .csv, .md, .html"
                }
                dragSubText="已支持 TXT、 MARKDOWN、 MDX、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 MD、 HTML，每个文件不超过 15MB。"
                style={{ marginTop: 10, marginBottom: 10 }}
              ></Form.Upload>
            </Col>
          </Row>
        </Section>
      </Form>
    </div>
  );
});

const DataCleaning: React.FC<any> = forwardRef((props, ref) => {
  return (
    <div className="py-4 lg:w-[70%] md:w-[100%] xl:w-[50%] w-full">
      <Form style={{ padding: 10, width: "100%" }}>
        <Section text={"分段设置"}>
          <Row gutter={10}>
            <Col span={8}>
              <Form.Input
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="name"
                label="分段标识符"
                trigger="blur"
              />
            </Col>
            <Col span={8}>
              <Form.Input
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="name"
                label="分段最大长度"
                trigger="blur"
              />
            </Col>

            <Col span={8}>
              <Form.Input
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="name"
                label="分段重叠长度"
                trigger="blur"
              />
            </Col>
            <Divider margin="12px" align="left">
              文本处理规则
            </Divider>
            <Col span={24}>
              <Form.CheckboxGroup
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="name"
                direction="horizontal"
                type="card"
                label="分段重叠长度"
                trigger="blur"
              >
                <Checkbox
                  style={{
                    width: 280,
                    border: "1px solid var(--semi-color-border)",
                  }}
                  value="admin"
                  extra="替换掉连续的空格、换行符和制表符"
                >
                  替换空格
                </Checkbox>
                <Checkbox
                  style={{
                    width: 280,
                    border: "1px solid var(--semi-color-border)",
                  }}
                  value="url"
                  extra="删除所有 URL 和电子邮件地址"
                >
                  删除URL
                </Checkbox>
              </Form.CheckboxGroup>
            </Col>
          </Row>
        </Section>
        <Section text={"Embedding 模型"}>
          <Row gutter={10}>
            <Col span={24}>
              <Form.Select
                rules={[{ required: true, message: "请填写知识库名称" }]}
                field="select"
                className="w-full"
                label="分段标识符"
                trigger="blur"
              />
            </Col>
          </Row>
        </Section>
      </Form>
    </div>
  );
});

export default KnowledgeForm;
