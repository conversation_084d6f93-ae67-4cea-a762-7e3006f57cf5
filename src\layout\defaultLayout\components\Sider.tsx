import {
  IconBell,
  IconBox,
  IconChevronLeft,
  IconChevronRight,
  IconChevronUpDown,
  IconCustomerSupport,
  IconExit,
  IconGridView,
  IconHome,
  IconInfoCircle,
  IconSetting,
  IconUndo,
  IconUser,
  IconUserGroup,
} from "@douyinfe/semi-icons";
import { IconConfig, IconIntro, IconToken } from "@douyinfe/semi-icons-lab";
import {
  Avatar,
  Button,
  Divider,
  Dropdown,
  DropdownItem,
  Layout,
  Nav,
  Popover,
} from "@douyinfe/semi-ui";
import { useNavigate } from "@tanstack/react-router";
import React, { Children, useEffect, useState } from "react";
import "./index.scss";
import { Logo } from "@/components/icon/Logo";
import SiderAvatar from "@/layout/components/SiderAvatar";
import AppList from "@/components/icon/AppList";
import classNames from "classnames";
import { findMenuByKey, System, SystemMenus } from "@/constants/systemMenus";
const LayoutSider: React.FC<SiderProps> = () => {
  const navigate = useNavigate();
  const [activeKey, setActiveKey] = useState<Array<string>>([]);

  useEffect(() => {
    setActiveKey([location.pathname]);
    const menu = findMenuByKey(SystemMenus, location.pathname);
    document.title = (menu?.label as string) || "LynkzHub";
  }, [location.pathname]);

  const jump = (key: string) => {
    setActiveKey([key]);
    navigate({
      to: key,
    });
  };




  return (
    <div className="h-full">
      <SiderNav
        jump={jump}
        activeKey={activeKey}
        menus={System}
        setActiveKey={setActiveKey}
      />
    </div>
  );
};

const SiderNav: React.FC<{
  activeKey: Array<string>;
  jump: (key: string) => void;
  menus: Array<MenuProps>;
  setActiveKey: (key: Array<string>) => void;
}> = ({ activeKey, jump, menus, setActiveKey }) => {
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [collapsed, setCollapsed] = useState<any>(false);
  const navigate = useNavigate();

  const renderMenuItems = (items: MenuProps[]): JSX.Element[] => {
    return items.map((item) => {
      if (item.children && item.children.length > 0) {
        return (
          <Nav.Sub
            key={item.key}
            itemKey={item.key}
            text={item.label}
            icon={item.icon}
          >
            {renderMenuItems(item.children)}
          </Nav.Sub>
        );
      } else {
        return (
          <Nav.Item
            key={item.key}
            itemKey={item.key}
            text={item.label}
            icon={item.icon}
          />
        );
      }
    });
  };
  return (
    <Nav
      className="h-full mt-2  relative  "
      // style={{ width: !collapsed ? "220px" : "60px" }}
      selectedKeys={activeKey}
      isCollapsed={collapsed}
      onSelect={(data: any) => {
        jump(data.itemKey as string);
        setActiveKey(data.selectedKeys as string[]);
      }}
      style={{background:"transparent",border:"none"}}
      footer={{
        style: {
          width: "100%",
        },
        collapseButton: true,
      }}
      onCollapseChange={(isCollapsed: boolean) => {
        setCollapsed(isCollapsed);
      }}
    >
      {renderMenuItems(menus)}
    </Nav>
  );
};

export default LayoutSider;
