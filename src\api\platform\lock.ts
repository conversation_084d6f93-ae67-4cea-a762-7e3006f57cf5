import { getAccessToken } from "@/utils/auth";
import { http } from "@/utils/axios";
const base_url = "/aigc/v1/lock";
import CryptoJS from "crypto-js";

// 存储当前标签页的唯一ID
let currentTabId: string | null = null;

/**
 * 获取编辑锁状态
 * @param resourceId 资源ID
 */
export async function getEditLock(resourceId: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${resourceId}`,
    method: "GET",
  });
}

/**
 * 获取编辑锁
 * @param resourceId 资源ID
 */
export async function acquireEditLock(resourceId: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${resourceId}`,
    method: "POST",
    headers: {
      'X-Tab-ID': getTabId()
    }
  });
}

/**
 * 强制获取编辑锁
 * @param resourceId 资源ID
 */
export async function forceAcquireEditLock(resourceId: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${resourceId}/force`,
    method: "POST",
    headers: {
      'X-Tab-ID': getTabId()
    }
  });
}

/**
 * 刷新编辑锁
 * @param resourceId 资源ID
 */
export async function refreshEditLock(resourceId: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${resourceId}/refresh`,
    method: "PUT",
    headers: {
      'X-Rquest-ID': getTabId()
    }
  });
}

/**
 * 释放编辑锁
 * @param resourceId 资源ID
 */
export async function releaseEditLock(resourceId: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${resourceId}`,
    method: "DELETE",
    headers: {
      'X-Rquest-ID': getTabId()
    }
  });
}

export function getSessionId() {
  //转MD5
  const token = getAccessToken();
  return CryptoJS.MD5(token);
}

/**
 * 获取标签页ID
 * 使用内存中的变量来存储标签页ID，确保每个标签页都有唯一的ID
 */
export function getTabId() {
  if (!currentTabId) {
    // 生成一个随机ID
    currentTabId = generateRandomId();
    
    // 在开发环境下打印，便于调试
    if (process.env.NODE_ENV === 'development') {
      console.log('Generated new tab ID:', currentTabId);
    }
    
    // 注册一个广播通道，用于跨标签页通信
    setupBroadcastChannel();
  }
  
  return currentTabId;
}

/**
 * 生成随机ID
 */
function generateRandomId() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15) + 
         Date.now().toString(36);
}

/**
 * 设置广播通道，用于跨标签页通信
 */
function setupBroadcastChannel() {
  // 创建一个广播通道
  const channel = new BroadcastChannel('edit-lock-channel');
  
  // 当收到消息时
  channel.onmessage = (event) => {
    // 如果收到ping消息，回复当前标签页ID
    if (event.data.type === 'ping') {
      channel.postMessage({
        type: 'pong',
        tabId: currentTabId
      });
    }
  };
  
  // 页面卸载时关闭通道
  window.addEventListener('beforeunload', () => {
    channel.close();
  });
}

/**
 * 获取所有活动的标签页ID
 * 可用于检测同一资源在其他标签页中是否打开
 */
export async function getActiveTabIds(): Promise<string[]> {
  return new Promise((resolve) => {
    const activeTabIds: string[] = [];
    const channel = new BroadcastChannel('edit-lock-channel');
    
    // 发送ping消息
    channel.postMessage({
      type: 'ping'
    });
    
    // 设置超时，等待所有标签页响应
    const timeout = setTimeout(() => {
      channel.close();
      resolve(activeTabIds);
    }, 500);

    
    // 收集响应
    channel.onmessage = (event) => {
      if (event.data.type === 'pong' && event.data.tabId !== currentTabId) {
        activeTabIds.push(event.data.tabId);
      }
    };
  });
}
