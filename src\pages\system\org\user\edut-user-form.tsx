import { editUser, getUser } from '@/api/system/user';
import { Col, Form, Modal, Row, Spin, Toast } from '@douyinfe/semi-ui'
import React, { useEffect } from 'react'

export default function EditUserForm({ open, onCancel, depts, id, onRefresh }: any) {
    const formApiRef = React.useRef(null);
    const [loading, setLoading] = React.useState(false);
    const [loadLoading, setLoadLoading] = React.useState(false);
    const featchUserData = () => {
        setLoadLoading(true)
        getUser(id).then(({ data }) => {
            const { user } = data;
            setTimeout(() => {
                formApiRef.current?.setValues({
                    nickName: user.nickName,
                    phoneNumber: user.phonenumber,
                    userName: user.userName,
                    email: user.email,
                    sex: user.sex,
                    deptId: user.deptId,
                })
            }, 100)

        }).finally(() => { setLoadLoading(false) })
    }
    useEffect(() => {
        if (open) {
            featchUserData()
        }
    }, [open])
    const getFormApi = (formApi: any) => {
        formApiRef.current = formApi;
    };
    const handleReset = () => {
        // @ts-expect-error
        formApiRef.current?.reset();
        onCancel();
    };
    const handleEditSubmit = async (values: any) => {
        return await editUser({
            ...values,
            userId: id
            // roleIds: values.roleIds.map((item: any) => item.value)
        }).then((res: any) => {
            Toast.success(res.msg)
            onRefresh();
            handleReset();
            return Promise.resolve(res)
        }).catch((err: any) => {
            return Promise.reject(err)
        })
    };
    const submitForm = async () => {
        try {
            setLoading(true);
            // @ts-expect-error
            const values = await formApiRef.current?.validate();
            await handleEditSubmit(values);
        } catch (errors) {
            console.error(errors);
        } finally {
            setLoading(false);
        }
    }

    return (
        <Modal
            width={700}
            visible={open}
            title={"编辑用户"}
            centered
            className="semi-light-scrollbar"
            bodyStyle={{
                // height: 400,
                overflowY: "auto",
                overflowX: "hidden", // 隐藏水平滚动条，防止滚动条影响布局
                // margin: "0",
                position: "relative",
                // padding: 0
            }}
            okButtonProps={{
                disabled: loadLoading,
            }}
            closeOnEsc={false}
            onOk={submitForm}
            onCancel={handleReset}
            confirmLoading={loading}
        >
            <div className='w-full h-full'>
                {
                    loadLoading ? <div className='flex flex-col items-center w-full h-[300px] justify-center'>
                        <Spin spinning size="large" />
                        <span className="mt-2 text-gray-500 text-sm">加载中...</span>
                    </div>
                        : <Form getFormApi={getFormApi}>
                            <Row gutter={10}>
                                <Col span={24}>
                                    <Form.TreeSelect placeholder={"请选择所属部门"}
                                        filterTreeNode className='w-full'
                                        showClear
                                        rules={[
                                            { required: true, message: "所属部门不能为空" },
                                        ]}
                                        treeData={depts} fieldClassName='w-full' label="所属部门" field='deptId'></Form.TreeSelect>
                                </Col> <Col span={12}>
                                    <Form.Input
                                        rules={[
                                            { required: true, message: "请输入账号名" },
                                            {
                                                min: 1,
                                                max: 30,
                                                message: "用户账号长度不能超过30个字符",
                                            },
                                            {
                                                pattern: /^[^<>&"']+$/,
                                                message: "用账号名不能包含特殊字符",
                                            }
                                        ]}
                                        placeholder={"请输入账号名"} label="账号名" field='userName'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input field='nickName' label="昵称" placeholder="请输入昵称" rules={[
                                        { required: true, message: "用户昵称不能为空" },
                                        {
                                            min: 1,
                                            max: 30,
                                            message: "用户昵称长度不能超过30个字符",
                                        },
                                        {
                                            pattern: /^[^<>&"']+$/,
                                            message: "用户昵称不能包含特殊字符",
                                        }
                                    ]} />
                                </Col>
                                <Col span={12}>
                                    <Form.Input
                                        placeholder={"请输入手机号"} label="手机号" field='phoneNumber'></Form.Input>
                                </Col>
                                <Col span={12}>
                                    <Form.Input rules={[
                                        { required: true, message: "请输入邮箱" },
                                        {
                                            min: 1,
                                            max: 50,
                                            message: "邮箱长度不能超过50个字符",
                                        },
                                        {
                                            pattern: /^[^<>&"']+$/,
                                            message: "邮箱不能包含特殊字符",
                                        },
                                        {
                                            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                            message: "邮箱格式不正确",
                                        }
                                    ]} placeholder={"请输入用户昵称"} label="邮箱" field='email'></Form.Input>
                                </Col>
                                <Col span={24}>
                                    <Form.RadioGroup
                                        rules={[{ required: true, message: "用户性别不能为空" }]}
                                        initValue={"0"} field="sex" label='性别'>
                                        <Form.Radio value="0">男</Form.Radio>
                                        <Form.Radio value="1">女</Form.Radio>
                                    </Form.RadioGroup>
                                </Col>
                            </Row>
                        </Form>
                }

            </div>

        </Modal>
    )
}
