// src/store/modules/useSse.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { nanoid } from 'nanoid';
import { chatStream } from '@/api/platform/chat';

type MessageContent =
    | string
    | {
        type: string;
        text: string;
        image_url?: undefined;
    }
    | {
        type: string;
        image_url: { url: string };
        text?: undefined;
    }[];

interface Message {
    role: string;
    id: string;
    createAt: number;
    content: MessageContent;
    status?: string;
    promptTokens?: number;
    completionTokens?: number;
    totalTokens?: number;
}

interface SSEState {
    messages: Message[];
    isOpen: boolean;
    conversationId: string;
    currentBotId: string;
    error: any | null;
    loading: boolean;

    openConnection: (params: { prompt: string; botId: string }) => void;
    closeConnection: () => void;
    handleMessage: (data: any) => void;
    deleteMessage: (messageId: string) => void;
    clearMessages: () => void;
    clearError: () => void;
    addSystemMessage: (content: string) => void;
}

export const useSSEStore = create<SSEState>()(
    persist(
        (set, get) => {
            let abortController: AbortController | null = null;

            // 获取或生成新的 conversationId
            let conversationId = localStorage.getItem('conversationId');
            if (!conversationId) {
                conversationId = nanoid();
                localStorage.setItem('conversationId', conversationId);
            }

            return {
                messages: [],
                isOpen: false,
                conversationId,
                currentBotId: '',
                error: null,
                loading: false,

                openConnection: async ({ prompt, botId }) => {
                    if (get().isOpen) get().closeConnection();
                    set({ isOpen: true, currentBotId: botId, error: null, loading: true });
                    abortController = new AbortController();
                    const stream = chatStream({
                        prompt,
                        conversationId: get().conversationId,
                        botId,
                    })
                    try {
                        for await (const event of stream) {
                            console.log(event);
                        }
                    } catch (error) {
                        console.log(error);

                    }
                    // try {
                    // for await (const event of await chatStream({
                    //     prompt,
                    //     conversationId: get().conversationId,
                    //     botId,
                    // })) {
                    //         if (event.event === 'message' && event.data) {
                    //             try {
                    //                 const parsedData = JSON.parse(event.data);
                    //                 get().handleMessage(parsedData);
                    //             } catch (parseError) {
                    //                 console.error('Failed to parse message:', parseError, 'Raw data:', event.data);

                    //                 // 尝试 fallback 处理
                    //                 get().handleMessage({
                    //                     message: {
                    //                         id: nanoid(),
                    //                         content: event.data,
                    //                         role: 'assistant',
                    //                     },
                    //                     metadata: {
                    //                         status: 'completed'
                    //                     }
                    //                 });
                    //             }
                    //         }
                    //     }
                    // } catch (error) {
                    //     if (error.name !== 'AbortError') {
                    //         set({ error, isOpen: false, loading: false });
                    //         console.error('SSE error:', error);
                    //     }
                    // } finally {
                    //     set({ isOpen: false, loading: false });
                    //     abortController = null;
                    // }
                },

                closeConnection: () => {
                    if (abortController) {
                        abortController.abort();
                        abortController = null;
                    }
                    set({ isOpen: false, loading: false });
                },

                handleMessage: (data) => {
                    try {
                        const respMessage = data?.message || {};
                        const respMetadata = data?.metadata || {};

                        set((state) => {
                            const lastMessage = state.messages[state.messages.length - 1];

                            if (!respMessage.id) {
                                console.warn('Received message without ID', respMessage);
                                return state;
                            }

                            const messageTime = respMessage.createAt || Date.now();

                            let mergedContent: MessageContent = respMessage.content;
                            if (lastMessage && lastMessage.id === respMessage.id) {
                                if (typeof lastMessage.content === 'string' && typeof respMessage.content === 'string') {
                                    mergedContent = lastMessage.content + respMessage.content;
                                } else if (Array.isArray(lastMessage.content) && Array.isArray(respMessage.content)) {
                                    mergedContent = [...lastMessage.content, ...respMessage.content];
                                }
                            }

                            if (!lastMessage || lastMessage.id !== respMessage.id) {
                                return {
                                    ...state,
                                    messages: [
                                        ...state.messages,
                                        {
                                            ...respMessage,
                                            ...respMetadata,
                                            createAt: messageTime,
                                        },
                                    ],
                                };
                            }

                            return {
                                ...state,
                                messages: [
                                    ...state.messages.slice(0, -1),
                                    {
                                        ...lastMessage,
                                        ...respMessage,
                                        ...respMetadata,
                                        createAt: lastMessage.createAt || messageTime,
                                        content: mergedContent,
                                    },
                                ],
                            };
                        });

                        if (data?.metadata?.status === 'completed') {
                            get().closeConnection();
                        }
                    } catch (error) {
                        console.error('Error handling SSE message:', error);
                        set({ error });
                    }
                },

                deleteMessage: (messageId) =>
                    set((state) => ({
                        messages: state.messages.filter((msg) => msg.id !== messageId),
                    })),

                clearMessages: () => set({ messages: [] }),

                clearError: () => set({ error: null }),

                addSystemMessage: (content) =>
                    set((state) => ({
                        messages: [
                            ...state.messages,
                            {
                                role: 'system',
                                id: `system-${Date.now()}`,
                                createAt: Date.now(),
                                content,
                            },
                        ],
                    })),
            };
        },
        {
            name: 'sse-chat-storage',
            partialize: (state) => ({
                messages: state.messages,
                conversationId: state.conversationId,
            }),
        }
    )
);