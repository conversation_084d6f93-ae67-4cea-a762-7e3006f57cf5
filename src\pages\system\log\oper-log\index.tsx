import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import {
  IconCaretdown,
  IconDelete,
  IconEdit,
  IconMore,
  IconSearch,
  IconSmallTriangleDown,
  IconSync,
} from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Card,
  Col,
  DatePicker,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Select,
  Table,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { VChart } from "@visactor/react-vchart";
import { useTable } from "@/hooks/useTables";
import {
  clean,
  countOperLogStatus,
  deleteLog,
  getOperLogList,
  statsOperLogs,
} from "@/api/log/oper-log";
import { commonSpec, pieSpec } from "./constants";
import OperationLogDetails from "./components/oper-log-details";
import ErrorStack from "./components/error-stack";

export default function OperationLog() {
  const [open, setOpen] = useState(false);
  const [errorStackOpen, setErrorStackOpen] = useState(false);
  const [errorStack, setErrorStack] = useState("");
  const [operateData, setOperateData] = useState(undefined);
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_oper_type",
    "sys_oper_status",
  ]);
  const [filterOption, setFilterOption] = useState<any>([]);
  useEffect(() => {
    loadDictionary();
  }, []);

  useEffect(() => {
    if (dictionaryData?.sys_oper_status) {
      setFilterOption(() => {
        return dictionaryData?.sys_oper_status.map((item: any) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          };
        });
      });
    }
  }, [dictionaryData?.sys_oper_status]);
  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getOperLogList,
    defaultPageSize: 15,
    params: {},
    columns: [
      {
        title: "操作模块",
        dataIndex: "title",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        dataIndex: "operName",
        title: "操作人员",
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "操作类别",
        dataIndex: "operatorType",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_oper_type"
              dictValue={text}
              dictionaryData={dictionaryData.sys_oper_type || []}
            />
          );
        },
      },
      {
        title: "操作部门",
        dataIndex: "deptName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "IP地址",
        dataIndex: "operIp",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作地点",
        dataIndex: "operLocation",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "方法耗时",
        dataIndex: "costTime",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text + " ms" : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              type="badge"
              dictType="sys_oper_status"
              dictValue={text}
              dictionaryData={dictionaryData.sys_oper_status || []}
            />
          );
        },
      },
      {
        dataIndex: "operTime",
        title: "操作日期",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button
                theme="borderless"
                onClick={() => {
                  setOperateData(row);
                  setOpen(true);
                }}
              >
                详情
              </Button>

              <Dropdown
                className="min-w-[120px]"
                zIndex={1000}
                position="bottom"
                trigger={"click"}
                render={
                  <Dropdown.Menu>
                    {row?.status == 1 && (
                      <Dropdown.Item
                        icon={<IconSearch />}
                        onClick={() => {
                          setErrorStack(row.errorStack);
                          setErrorStackOpen(true);
                        }}
                      >
                        堆栈信息
                      </Dropdown.Item>
                    )}
                    <Dropdown.Item
                      type="danger"
                      icon={<IconDelete />}
                      onClick={() => handleRemoveDictData(row.operId)}
                    >
                      删除
                    </Dropdown.Item>
                  </Dropdown.Menu>
                }
              >
                <Button
                  type="tertiary"
                  theme="borderless"
                  icon={<IconMore className="cursor-pointer" />}
                ></Button>
              </Dropdown>
            </div>
          );
        },
      },
    ],
  });

  const [barChartData, setBarChartData] = useState<any>([]);
  const [barChartFileter, setBarChartFilter] = useState<any>([]);
  const getOperLogStats = () => {
    let params = [] as any;
    if (barChartFileter !== null || barChartFileter.length == 2) {
      params = {
        startDate: barChartFileter[0],
        endDate: barChartFileter[1],
      };
    }
    statsOperLogs(params).then(({ data }) => {
      // console.log(data)
      const tempData: { type: string; date: any; value: any }[] = [];
      data.logs.forEach((item) => {
        tempData.push({
          type: "操作成功",
          date: item.day,
          value: item.success,
        });
        tempData.push({
          type: "操作失败",
          date: item.day,
          value: item.error,
        });
      });
      setBarChartData(tempData);
    });
  };
  useEffect(() => {
    getOperLogStats();
  }, [barChartFileter]);

  const barChartConfig = useMemo(() => {
    return {
      ...commonSpec,
      xField: ["date", "type"],
      yField: "value",
      direction: "vertical",
      legends: {
        visible: true,
        orient: "top",
        position: "end",
      },
      data: [
        {
          id: "id0",
          values: barChartData,
        },
      ],
    };
  }, [barChartData]);

  const [pieChartData, setPieChartData] = useState<any>([]);
  const getLoginCount = () => {
    countOperLogStatus().then(({ data }) => {
      setPieChartData([
        { type: "操作成功", value: data.success },
        { type: "操作失败", value: data.error },
      ]);
    });
  };
  useEffect(() => {
    getLoginCount();
  }, []);

  const pieConfig = useMemo(() => {
    return {
      ...pieSpec,
      data: [
        {
          id: "id0",
          values: pieChartData,
        },
      ],
    };
  }, [pieChartData]);

  const handleClear = (type: string) => {
    const modal = Modal.warning({
      title: "确认删除数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await clean(type as string)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            resetSearchParams();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  const handleRemoveDictData = (id: string) => {
    const modal = Modal.warning({
      title: "确认删除操作数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await deleteLog(id as string)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            resetSearchParams();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  return (
    <div className="w-full flex gap-2 flex-col h-full py-2 px-2 sm:overflow-auto rounded-md">
      <Row gutter={12} className="w-full">
        <Col span={16}>
          <Card
            bordered={false}
            header={
              <div className="flex flex-row items-center justify-between">
                <div className="text-lg font-semibold">登录统计</div>
                <div>
                  <DatePicker
                    type="dateRange"
                    insetInput
                    onChange={(date, dateString) => {
                      setBarChartFilter(dateString);
                    }}
                    style={{ width: "100%" }}
                  />
                </div>
              </div>
            }
            bodyStyle={{ padding: "0 10px 10px 10px" }}
            headerLine={false}
          >
            <VChart
              spec={barChartConfig}
              options={{ mode: "desktop-browser" }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            bordered={false}
            header={
              <div className="flex flex-row items-center justify-between">
                <div className="text-lg font-semibold">操作日志</div>
              </div>
            }
            bodyStyle={{ padding: "0 10px 10px 10px" }}
            headerLine={false}
          >
            <VChart spec={pieConfig} options={{ mode: "desktop-browser" }} />
          </Card>
        </Col>
      </Row>
      <div className="sm:overflow-auto rounded-lg overflow-hidden h-full flex gap-2 flex-col bg-semi-color-white w-full px-2 py-2">
        <div className="flex  flex-row items-center justify-between gap-2 py-2 px-2">
          <div className="text-lg font-semibold">操作日志</div>
          <div className="flex flex-row items-center gap-2">
            <Select
              className="w-[200px]"
              placeholder="操作状态"
              showClear
              onChange={(value) => {
                console.log(value);

                setSearchParams({
                  status: value,
                });
                refresh();
              }}
              value={searchParams.status}
              optionList={filterOption}
            />
            <Dropdown
              position="bottomLeft"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleClear("0")}>
                    保留一个月
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("1")}>
                    保留三个月
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("2")}>
                    保留一年
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("3")}>
                    保留一万条
                  </Dropdown.Item>
                  <Dropdown.Item type="danger" onClick={() => handleClear("4")}>
                    清空所有
                  </Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                theme="solid"
                type="warning"
                iconPosition="right"
                icon={<IconSmallTriangleDown />}
              >
                批量处理
              </Button>
            </Dropdown>
            <Tooltip content="刷新列表">
              <Button
                type="tertiary"
                icon={<IconSync />}
                onClick={() => refresh()}
              />
            </Tooltip>
          </div>
        </div>
        <div className="flex-1 sm:overflow-hidden h-full">
          <Table
            rowSelection={rowSelection as any}
            columns={columns}
            className="h-full"
            dataSource={dataSource}
            loading={loading}
            size="small"
            scroll={{ x: 962, y: "calc(100vh - 520px)" }}
            pagination={pagination}
            rowKey="operId"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
          />
        </div>
      </div>
      <OperationLogDetails
        open={open}
        onCancel={() => {
          setOpen(false);
          setOperateData(undefined);
        }}
        status={dictionaryData.sys_oper_status}
        data={operateData}
      />
      <ErrorStack
        open={errorStackOpen}
        errorStack={errorStack}
        onCancel={() => {
          setErrorStack("");
          setErrorStackOpen(false);
        }}
      />
    </div>
  );
}
