import AuthLayout from "../component/AuthLayout";
import React from 'react'
import {
  Avatar,
  Button,
  Col,
  Divider,
  Form,
  Modal,
  Row,
  Spin,
  TabPane,
  Tabs,
  Toast,
  Typography,
} from "@douyinfe/semi-ui";
import { LocalForageService as storage } from "@/utils/storage";
import Icon, {
  IconCamera,
  IconGithubLogo,
  IconLock,
  IconTiktokLogo,
  IconUser,
  IconUserCircle,
} from "@douyinfe/semi-icons";
import useBoolean from "@/hooks/useBoolean";
import { login } from "@/api";
import { useState } from "react";
import './styles.scss'
// import { useAuth } from "@/components/Auth";
import { useLocation, useNavigate } from "@tanstack/react-router";
import { useAuthStore, useGlobalStore, useLoadingStore } from "@/store";
import { captcha as getCaptcha } from '@/utils/captcha';
import { featchCaptchaConfig } from "@/api/captcha";
import { Github } from "lucide-react";
import WxIcon from "@/components/icon/WxIcon";
function Login() {
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const { fetchUserInfo } = useGlobalStore.getState();

  const navigate = useNavigate();
  const location = useLocation();
  const { showLoading, hideLoading } = useLoadingStore();
  const loginParams = {
    // @ts-expect-error
    clientId: import.meta.env.VITE_GLOB_CLIENT_ID,
    // @ts-expect-error
    grantType: import.meta.env.VITE_GLOB_GRANT_TYPE,
    // @ts-expect-error
    tenantId: import.meta.env.VITE_GLOB_DEFAULT_TENANT_ID,
  };
  const [open,setOpen]= useState(false)
  const { setTokenData } = useAuthStore();
  const [captchaConfig, setCaptchaConfig] = useState<API.CaptchaConfig | null>(null);
  //获取验证码配置
  
  React.useEffect(() => { 
    getCpatchaConfig();
  }, []);

  const getCpatchaConfig = ()=>{
    featchCaptchaConfig().then(({data})=>{
      setCaptchaConfig(data);
    })
  }


  const featchLogin = async (values:any) => {
    setLoading();
    await login({
      ...loginParams,
      username: values.username,
      password: values.password,
    })
      .then(async (res) => {
        // showLoading(); // 显示 loading
        setTokenData(res.data);
        closeLoading();
        Toast.success("登录成功");

        const searchParams = new URLSearchParams(location.search);
        const redirect = searchParams.get("redirect") || "/";
        navigate({
          to: redirect,
        });
        // hideLoading(); // 跳转后隐藏 loading
      })
      .catch(() => {
        closeLoading();
      });
  };
  const handleSubmit = async (values: any) => {
    const enabled = captchaConfig['sys.captcha.enable'] === 'true' ? true :false;

    if(enabled){
      setOpen(true);
      setTimeout(() => {
        getCaptcha(
          {
            bindEl: "#box",
          },
          {
            sliderTitleText: "拖动滑块完成拼图",
            theme: "night"
          },
          async (res) => {
            setOpen(false);
            await featchLogin(values);
            // console.log(res.data.id);
            // showModal.value = false;
            // await validate();
            // await authStore.login(model.username, model.password, model.code, res.data.id, fetchCaptchCode);
          },
          () => {},
          () => {
            setOpen(false);
          }
        );
      }, 100);
    }else{
      await featchLogin(values);
    }
   
  };
  return (
    <AuthLayout>
     <Modal
     className="auth-box"
        // title="基本对话框"
        header={null}
        closable={false}
        centered
        footer={null}
        bodyStyle={{ padding: 0 ,backgroundColor:"transparent"}}
        // onOk={handleOk}
        visible={open}
        closeOnEsc={true}
      >
        <div id="box"></div>
      </Modal>
      <div className="mb-0 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-foreground mb-3 text-2xl font-bold leading-9 tracking-tight lg:text-3xl">
          嗨，近来可好
        </div>
        <div className="text-semi-color-text-1 lg:text-md text-sm">
          👋 欢迎来到 LynkzHub
        </div>
      </div>
      <Tabs>
        <TabPane tab="账号登录" itemKey="user">
          <div className="w-full items-center flex ">
            <Form
              initValues={{
                username: "admin",
                password: "admin123",
                agree: true,
              }}
              className="w-full"
              aria-required={false}
              onSubmit={(values) => handleSubmit(values)}
            >
              {({ formState, values, formApi }) => (
                <Row>
                  <Col span={24}>
                    <Form.Input
                      field="username"
                      prefix={<IconUser />}
                      label={{
                        required: false,
                        text: "用户名",
                      }}
                      size="large"
                      placeholder="请输入用户名"
                      rules={[
                        { required: true, message: "请输入用户名" },
                        { type: "string", message: "请输入用户名" },
                      ]}
                    ></Form.Input>
                  </Col>
                  <Col span={24}>
                    <Form.Input
                      prefix={<IconLock />}
                      field="password"
                      fieldClassName="bg-semi-color-white"
                      mode="password"
                      size="large"
                      label={{
                        required: false,
                        text: (
                          <div className="flex justify-between">
                            <div>密码</div>
                            <Typography.Text link={{ href: "$" }}>
                              忘记密码
                            </Typography.Text>
                          </div>
                        ),
                      }}
                      placeholder="请输入密码"
                      rules={[
                        { required: true, message: "请输入密码" },
                        { type: "string", message: "请输入密码" },
                      ]}
                    ></Form.Input>
                  </Col>

                  <Col span={24}>
                    <Form.Checkbox field="agree" noLabel>
                      我已阅读并同意服务条款
                    </Form.Checkbox>
                  </Col>
                  <Col span={24}>
                    <Button
                      size="large"
                      loading={loading}
                      disabled={!values.agree}
                      theme="solid"
                      htmlType="submit"
                      block
                      type="primary"
                    >
                      登录
                    </Button>
                  </Col>
                </Row>
              )}
            </Form>
          </div>
        </TabPane>
        <TabPane tab="手机登录" itemKey="phone">
          手机登录
        </TabPane>
      </Tabs>

      <Divider margin="20px" style={{ fontWeight: 400 }} align="center">
        <div className="text-semi-color-text-1 text-sm">其他方式登录</div>
      </Divider>
      <div className="flex flex-row justify-center gap-5">
        <Button theme="borderless" icon={<Icon className="text-green" svg={<WxIcon  />}/>} aria-label="微信登录" />
        <Button theme="borderless"  icon={<IconGithubLogo  className="text-black" />} aria-label="github登录" />
        <Button theme="borderless" icon={<IconTiktokLogo />} aria-label="抖音登录" />
      </div>
      
      
    </AuthLayout>
  );
}
export default Login;
