import { <PERSON><PERSON>, Tooltip } from "@douyinfe/semi-ui";
import React, { act, useEffect, useState } from "react";
import AvatarMenu from "../components/AvatarMenu";
import Icon, { IconPlusCircle, IconSearchStroked } from "@douyinfe/semi-icons";
import classNames from "classnames";
import {
    MessageSquare,
    Cable,
    FolderCog,
    ChevronLeft,
    ChevronRight,
    Book,
    SquareTerminal,
    Wrench,
    TvMinimal,
} from "lucide-react";
import Typography from "@douyinfe/semi-ui/lib/es/typography/typography";
import { findPlateMenu, PlateMenus } from "@/constants/systemMenus";
import IconNavMenu from "./component/IconNavMenu";
import { useNavigate } from "@tanstack/react-router";
import { getDeepestChild, getTitleFromMenu } from "../utils/navigationUtils";
import Search from "../components/Search";
import { Logo } from "@/components/icon/Logo";

export default function PlateFormLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const navigate = useNavigate();
    const [activeKey, setActiveKey] = useState<string>(""); // Active top-level menu
    const [subMenus, setSubMenus] = useState<MenuProps[]>([]); // Sub-menu items
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [menuActiveKey, setMenuActiveKey] = useState<string>("");
    // Menu click handler
    const handleMenuClick = (menu: MenuProps) => {
        setActiveKey(menu?.key);
        if (menu?.children) {
            const deepestChild = getDeepestChild(menu);
            setSubMenus(menu?.children);
            navigate({ to: deepestChild.key });
        } else {
            setSubMenus([]);
            navigate({ to: menu.key });
        }
        document.title = getTitleFromMenu(menu);
    };
    const jump = (key: string) => {
        navigate({
            to: key,
        });
        setMenuActiveKey(key);
    };

    // In PlateFormLayout.tsx
    useEffect(() => {
        const path = location.pathname;
        console.log(path);
        // ✅ Use location from React Router
        const currentMenu = findPlateMenu(path);

        if (currentMenu) {
            setActiveKey(currentMenu.key);

            // @ts-expect-error
            if (currentMenu.children?.length > 0) {
                // @ts-expect-error
                const matchedSubmenu = currentMenu.children.find(
                    item => item.key === path
                    // @ts-expect-error
                ) || currentMenu.children[0];
                // @ts-expect-error
                setSubMenus(currentMenu.children);
                setMenuActiveKey(matchedSubmenu?.key || '');
            } else {
                setSubMenus([]);
                setMenuActiveKey('');
            }
        }
    }, [location.pathname, activeKey]);

    return (
        <div className="h-full w-full overflow-hidden flex flex-row bg-gray-50 relative">
            {/* 小图标导航区 */}
            <div className="w-[60px] h-full flex flex-col gap-2 border-r py-2 pt-4 border-solid border-semi-color-border relative">
                {/* 头像区域 */}
                <div className="w-full h-[60px] flex items-center justify-center">
                    {/* <Avatar
                        className="hover:border hover:border-solid hover:border-semi-color-border box-border"
                        shape="square"
                        size="small"
                        src="https://registry.npmmirror.com/@lobehub/fluent-emoji-3d/latest/files/assets/1f60b.webp"
                    /> */}
                    <div className="flex items-center justify-center h-[40px] w-[40px] rounded-full cursor-pointer">
                       <Icon svg={ <Logo />} size="extra-large" />
                    </div>
                </div>

                {/* 图标导航区域 */}
                <div className="flex flex-col flex-1 items-center gap-2 px-1">
                    {PlateMenus.map((menu) => (
                        <IconNavMenu
                            key={menu.key}
                            menu={menu}
                            activeKey={activeKey}
                            onClick={handleMenuClick}
                        />
                    ))}
                </div>

                {/* 底部搜索和用户头像菜单 */}
                <div className="items-center flex-col gap-2 flex w-full justify-center h-[90px]">
                    <Search />
                    <AvatarMenu showSystemMenu />
                </div>
            </div>
            {/* 左侧固定菜单区域 */}
            {subMenus.length > 0 && (
                <div
                    className={`${isCollapsed ? "w-[0]" : "w-[260px]"} h-full flex flex-row transition-all duration-300 ease-in-out relative`}
                >
                    {/* 菜单内容区域 + 悬浮按钮容器 */}
                    <div
                        className={`h-full border-r border-solid border-semi-color-border transition-all duration-300 ease-in-out overflow-hidden ${isCollapsed ? "w-0" : "w-full"
                            }`}
                    >
                        <div className="px-4 py-2 flex flex-col gap-4 h-full whitespace-nowrap">
                            <div className="pt-6 flex items-start flex-col gap-1 ">
                                <div className="text-2xl font-bold">资源</div>
                                <div className="text-sm text-semi-color-text-2">
                                    智能体所需相关资源
                                </div>
                            </div>

                            <div className="flex-1 flex flex-col gap-1">
                                {subMenus.map((item) => (
                                    <div
                                        onClick={() => jump(item.key)}
                                        key={item.key}
                                        className={classNames(
                                            "flex  text-semi-color-text-0 items-center p-2 rounded-md cursor-pointer gap-2 hover:bg-semi-color-fill-0",
                                            menuActiveKey === item.key
                                                ? "bg-semi-color-fill-0 "
                                                : ""
                                        )}
                                    >
                                        {item.icon}
                                        <div className="text-sm text-semi-color-text-0">{item.label}</div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    {/* 悬浮按钮（始终悬浮在菜单内容区域右侧） */}
                    <div
                        className="absolute top-0 -right-3  bottom-0 w-3 flex items-center justify-center "
                        style={{ pointerEvents: "none", background: "transparent" }}
                    >
                        <div
                            onClick={() => setIsCollapsed((prev) => !prev)}
                            className="absolute bg-semi-color-fill-0 z-50 w-3 h-12 rounded-r-md cursor-pointer flex items-center justify-center shadow-md transition-colors duration-200"
                            style={{ pointerEvents: "auto" }} // Re-enable click on button
                        >
                            <Icon
                                className="text-semi-color-text-1 "
                                svg={
                                    isCollapsed ? (
                                        <ChevronRight size="16px" />
                                    ) : (
                                        <ChevronLeft size="16px" />
                                    )
                                }
                                size="small"
                            />
                        </div>
                    </div>
                </div>
            )}

            {/* 主内容区域 */}
            <div className="flex-1 overflow-auto">{children}</div>
        </div>
    );
}
