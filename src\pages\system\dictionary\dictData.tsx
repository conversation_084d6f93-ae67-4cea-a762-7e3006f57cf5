import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import { IconDelete, IconEdit, IconMore } from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Dropdown,
  Empty,
  Input,
  Modal,
  Table,
  Toast,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState } from "react";
import DictDataForm from "./components/dictDataForm";
import { useBoolean } from "@/hooks";
import {
  dictDataFormTitle,
  getBgClass,
  getDictListClass,
} from "./constants/dictonary";
import classNames from "classnames";
export default function DictData({ selectedDict }: any) {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(15);
  const [total, setTotal] = useState(0);
  const [dataSource, setData] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [formOpen, { setTrue, setFalse }] = useBoolean();
  const [formTitle, setFormTitle] = useState(dictDataFormTitle[0]);
  const [formId, setFormId] = useState<any>(undefined);
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
  ]);
  useEffect(() => {
    loadDictionary();
  }, []);
  const handleRemoveDictData = (dictCode: string) => {
    const modal = Modal.warning({
      title: "确认删除字典数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await delDictData(dictCode as string)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            fetchData(page, pageSize, {
              dictType: selectedDict?.dictType,
            });
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
        // 刷新字典数据
        // 刷新缓存
      },
    });
  };

  const columns = [
    {
      title: "字典标签",
      dataIndex: "dictLabel",
      ellipsis: true,
      toolTip: true,
      render: (text: string) => (
        <Typography.Text ellipsis={{ showTooltip: true }}>
          {text}
        </Typography.Text>
      ),
    },
    {
      title: "字典键值",
      dataIndex: "dictValue",
    },
    {
      title: "字典样式",
      dataIndex: "listClass",
      render: (text: string) => {
        return (
          <div
            className={classNames(
              "w-[10px] h-[10px] rounded-sm",
              getBgClass(text)
            )}
          ></div>
        );
      },
    },
    {
      title: "字典排序",
      dataIndex: "dictSort",
    },
    {
      title: "备注",
      dataIndex: "remark",
      ellipsis: true,
      toolTip: true,
      render: (text: any) => {
        return (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text ? text : "-"}
          </Typography.Text>
        );
      },
    },
    {
      title: "操作",
      dataIndex: "operate",
      render: (text: any, row: any) => {
        return (
          <Dropdown
            className="min-w-[120px]"
            zIndex={1000}
            position="bottom"
            trigger={"click"}
            render={
              <Dropdown.Menu>
                <Dropdown.Item
                  icon={<IconEdit />}
                  onClick={() => handleEditForm(row)}
                >
                  修改字典数据
                </Dropdown.Item>
                <Dropdown.Item
                  type="danger"
                  icon={<IconDelete />}
                  onClick={() => handleRemoveDictData(row.dictCode)}
                >
                  删除
                </Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <Button
              type="tertiary"
              theme="borderless"
              icon={<IconMore className="cursor-pointer" />}
            ></Button>
          </Dropdown>
        );
      },
    },
  ];
  const handleEditForm = (row: any) => {
    setFormTitle(dictDataFormTitle[1]);
    setFormId(row.dictCode);
    setTrue();
  };
  const handleAddForm = () => {
    if (selectedDict) {
      setFormTitle(dictDataFormTitle[0]);
      setFormId(undefined);
      setTrue();
    } else {
      Toast.error("请先选择字典类型");
    }
  };

  const rowSelection = {
    getCheckboxProps: (record: { name: any }) => ({
      // disabled: record.name === "设计文档", // Column configuration not to be checked
      name: record.name,
    }),
    onSelect: (record: any, selected: any) => {
      console.log(`select row: ${selected}`, record);
    },
    onSelectAll: (selected: any, selectedRows: any) => {
      console.log(`select all rows: ${selected}`, selectedRows);
    },
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      console.log(
        `selectedRowKeys: ${selectedRowKeys}`,
        "selectedRows: ",
        selectedRows
      );
    },
  };
  const handlePageChange = (page: number) => {
    fetchData(page, pageSize, {
      dictType: selectedDict?.dictType,
    });
  };
  const handlePageSizeChange = (pageSize: number) => {
    fetchData(page, pageSize, {
      dictType: selectedDict?.dictType,
    });
  };

  const fetchData = (
    currentPage = 1,
    currentPageSize = 15,
    formValues = {}
  ) => {
    setLoading(true);
    setPage(currentPage);
    setPageSize(currentPageSize);
    getDictDataList({
      pageNum: currentPage,
      pageSize: currentPageSize,
      ...formValues,
    })
      .then((res) => {
        setLoading(false);
        setData(res.rows);
        setTotal(res.total);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchData(page, pageSize, {
      dictType: selectedDict?.dictType,
    });
  }, [selectedDict]);

  const syncDict = () => {
    const modal = Modal.warning({
      title: "提示",
      content: "请选择是否确定同步缓存！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        try {
          const { msg } = await refreshCache();
          Toast.success(msg);
          return await Promise.resolve();
        } catch {
          return await Promise.reject();
        }
      },
    });
  };
  return (
    <div className="w-full h-full flex items-stretch gap-2 sm:overflow-auto flex-col bg-semi-color-white rounded-md py-4 px-2">
      {selectedDict ? (
        <div className="px-2 flex items-center gap-1">
          <span className="font-semibold">已选字典：</span>
          <Typography.Paragraph copyable>{selectedDict?.dictType}</Typography.Paragraph>
        </div>
      ) : (
        <div className="px-2">
          <span className="font-semibold">暂未选择</span>
        </div>
      )}
      <div className="flex  flex-row items-center justify-between gap-2 px-2">
        <div className="flex flex-row items-center gap-2">
          <Button
            type="primary"
            theme="solid"
            onClick={() => {
              handleAddForm();
            }}
          >
            新增字典
          </Button>
          <Button theme="solid" type="warning" onClick={syncDict}>
            同步缓存
          </Button>
        </div>
        <div>
          <Input showClear placeholder={"请输入字典标签"} />
        </div>
      </div>
      <div className="flex-1 sm:overflow-hidden">
        <Table
          rowSelection={rowSelection as any}
          columns={columns}
          className="h-full"
          dataSource={dataSource}
          loading={loading}
          size="small"
          scroll={{ x: 962, y: "calc(100vh - 210px)" }}
          pagination={{
            currentPage: page,
            pageSize,
            total,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            showSizeChanger: true,
          }}
          rowKey="dictCode"
          empty={
            <Empty
              description="暂无数据"
              image={<EmptyDataIcon style={{ width: 100 }} />}
            />
          }
        />
      </div>
      <DictDataForm
        dictType={selectedDict?.dictType}
        open={formOpen}
        title={formTitle}
        id={formId}
        onCancel={() => {
          setFormId(undefined);
          setFalse();
        }}
        onRefresh={() =>
          fetchData(1, 15, {
            dictType: selectedDict?.dictType,
          })
        }
      />
    </div>
  );
}
