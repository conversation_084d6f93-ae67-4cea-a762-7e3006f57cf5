import { Col, Row } from "@douyinfe/semi-ui";
import React, { useState } from "react";
import DictType from "./dictType";
import DictData from "./dictData";

export default function Dictionary() {
  const [selectedDict, setSelectedDict] = useState<any>(null);
  return (
    <div className="w-full flex h-full rounded-md px-2 py-2">
      <Row gutter={10} className="flex-1 w-full">
        <Col span={5} md={12} lg={5} sm={24} className="h-full w-full">
          <DictType
            onClickDictType={(dict: any) => {
              setSelectedDict(dict);
            }}
          />
        </Col>
        <Col span={19} md={12} lg={19} sm={24} className="h-full w-full">
          <DictData selectedDict={selectedDict} />
        </Col>
      </Row>
    </div>
  );
}
