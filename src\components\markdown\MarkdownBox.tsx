import React, { memo, PropsWithChildren, useState, useEffect } from "react";
import RemarkGfm from "remark-gfm";
import RemarkMath from "remark-math";
import RemarkBreaks from "remark-breaks";
import <PERSON>hypeKatex from "rehype-katex";
import <PERSON>hy<PERSON><PERSON><PERSON>light from "rehype-highlight";
import { CopyButton } from "./CopyButton";
import { MermaidBox } from "./MermaidBox";
import Markdown, { Components } from "react-markdown";
import { MessageUtil } from "@/utils/message-util";
import { isString, toAny } from "@/utils";
import { Link } from "@tanstack/react-router";
import CodeBlock from './CodeBlock';
import { RemarkThinksPlugin } from "./plugins/remarkThinksPlugin";
import ThinkingBlock from "../ThinkingBlock";
interface MarkdownBoxProps {
  content: string;
}

const isEqual = (prevProps: MarkdownBoxProps, currentProps: MarkdownBoxProps) => {
  return prevProps.content === currentProps.content;
}

// 文字逐个出现的动画组件
const FadeIn = memo(({ children }: PropsWithChildren) => {
  return <span className="fade-in animate-in duration-1000">{children} </span>;
});
FadeIn.displayName = "FadeIn";

// 单词逐个淡入的组件
const WordByWordFadeIn = memo(({ children }: PropsWithChildren) => {
  const childrens = [children]
    .flat()
    .flatMap((child) => (isString(child) ? child.split(" ") : child));
  return childrens.map((word, index) =>
    isString(word) ? <FadeIn key={index}>{word}</FadeIn> : word,
  );
});
WordByWordFadeIn.displayName = "WordByWordFadeIn";

export const MarkdownBox: React.FC<MarkdownBoxProps> = React.memo(({ content }) => {
  const components: Partial<Components> = {
    // 代码块处理
    code: ({ node, className, children, ...props }) => {
      const match = /language-(\w+)/.exec(className || "");
      const id = Math.random().toString(36).substr(2, 9);

      // 处理行内代码
      if (!match || props.inline) {
        return (
          <code className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono text-gray-800 dark:text-gray-200" {...props}>
            {children}
          </code>
        );
      }

      // 处理 mermaid 图表
      if (match[1] === 'mermaid') {
        return (
          <div className="w-full my-4" style={{ width: '100%', maxWidth: '100%' }}>
            <MermaidBox lang={match[1]} elementId={id} chartContent={String(children)} />
          </div>
        );
      }

      // 特别处理 markdown 语言的代码块
      if (match[1] === 'markdown') {
        return (
          <div className="w-full my-4" style={{ width: '100%', maxWidth: '100%', minWidth: '100%' }}>
            <CodeBlock
              language={match[1]}
              elementId={id}
              className={`${className} w-full`}
              style={{
                width: '100%',
                maxWidth: '100%',
                minWidth: '100%'
              }}
            >
              {children}
            </CodeBlock>
          </div>
        );
      }

      // 处理普通代码块
      return (
        <div className="w-full my-4" style={{ width: '100%', maxWidth: '100%' }}>
          <CodeBlock
            language={match[1]}
            elementId={id}
            className={className}
            style={{ width: '100%', maxWidth: '100%' }}
          >
            {children}
          </CodeBlock>
        </div>
      );
    },

    // 引用块优化 - 采用苹果风格的蓝色边框
    blockquote: ({ children }) => {
      return (
        <blockquote className="pl-4 border-l-4 border-primary/50 bg-primary/5 p-4 rounded-lg my-6">
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </blockquote>
      );
    },

    // 段落样式
    p: ({ children }) => {
      return (
        <p className="my-4 text-gray-800 dark:text-gray-200 leading-relaxed">
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </p>
      );
    },

    // 有序列表
    ol: ({ children, ...props }) => {
      return (
        <ol className="list-decimal pl-5 my-4 space-y-2" {...props}>
          {children}
        </ol>
      );
    },

    // 无序列表
    ul: ({ children, ...props }) => {
      return (
        <ul className="list-disc pl-5 my-4 space-y-2" {...props}>
          {children}
        </ul>
      );
    },

    // 列表项
    li: ({ children, ...props }) => {
      return (
        <li className="text-gray-700 dark:text-gray-300" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </li>
      );
    },

    // 粗体文本
    strong: ({ children, ...props }) => {
      return (
        <span className="font-semibold text-gray-900 dark:text-white" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </span>
      );
    },

    // 链接样式 - 苹果风格的蓝色链接
    a: ({ href, children, ...props }) => {
      return (
        <Link
          to={href || "#"}
          className="text-primary hover:text-primary/80 transition-colors duration-200 underline hover:no-underline"
          target="_blank"
          rel="noreferrer"
          {...props}
        >
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </Link>
      );
    },

    // 标题样式 - 苹果风格的字体大小和间距
    h1: ({ children, ...props }) => {
      return (
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-4" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </h1>
      );
    },

    h2: ({ children, ...props }) => {
      return (
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mt-6 mb-3" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </h2>
      );
    },

    h3: ({ children, ...props }) => {
      return (
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mt-5 mb-2" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </h3>
      );
    },

    h4: ({ children, ...props }) => {
      return (
        <h4 className="text-base font-semibold text-gray-700 dark:text-gray-200 mt-4 mb-1" {...props}>
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </h4>
      );
    },

    // 图片样式
    img: ({ src, alt, ...rest }) => {
      return (
        <img
          src={src}
          alt={alt}
          className="my-6 mx-auto rounded-xl shadow-md max-w-full transition-transform duration-300 hover:scale-[1.02]"
          {...rest}
        />
      );
    },
    hr: () => {
      return <hr className="my-6 border-semi-color-border dark:border-gray-700" />;
    },
    // 表格样式优化 - 确保表格占据100%宽度
    table: ({ children }) => {
      return (
        <div className="my-6 w-full overflow-x-auto rounded-lg shadow-sm border border-gray-300 dark:border-gray-700">
          <table className="min-w-full w-full caption-bottom text-sm border-collapse">
            {children}
          </table>
        </div>
      );
    },

    thead: ({ children }) => {
      return (
        <thead className="border-b-2  border-gray-400 dark:border-gray-500 bg-gray-100 dark:bg-gray-700">
          {children}
        </thead>
      );
    },

    th: ({ children }) => {
      return (
        <th className="h-12  px-6 text-left align-middle font-medium text-gray-800 dark:text-gray-100 border-b border-r last:border-r-0 border-gray-400 dark:border-gray-500">
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </th>
      );
    },

    tbody: ({ children }) => {
      return (
        <tbody>
          {children}
        </tbody>
      );
    },

    tr: ({ children }) => {
      return (
        <tr className="hover:bg-blue-50/30 dark:hover:bg-blue-900/20 transition-colors duration-150">
          {children}
        </tr>
      );
    },

    td: ({ children }) => {
      return (
        <td className="px-6 py-4 align-middle border-b border-r last:border-r-0 border-gray-400 dark:border-gray-500">
          <WordByWordFadeIn>{children}</WordByWordFadeIn>
        </td>
      );
    },


  };

  // 预处理内容，提取 <thinks> 标签
  const [thinkingContent, setThinkingContent] = useState<string | null>(null);
  const [mainContent, setMainContent] = useState(content);

  useEffect(() => {
    // 检查是否已经在处理思考内容，避免递归
    if (content.includes('AI 思考过程')) {
      setMainContent(content);
      return;
    }

    // 使用非贪婪匹配来捕获所有 <thinks> 标签内容
    const thinksRegex = /<thinks>([\s\S]*?)<\/thinks>/g;
    const matches = [...content.matchAll(thinksRegex)];

    if (matches.length > 0) {
      // 收集所有思考内容，确保不重复
      const thinkParts = new Set<string>();
      matches.forEach(match => {
        // 将思考内容按段落分割，以便去重
        const paragraphs = match[1].split('\n\n');
        paragraphs.forEach(p => {
          if (p.trim()) thinkParts.add(p.trim());
        });
      });

      // 将去重后的思考内容重新组合
      const uniqueThinks = Array.from(thinkParts).join('\n\n');

      // 设置思考内容
      setThinkingContent(uniqueThinks);

      // 移除原始标签
      setMainContent(content.replace(thinksRegex, ''));
    } else {
      setThinkingContent(null);
      setMainContent(content);
    }
  }, [content]);

  return (
    <>
      {thinkingContent && <ThinkingBlock content={thinkingContent} />}
      <Markdown
        remarkPlugins={[RemarkGfm, RemarkMath, RemarkBreaks]}
        rehypePlugins={[RehypeKatex, RehypeHighlight]}
        children={MessageUtil.messageContentConvert(mainContent)}
        components={components}
      />
    </>
  );
}, isEqual);
