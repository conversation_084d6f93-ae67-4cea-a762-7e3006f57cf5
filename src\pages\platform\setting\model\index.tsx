import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Collapse,
  DatePicker,
  Divider,
  Dropdown,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Space,
  Spin,
  Table,
  TabPane,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import { useEffect, useMemo, useState } from "react";
import {
  IconDelete,
  IconEdit,
  IconHelpCircle,
  IconMore,
  IconRefresh,
  IconRefresh2,
  IconSearch,
} from "@douyinfe/semi-icons";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { useBoolean } from "@/hooks";
import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import { getModelList } from "@/api/platform/model";
import useDictionary from "@/hooks/useDictionary";
import DictTag from "@/components/DictTag";
import { dictConvetToOpention } from "@/utils";
import { models } from "@/enums/modelEnum";
import OpenAiIcon from "@/components/icon/OpenAi";
import VolcanoIcon from "@/components/icon/VolcanoIcon";
import AliLinkIcon from "@/components/icon/AliLinkIcon";
import OllamaIcon from "@/components/icon/OllamaIcon";
import DeepSeekIcon from "@/components/icon/DeepSeekIcon";
import ModelDetails from "./components/ModelDetails";
import DashScopeModel from "./components/DashScopeModel";
import { initPageInfo } from "./contants/model";

function Model() {
  return (
    <div className="h-full px-2  w-full">
      <Tabs type="line">
        <TabPane tab="模型列表" itemKey="1">
          <AddModelList />
        </TabPane>
        <TabPane tab="添加更多模型" itemKey="2">
          <ModelList />
        </TabPane>
      </Tabs>
    </div>
  );
}

function AddModelList() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  const scroll = useMemo(() => ({ y: 600, x: "1200px" }), []);
  const [loading, setLoading] = useState(false);

  const [page, setPage] = useState(initPageInfo.page);
  const [pageSize, setPageSize] = useState(initPageInfo.pageSize);

  const [total, setTotal] = useState(0);
  const [dataSource, setData] = useState<any[]>([]);
  const [search, setSearch] = useState("");
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_model_type",
    "sys_normal_disable",
    "ai_model_provider",
  ]);
  useEffect(() => {
    loadDictionary();
  }, []);
  const getFilter = (data: any) => {
    return data?.map((element: any) => {
      return {
        text: element.dictLabel,
        value: element.dictValue,
      };
    });
  };
  const columns = useMemo(() => {
    return [
      {
        title: "模型版本",
        dataIndex: "modelVersion",
        render: (text:any, record:any) => {
          return (
            <div className="flex items-center gap-2">
              <Avatar
                size="extra-small"
                shape="square"
                src={record?.logo}
              ></Avatar>
              <div>{text}</div>
            </div>
          );
        },
      },
      {
        title: "别名",
        dataIndex: "aliasName",
      },
      {
        title: "供应商",
        dataIndex: "provider",
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_provider"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_provider || []}
            />
          );
        },
      },
      {
        title: "模型类型",
        dataIndex: "type",
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_type"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_type || []}
            />
          );
        },
      },
      {
        title: "更新日期",
        dataIndex: "updatedAt",
      },
      {
        title: "",
        dataIndex: "operate",
        render: () => {
          return (
            <Dropdown
              position="bottom"
              className="w-[100px]"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item>编 辑</Dropdown.Item>
                  <Dropdown.Item type="danger">删 除</Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                size="small"
                theme="borderless"
                type="primary"
                icon={<IconMore className="cursor-pointer" />}
              />
            </Dropdown>
          );
        },
      },
    ];
  }, []);

  const fetchData = (
    currentPage = initPageInfo.page,
    pageSize = initPageInfo.pageSize,
    formValues = {}
  ) => {
    setLoading(true);
    setPage(currentPage);
    getModelList({
      pageNum: currentPage,
      pageSize,
      ...formValues,
    })
      .then(({rows,total}) => {
        setLoading(false);
        setData(rows);
        setTotal(total);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handlePageChange = (page: number) => {
    fetchData(page);
  };
  const handlePageSizeChane = (pageSize: number) => {
    fetchData(page, pageSize);
  };

  useEffect(() => {
    fetchData();
  }, []);
  const handleSearch = (formValues: any) => {
    fetchData(initPageInfo.page, initPageInfo.pageSize, formValues);
  };
  const handleReset = () => {
    fetchData(initPageInfo.page, initPageInfo.pageSize);
  };

 

  return (
    <div className="h-full w-full  relative flex flex-col">
      <div className="bg-semi-color-white rounded-xl w-full flex-1 ">
        <div className=" mx-1 flex gap-1 items-center justify-between flex-wrap">
          <Form
            onReset={() => handleReset()}
            onSubmit={(values) => handleSearch(values)}
          >
            <Space wrap>
              <Form.Input
                noLabel
                style={{ width: 260 }}
                placeholder={"请输入模型名称"}
                field="aliasName"
              />
              <Form.Select
                filter
                noLabel
                optionList={dictConvetToOpention(
                  dictionaryData["ai_model_provider"]
                )}
                showClear
                style={{ width: 260 }}
                placeholder={"请选择模型供应商"}
                field="provider"
              />
              <Space>
                <Button theme="solid" type="primary" htmlType="submit">
                  提交
                </Button>
                <Button theme="outline" type="tertiary" htmlType="reset">
                  重置
                </Button>
              </Space>
            </Space>
          </Form>
        </div>

        <Table
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          scroll={scroll}
          empty={
            <Empty
              image={
                <IllustrationNoContent style={{ width: 150, height: 150 }} />
              }
              darkModeImage={
                <IllustrationNoContentDark
                  style={{ width: 150, height: 150 }}
                />
              }
              title="暂无数据"
            ></Empty>
          }
          pagination={{
            currentPage: page,
            pageSize: pageSize,
            onPageSizeChange: handlePageSizeChane,
            total: total,
            onPageChange: handlePageChange,
          }}
        />
      </div>
    </div>
  );
}

function ModelList() {
  const [open, { setTrue: setOpen, setFalse: closeOpen }] = useBoolean(false);
  const [model, setModel] = useState<any>();
  const [formOpen, { setTrue: setFormOpen, setFalse: closeFormOpen }] =
    useBoolean(false);
  const dataList = [
    {
      icon: <OpenAiIcon />,
      title: "OpenAI",
      key: "OpenAI",
      auth: "OpenAI",
      tags: ["LLM", "TEXT EMBEDDING"],
      description: "基于OpenAI官方提供的API",
    },
    {
      icon: <AliLinkIcon />,
      title: "通义千问",
      key: "DashScope",
      auth: "阿里云",
      tags: ["LLM", "TEXT EMBEDDING"],
      description: "基于阿里云提供的通义千问API",
    },
    {
      icon: <VolcanoIcon />,
      title: "火山引擎",
      key: "DashScope",
      auth: "字节跳动",
      tags: ["LLM", "TEXT EMBEDDING"],
      description: "基于火山引擎提供的豆包大模型API",
    },
    {
      icon: <DeepSeekIcon />,
      title: "DeepSeek",
      key: "DeepSeek",
      auth: "深度求索",
      tags: ["LLM", "TEXT EMBEDDING"],
      description: "由DeepSeek提供的大模型API",
    },
    {
      icon: <OllamaIcon />,
      title: "Ollama",
      key: "Ollama",
      auth: "Ollama",
      tags: ["LLM", "TEXT EMBEDDING"],
      description:
        "Ollama是一个轻量级的简单易用的本地大模型运行框架,通过Ollama可以在本地服务器构建和运营大语言模型(比如Llama3等).ChatWiki支持使用Ollama部署LLM的型和Text Embedding模型",
    },
  ];
  const handleDetails = (model: any) => {
    setOpen();
    setModel(model);
  };
  const handleAddForm = (model: any) => {
    setModel(model);
    setFormOpen();
  };
  return (
    <div>
      <List
        size="small"
        className="h-full"
        dataSource={dataList}
        grid={{
          xs: 24,
          sm: 24,
          md: 24,
          lg: 24,
          xl: 24,
          xxl: 24,
        }}
        renderItem={(item) => (
          <List.Item style={{ padding: "0px 0px 10px 0px" }}>
            <Card
              className="w-full"
              shadows="hover"
              bodyStyle={{ padding: "15px 10px 0px 10px" }}
              footerStyle={{ padding: "10px", paddingTop: "0" }}
              footer={
                <div className="flex justify-end items-center">
                  <div>
                    <Typography.Text type="secondary">
                      @{item.auth}
                    </Typography.Text>
                    <Divider layout="vertical" margin="12px" />
                  </div>
                  <Space>
                    <Button
                      type="tertiary"
                      theme="light"
                      onClick={() => handleDetails(item)}
                    >
                      查看详情
                    </Button>
                    <Button
                      theme="solid"
                      type="primary"
                      onClick={() => handleAddForm(item)}
                    >
                      添加模型
                    </Button>
                  </Space>
                </div>
              }
            >
              <Meta
                title={item.title}
                description={
                  <Space>
                    {item.tags.map((tag) => {
                      return (
                        <Tag size="small" color="cyan">
                          {tag}
                        </Tag>
                      );
                    })}
                  </Space>
                }
                avatar={item.icon}
              />
              <div className="text-semi-color-text-2 w-full px-1 py-1 theme='borderless' text-sm">
                <Typography.Text
                  type="secondary"
                  ellipsis={{
                    rows: 2,
                    showTooltip: {
                      opts: {
                        content: item.description,
                      },
                    },
                  }}
                >
                  {item.description}
                </Typography.Text>
              </div>
            </Card>
          </List.Item>
        )}
      />
      <ModelDetails open={open} onCancel={closeOpen} model={model} />
      <DashScopeModel open={formOpen} onCancel={closeFormOpen} model={model} />
    </div>
  );
}

export default Model;
