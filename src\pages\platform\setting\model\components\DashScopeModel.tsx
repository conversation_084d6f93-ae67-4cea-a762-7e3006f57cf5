import React, { useEffect } from "react";
import {
  Button,
  Descriptions,
  Form,
  Modal,
  Table,
  Toast,
} from "@douyinfe/semi-ui";
import useDictionary from "@/hooks/useDictionary";
import { dictConvetToOpention } from "@/utils";
import { IconForward, IconLink } from "@douyinfe/semi-icons";
import { formRules } from "../contants/model";
import { addChatModel } from "@/api/platform/chatModel";
import useBoolean from "@/hooks/useBoolean";

const DashScopeModel: React.FC<any> = ({ open, onCancel, model }) => {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_model_type",
  ]);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  useEffect(() => {
    if (open) {
      loadDictionary();
    }
  }, [open]);
  const formApiRef = React.useRef<any>(null);
  // 获取表单实例
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };

  // 表单提交处理
  const submitForm = async () => {
    try {
      const values = await formApiRef.current?.validate();
      await handleSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };
  // 提交逻辑
  const handleSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addChatModel({
        ...values,
        provider: model.key,
      });
      Toast.success(res.msg);
      // setLogo(defaultLogo);
      closeLoading();
      onCancel();
      return res;
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  // 重置表单
  const handleReset = () => {
    formApiRef.current?.reset();
  };
  return (
    <Modal
      width={800}
      title="新增通义模型"
      visible={open}
      onOk={onCancel}
      onCancel={onCancel}
      closeOnEsc={false}
      className="semi-light-scrollbar"
      footer={
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button style={{ marginRight: 8 }} onClick={handleReset}>
            重置
          </Button>
          <Button theme="solid" loading={loading} onClick={submitForm}>
            提交
          </Button>
        </div>
      }
    >
      <Form getFormApi={getFormApi} style={{ width: "100%" }}>
        <div>{model?.icon}</div>
        {/* <Form.Select
          className="w-full"
          filter
          rules={formRules.type}
          showClear
          optionList={dictConvetToOpention(dictionaryData["ai_model_type"])}
          field="type"
          label="模型类型"
          placeholder="请选择模型类型"
        /> */}

        <Form.Input
          className="w-full"
          field="aliasName"
          rules={formRules.aliasName}
          showClear
          label="模型别名"
          placeholder="请输入模型别名"
        />
        <Form.Input
          className="w-full"
          field="apiKey"
          rules={formRules.apiKey}
          showClear
          label="API Key"
          placeholder="请输入API Key"
          extraText={
            <div
              onClick={() =>
                window.open(
                  "https://www.aliyun.com/product/bailian?spm=5176.28630291.0.0.2be97eb5DEcmoQ"
                )
              }
              className="flex text-semi-color-primary cursor-pointer items-center gap-2 text-md"
            >
              <div>从阿里云百炼获取 API Key</div> <IconLink />
            </div>
          }
        />
      </Form>
    </Modal>
  );
};
export default DashScopeModel;
