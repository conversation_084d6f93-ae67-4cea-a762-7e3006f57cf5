import { Knowledge } from "@/components/icon/KnowledgeIcon";
import Icon, { IconPlus, IconSearch } from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Input,
  List,
  Row,
  Space,
} from "@douyinfe/semi-ui";
import React, { useState, useEffect } from "react";
import DatasetsForm from "./component/KnowledgeForm";
import { useBoolean } from "@/hooks";
import InfiniteScroll from "react-infinite-scroller";
import { getDatasetsList } from "@/api/platform/knowledge";

function Datasets() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);

  const fetchData = async () => {
    const res = await getDatasetsList({
      pageNum: page,
      pageSize: 10,
    });

    if (res.code === 200) {
      setData((prevData) => [...prevData, ...res.rows]);
      setTotal(res.total);
      setPage((prevPage) => prevPage + 1);

      // 如果当前数据长度等于总数据长度，设置 hasMore 为 false
      if (data.length + res.rows.length >= res.total) {
        setHasMore(false);
      }
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="h-full relative flex flex-col">
      <div className="text-nowrap font-extrabold pl-4 pt-2 text-lg mb-2 flex items-center justify-between">
        <div>角色预设</div>
        <div className="flex gap-2 items-center">
          <Button theme="solid" type="primary" onClick={setTrue}>
            创建知识库
          </Button>
          <div className="w-[300px]">
            <Input prefix={<IconSearch />} className="w-[300px]" showClear />
          </div>
        </div>
      </div>
      <InfiniteScroll
        dataLength={data.length}
        next={fetchData}
        hasMore={hasMore}
        loader={<h4>Loading...</h4>}
        endMessage={<p style={{ textAlign: "center" }}>没有更多数据了</p>}
        height={200} // 设置一个固定高度以便触发滚动事件
      >
        <List
          split={false}
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          dataSource={data}
          layout="horizontal"
          renderItem={(item) => (
            <List.Item className="w-full">
              <Card
                shadows="hover"
                footer={
                  <div className="flex justify-end">
                    <Button size="small">连接外部知识库</Button>
                  </div>
                }
              >
                <Card.Meta
                  title={`ZKDIGIMAX CMS性能测试 - ${item.id}`}
                  avatar={<Icon svg={<Knowledge />} />}
                />
                <div className="text-semi-color-text-2 px-1 py-1 theme='borderless' text-sm">
                  useful for when you want to answer queries about the ZKDIGIMAX
                  CMS性能测试-测试报告V1.1.docx
                </div>
              </Card>
            </List.Item>
          )}
        />
      </InfiniteScroll>
      <DatasetsForm open={formOpen} onCancel={setFalse} onOK={setFalse} />
    </div>
  );
}

export default Datasets;
