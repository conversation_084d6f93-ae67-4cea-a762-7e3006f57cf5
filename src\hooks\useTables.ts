import { useState, useEffect, useRef, useCallback } from "react";
interface TableProps {
  api: (params: any) => Promise<any>;
  params?: any;
  columns?: any[];
  pageSizeOpts?: any[];
  defaultPageSize?: number;
  onRowSelect?: (selectedRowKeys: any, selectedRows: any) => void;
  initialSearchParams?: any;
  autoReloadOnSearch?: boolean;
}

export const useTable = ({
  api,
  params = {},
  columns = [],
  defaultPageSize = 15,
  pageSizeOpts = [15, 20, 40, 100],
  onRowSelect,
  initialSearchParams = {},
  autoReloadOnSearch = true, // 默认自动加载
}: TableProps) => {
  // const pageRef = useRef({
  //   page:1,
  //   pageSize: defaultPageSize,
  // });
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [total, setTotal] = useState(0);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const searchParams = useRef(initialSearchParams);
  const [visibleColumns, setVisibleColumns] = useState<any[]>(columns);
  const updateVisibleColumns = useCallback(
    (selectedColumns: any[]) => {
      // 按原始 columns 的顺序排序 selectedColumns
      const ordered = columns.filter((col) =>
        selectedColumns.some((c) => c.dataIndex === col.dataIndex)
      );
      setVisibleColumns(ordered);
    },
    [columns]
  );

  // 使用 ref 保持最新参数引用
  // const paramsRef = useRef({ params, searchParams:searchParams.current });
  // paramsRef.current = { params, searchParams };
  // 统一参数获取函数
  const getCurrentParams = (currentPage: number, currentPageSize: number) => ({
    pageNum: currentPage,
    pageSize: currentPageSize,
    ...params,
    ...searchParams.current,
    // ...paramsRef.current.params,
    // ...paramsRef.current.searchParams,
  });

  // 核心请求方法（无依赖项）
  const fetchData = useCallback(
    async (newPage = page, newPageSize = pageSize) => {
      setLoading(true);
      try {
        const res = await api(getCurrentParams(newPage, newPageSize));
        setDataSource(res.rows || []);
        setTotal(res.total || 0);
      } finally {
        setLoading(false);
      }
    },
    [api]
    // api, pageRef.current.page, pageRef.current.pageSize
  );

  // 初始化加载（仅执行一次）
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // 分页变化处理
  const handlePageChange = (newPage: number) => {
    // setPage(newPage);
    // pageRef.current.page = newPage
  }

  const handlePageSizeChange = (newPageSize: number) => {
    // debugger;
    // setPageSize(newPageSize);
    //  pageRef.current.pageSize = newPageSize
    // setPage(1);
  }

  // 行选择处理
  const handleRowSelection = (selectedRowKeys: any, selectedRows: any) => {
    if (onRowSelect) {
      onRowSelect(selectedRowKeys, selectedRows);
    }
  };

  // 重置搜索参数
  const resetSearchParams = () => {
    setSearchParams(initialSearchParams);
    if (autoReloadOnSearch) {
      fetchData();
    }
  };

  const setSearchParams = (values:any)=>{
   if(values){
     searchParams.current = values
   }
  }
  const handleChange = (newPage: number, newPageSize: number) => {
    setPage(newPage);
    setPageSize(newPageSize);
    // debugger;
    // console.log(newPage, newPageSize)
    if(newPageSize !== pageSize){
      setPage(1);
      fetchData(1, newPageSize);
    }else{
      fetchData(newPage, newPageSize);
    }
    
  }

  return {
    dataSource,
    loading,
    columns,
    visibleColumns, 
    updateVisibleColumns, 
    pagination: {
      currentPage: page,
      pageSize,
      total,
      showTotal: true,
      onChange:  handleChange,
      // onPageChange: handlePageChange,
      // onPageSizeChange: handlePageSizeChange,
      showSizeChanger: true,
      pageSizeOpts,
    },
    rowSelection: {
      onChange: (selectedRowKeys: any, selectedRows: any) =>
        handleRowSelection(selectedRowKeys, selectedRows),
    },
    
    refresh: fetchData, // 手动刷新方法
    searchParams, // 当前搜索参数
    setSearchParams, // 更新搜索参数
    resetSearchParams, // 重置搜索参数
  };
};
