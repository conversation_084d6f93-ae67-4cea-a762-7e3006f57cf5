import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const TRACEFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.task"
          label="任务"
          rows={6}
          placeholder="描述任务"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.request"
          label="请求"
          rows={6}
          placeholder="描述请求"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.operation"
          label="操作"
          rows={6}
          placeholder="描述操作"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.context"
          label="上下文"
          rows={6}
          placeholder="提供上下文信息"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.example"
          label="示例"
          rows={6}
          placeholder="给出示例"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default TRACEFields;