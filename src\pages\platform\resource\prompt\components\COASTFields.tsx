import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const COASTFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.context"
          label="背景"
          rows={6}
          placeholder="上下文：为讨论设置背景或上下文"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.objective"
          label="目的"
          rows={6}
          placeholder="目的描述目标"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.action"
          label="行动"
          rows={6}
          placeholder="行动定义要完成的工作或活动"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.scenario"
          label="场景"
          rows={6}
          placeholder="场景描述场景"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.task"
          label="任务"
          rows={6}
          placeholder="任务描述任务"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default COASTFields;