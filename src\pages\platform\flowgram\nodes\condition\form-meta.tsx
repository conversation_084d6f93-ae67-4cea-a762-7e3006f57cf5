import {
  FormRenderProps,
  FormMeta,
  ValidateTrigger,
} from "@flowgram.ai/free-layout-editor";

import { FlowNodeJSON } from "../../typings";
import { FormHeader, FormContent } from "../../form-components";
import { ConditionInputs } from "./condition-inputs";
import { useIsSidebar } from "../../hooks";

export const renderForm = ({ form }: FormRenderProps<FlowNodeJSON>) => {
  const isSidebar = useIsSidebar();
  if (isSidebar) {
    return (
      <>
        {/* <FormHeader /> */}
        <FormContent>
          <ConditionInputs />
        </FormContent>
      </>
    );
  }
  return (
    <>
      <FormHeader />
      <FormContent>
        <ConditionInputs />
      </FormContent>
    </>
  );
};

export const formMeta: FormMeta<FlowNodeJSON> = {
  render: renderForm,
  validateTrigger: ValidateTrigger.onChange,
  validate: {
    title: ({ value }: { value: string }) =>
      value ? undefined : "Title is required",
    "inputsValues.conditions.*": ({ value }) => {
      if (!value?.value?.content) return "Condition is required";
      return undefined;
    },
  },
};
