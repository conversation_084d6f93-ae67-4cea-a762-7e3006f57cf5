import { http } from "@/utils/axios";

const base_url = "aigc/v1/model";
export function getModelList(params: any) {
  return http.request<API.TalbeDataInfo<any>>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function getModelProviderOptions(type: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/provider/selected/${type}`,
    method: "GET",
  });
}
