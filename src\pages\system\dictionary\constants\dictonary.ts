export const dictDataFormTitle = ["新增字典数据", "编辑字典数据"];
export const dictTypeFormTitle = ["新增字典类型", "编辑字典类型"];
export const dictCssTypeOptions = [
    {
      label: "进行中",
      value: "processing",
    },
    {
      label: "信息",
      value: "info",
    },
    {
      label: "成功",
      value: "success",
    },
    {
      label: "提醒",
      value: "warning",
    },
    {
      label: "错误",
      value: "error",
    },
  ]

  export const getDictListClass = (value: string) => {
    return dictCssTypeOptions.filter(item => item.value === value);
};


  export const getBgClass = (value: string) => {
    let bgClass = "";
    switch (value) {
      case "processing":
        bgClass = "bg-semi-color-primary";
        break;
      case "success":
        bgClass = "bg-semi-color-success";
        break;
      case "info":
        bgClass = "bg-semi-color-tertiary";
        break;
      case "warning":
        bgClass = "bg-semi-color-warning";
        break;
      case "error":
        bgClass = "bg-semi-color-danger";
    }
    return bgClass;
  };