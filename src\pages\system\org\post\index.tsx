import { getPostList } from "@/api/system/post";
import {
  IconMore,
  IconMoreStroked,
  IconPlusStroked,
} from "@douyinfe/semi-icons";
import {
  Button,
  Dropdown,
  Input,
  List,
  ResizeGroup,
  ResizeHandler,
  ResizeItem,
  Spin,
  Typography,
} from "@douyinfe/semi-ui";
import classNames from "classnames";
import React, { useEffect, useState } from "react";
import { UserList } from "../user/user-list";
import PostForm from "./components/post-form";

export default function Post() {
  const [selectPost, setSelectPost] = useState<any>(null);
  return (
    <div className="p-2 w-full h-full">
      <ResizeGroup direction="horizontal">
        <ResizeItem defaultSize={"20%"} min={"15%"} max={"20%"}>
          <PostList
            onSelectPost={(post: any) => {
              setSelectPost(post);
            }}
          />
        </ResizeItem>
        <ResizeHandler
          style={{
            zIndex:1,
            backgroundColor: "rgba(var(--semi-color-bg-1), 1)",
          }}
        ></ResizeHandler>
        <ResizeItem defaultSize={"80%"}>
          <div className="bg-semi-color-white w-full h-full rounded-md">
            <UserList postId={selectPost?.postId} />
          </div>
        </ResizeItem>
      </ResizeGroup>
    </div>
  );
}

const PostList = ({ onSelectPost }: any) => {
  const titles=["新增岗位","修改岗位"]
  const [selected, setSelected] = useState<any>(null);
  const [dataSource, setDataSource] = useState<any>([]);
  const [loading, setLoading] = useState(false);
  const [id,setId] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const [title, setTitle]= useState<any>(titles[0]);
  const handleOpen = (id?:any) => {
    if(id){
      setTitle(titles[1]);
      setId(id);
    }else{
      setTitle(titles[0]);
      setId(null);
    }
    setOpen(true)
  };
  const handleItemClick = (item: any) => {
    setSelected((prev: any) => (prev?.postId === item.postId ? null : item));
    if (selected?.postId === item.postId) {
      onSelectPost(null);
    } else {
      onSelectPost(item);
    }
  };
  const featchPostList = () => {
    setLoading(true);
    getPostList(null)
      .then(({ data }) => {
        setDataSource(data);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    // 初始化
    featchPostList();
  }, []);
  return (
    <div className=" bg-semi-color-white w-full h-full flex flex-col gap-2  rounded-md">
      <div className="text-semi-color-text-0 font-semibold px-3 pt-4">岗位</div>
      <div className="px-2">
        <Input showClear placeholder={"搜索"} />
      </div>
      <div className="px-2">
        <Button onClick={()=>handleOpen()} block theme="solid" type="primary" icon={<IconPlusStroked />}>
          创建岗位
        </Button>
      </div>
      <div className="flex-1 px-2">
        {loading ? (
          <Spin
            style={{ width: "100%", height: "100%" }}
            tip="正在加载..."
            spinning={loading}
          ></Spin>
        ) : (
          <List
            dataSource={dataSource}
            split={false}
            renderItem={(item, index) => (
              <List.Item
                onClick={() => handleItemClick(item)}
                className={classNames(
                  "w-full justify-between group/item box-border  group/item flex items-center flex-nowrap",
                  "hover:bg-semi-color-fill-0",
                  "rounded-md",
                  "cursor-pointer",
                  {
                    "bg-semi-color-fill-0 text-semi-color-primary":
                      selected?.postId === item.postId,
                  }
                )}
                key={item.postId}
              >
                <Typography.Text
                  ellipsis={{ showTooltip: true }}
                  className={classNames({
                    "text-semi-color-primary": selected?.postId === item.postId,
                  })}
                >
                  {item.postName}
                </Typography.Text>
                <Dropdown
                  className="min-w-[150px]"
                  zIndex={1000}
                  trigger={"click"}
                  position={"bottomLeft"}
                  render={
                    <Dropdown.Menu>
                      <Dropdown.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          // handleEditForm(item);
                        }}
                      >
                        修改
                      </Dropdown.Item>
                      <Dropdown.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          // handleRemoveDictType(item.dictId);
                        }}
                      >
                        删除
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  }
                >
                  <div className="cursor-pointer group/edit invisible group-hover/item:visible ">
                    <Button
                      theme="borderless"
                      type="tertiary"
                      onClick={(e) => {
                        // e.stopPropagation();
                        // Toast.info({ item?.key });
                        // e.stopPropagation();
                      }}
                      icon={<IconMoreStroked />}
                      size="small"
                    />
                  </div>
                </Dropdown>
              </List.Item>
            )}
          ></List>
        )}
      </div>
      <PostForm
        title={title}
        open={open}
        onRefresh={featchPostList}
        id={id}
        onCancel={() => {
          setOpen(false);
        }}
      />
    </div>
  );
};
