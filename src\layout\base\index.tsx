import React, { useEffect, useState } from "react";
import Header from "@douyinfe/semi-ui/lib/es/navigation/Header";
import { Divider, Icon, Layout, Nav } from "@douyinfe/semi-ui";

import { useNavigate } from "@tanstack/react-router";
import DocIcon from "@/components/icon/DocIcon";
import AgentIcon from "@/components/icon/AgentIcon";
import SettingIcon from "@/components/icon/SettingIcon";
import { Logo } from "@/components/icon/Logo";
import AppList from "../components/AppList";
import SiderAvatar from "../components/SiderAvatar";
import SiderTeamProject from "../components/SiderTeamProject";
import ResourceIcon from "@/components/icon/ResourceIcon";
import McpIcon from "@/components/icon/McpIcon";
const BaseLayout: React.FC<any> = (props) => {
  const tabData = [
    { itemKey: "/platform/app", text: "智能体应用", icon: <Icon svg={<AgentIcon />} size="large"  /> },
    { itemKey: "/platform/mcp", text: "MCP管理", icon: <Icon svg={<McpIcon/>} size="large"  /> },
    // { itemKey: "/platform/resource", text: "资源管理", icon: <ResourceIcon /> },
    { itemKey: "/platform/knowledge", text: "知识库管理", icon:  <Icon svg={<DocIcon />} size="large"  /> } ,
    {
      itemKey: "/platform/setting",
      text: "系统管理",
      icon: <Icon svg={<SettingIcon />} size="large"  />,
    },
  ];
  const [activeKey, setActiveKey] = useState<Array<string>>([]);
  const navigate = useNavigate();
  const pathToTitle: { [key: string]: string } = {
    // "/platform/workbench": "数据分析",
    "/platform/app": "智能体应用",
    "/platform/mcp": "MCP管理",
    "/platform/knowledge": "知识库管理",
    "/platform/setting": "系统管理",
  };
  const jump = (key: string) => {
    setActiveKey([key]);
    navigate({
      to: key,
    });
  };
  useEffect(() => {
    setActiveKey([location.pathname]);
    document.title = pathToTitle[location.pathname] || "LynkzHub";
  }, [location.pathname]);
  return (
    <Layout
      className="full-height relative w-full"
      style={{ background: "#f2f4f7" }}
    >
      <Header
        style={{
          zIndex: 2,
        }}
        className="flex justify-between items-center  "
      >
        <Nav
          mode={"horizontal"}
          className="w-full"
          items={tabData}
          onSelect={(data: any) => {
            jump(data.itemKey as string);
            setActiveKey(data.selectedKeys as string[]);
          }}
          selectedKeys={activeKey}
          header={{
            children: (
              <div className="flex gap-2 items-center font-semibold">
                <AppList />
                <Divider layout="vertical" />
                <div className="flex items-center gap-1">
                  <Logo className="w-6 h-full" />
                  <div className="text-lg font-semibold">LynkzHub</div>
                </div>
              </div>
            ),
          }}
          footer={
            <div className="flex items-center gap-2">
              <SiderTeamProject />
              <SiderAvatar />
            </div>
          }
        />
      </Header>
      <div className="flex-1 px-2 h-full w-full p-2 overflow-y-auto overflow-x-auto">
        {props.children}
      </div>
    </Layout>
  );
};

export default BaseLayout;
