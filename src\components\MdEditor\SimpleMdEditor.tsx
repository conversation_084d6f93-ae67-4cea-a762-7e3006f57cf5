import React, { useState, useEffect } from 'react';
import { MdEditor } from 'md-editor-rt';
import 'md-editor-rt/lib/style.css';
// import './style.scss'
interface SimpleMdEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  maxHeight?: number;

}

const SimpleMdEditor: React.FC<SimpleMdEditorProps> = ({
  value = '',
  onChange,
  placeholder = '输入您的提示词...',
  className = '',
  minHeight = 300,
  maxHeight = 600,
}) => {
  const [content, setContent] = useState(value);

  useEffect(() => {
    setContent(value);
  }, [value]);

  const handleChange = (newValue: string) => {
    setContent(newValue);
    onChange?.(newValue);
  };

  return (
    <div className={` w-full rounded-lg overflow-hidden shadow-sm ${className}`}>
      <MdEditor
        className="simple-md-editor"
        modelValue={content}
        onChange={handleChange}
        placeholder={placeholder}
        preview={false} // 关闭预览
        previewOnly={false} // 关闭仅预览模式
        toolbars={[]} // 移除所有工具栏
        footers={[]} // 移除底部工具栏
        showCodeRowNumber={false}
        style={{
          minHeight: `${minHeight}px`,
          maxHeight: `${maxHeight}px`,
        }}
        theme="light"
        previewTheme="default"
        codeTheme="atom"
      />

      {/* 自定义状态栏 */}
      <div className="flex items-center justify-end px-3 py-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-500">
        <div>
          {content.length} 字符 · {content.split('\n').length} 行
        </div>
      </div>
    </div>
  );
};

export default SimpleMdEditor;
