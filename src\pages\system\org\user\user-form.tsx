import { addUser, getUser } from '@/api/system/user';
import { IconClose, IconMailStroked1, IconUserStroked } from '@douyinfe/semi-icons';
import { Banner, Button, Col, Divider, Form, Input, Modal, Row, TabPane, Tabs, Toast } from '@douyinfe/semi-ui';
import React, { useEffect } from 'react';
import '@/layout/components/style.scss';
import { motion } from 'framer-motion';
import Section from '@douyinfe/semi-ui/lib/es/form/section';
import './styles.scss'
// 定义动画配置
const tabVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -10 }
};

// 表单标签宽度常量
// const LABEL_WIDTH = 100;

export default function UserForm({
    title,
    deptId,
    open,
    onRefresh,
    detps,
    onCancel,
}: any) {
    const formApiRef = React.useRef(null);
    const [roles, setRoles] = React.useState<any>([]);
    const [loading, setLoading] = React.useState(false);
    const [type, setType] = React.useState<any>("manual");

    const getFormApi = (formApi: any) => {
        formApiRef.current = formApi;
    };

    const getUserRoles = () => {
        getUser().then((res) => {
            setRoles(res.data?.roles?.map((item: any) => {
                return { label: item.roleName, value: item.roleId, key: item.roleId };
            }));
        });
    };

    useEffect(() => {
        if (open) {
            getUserRoles();
        }
    }, [open]);

    useEffect(() => {
        if (deptId) {
            formApiRef.current?.setValues({ deptId: deptId });
        }
    }, [deptId]);

    const handleReset = () => {
        formApiRef.current?.reset();
        onCancel();
    };

    const handleAddSubmit = async (values: any) => {
        return await addUser({
            ...values,
        }).then((res: any) => {
            Toast.success(res.msg);
            onRefresh();
            handleReset();
            return Promise.resolve(res);
        }).catch((err: any) => {
            return Promise.reject(err);
        });
    };

    const handleEditSubmit = async (values: any) => {
        //  TODO
    };

    const submitForm = async () => {
        if (type === "manual") {
            try {
                setLoading(true);
                const values = await formApiRef.current?.validate();
                await handleAddSubmit(values);
            } catch (errors) {
                console.error(errors);
            } finally {
                setLoading(false);
            }
        }
    };

    return (
        <>
            <Modal
                className='profile-modal'
                width={900}
                visible={open}
                onOk={submitForm}
                closable={false}
                centered
                footer={null}
                closeOnEsc={false}
                bodyStyle={{
                    height: 600,
                    overflowY: "hidden",
                    margin: "0",
                    position: "relative",
                    padding: 0
                }}
                onCancel={handleReset}
                confirmLoading={loading}
                okText={"提交"}
            >
                <div className='w-full h-full flex flex-row '>
                    {/* 左侧菜单 */}
                    <div className="w-[200px] bg-semi-color-fill-0 py-2 flex flex-col gap-1  px-2">
                        <div className="text-semi-color-text-0 px-2 font-semibold py-2 text-lg">
                            {title}
                        </div>
                        <div
                            className={`hover:bg-semi-color-fill-0 flex items-center gap-2 px-2 py-2 cursor-pointer rounded-md ${type === 'manual' ? 'font-bold text-semi-color-primary' : ''}`}
                            onClick={() => setType('manual')}
                        >
                            <IconUserStroked /> <span>创建用户</span>
                        </div>
                        <div
                            className={`hover:bg-semi-color-fill-0 flex items-center gap-2 px-2 py-2 cursor-pointer rounded-md ${type === 'email' ? 'font-bold text-semi-color-primary' : ''}`}
                            onClick={() => setType('email')}
                        >
                            <IconMailStroked1 /> <span>邮箱邀请</span>
                        </div>
                    </div>
                    {/* 右侧内容 */}
                    <div className="flex-1 p-4 flex flex-col">
                        <div className="flex justify-between items-center mb-6">
                            <div className='flex flex-col'>
                                <div className="text-xl font-bold text-semi-color-text-0">
                                    {type === 'manual' ? '创建用户' : '邮箱邀请'}
                                </div>
                                <div className='text-semi-color-text-2 mt-1'>
                                    {
                                        type === 'manual' ? "添加成员帐号，成员通过用户名和初始密码登录系统，修改初始密码后进入组织" : "受邀新成员的，组织与角色为：所选的"
                                    }
                                </div>
                            </div>
                            <Button theme="borderless" type="tertiary" icon={<IconClose />} onClick={onCancel} />
                        </div>

                        {/* 动画包裹内容区域 */}
                        <motion.div
                            key={type}
                            className='flex-1 overflow-y-auto semi-light-scrollbar '
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                            variants={tabVariants}
                            transition={{ duration: 0.3 }}
                        >
                            {type === 'manual' ? (
                                <ManualForm depts={detps} roles={roles} getFormApi={getFormApi} />
                            ) : (
                                <EmailAdd depts={detps} roles={roles} />
                            )}
                        </motion.div>

                        {/* 独立的底部操作栏 */}
                        <div className='flex h-[60px] px-4 w-full justify-end items-center gap-2'>
                            <Button type="tertiary" onClick={handleReset}>
                                取消
                            </Button>
                            <Button theme='solid' type="primary" onClick={submitForm} loading={loading}>
                                提交
                            </Button>
                        </div>
                    </div>
                </div>
            </Modal>
        </>
    );
}

const ManualForm = ({ depts, roles, getFormApi }: any) => {
    return (
        <div className='w-full overflow-x-hidden user-form'>
            <Form getFormApi={getFormApi} >
                <Section text={'基础信息'}>
                    <Row gutter={[10, 5]}>
                        <Col span={24}>
                            <Form.TreeSelect
                                placeholder={"请选择所属部门"}
                                filterTreeNode
                                className='w-full'
                                showClear
                                rules={[
                                    { required: true, message: "所属部门不能为空" },
                                ]}
                                treeData={depts}
                                fieldClassName='w-full'
                                label="所属部门"
                                field='deptId'
                            ></Form.TreeSelect>
                        </Col>

                        <Col span={12}>
                            <Form.Input
                                placeholder={"请输入用户昵称"}
                                label="用户昵称"
                                field='nickName'
                                rules={[
                                    { required: true, message: "用户昵称不能为空" },
                                    {
                                        min: 1,
                                        max: 30,
                                        message: "用户昵称长度不能超过30个字符",
                                    },
                                    {
                                        pattern: /^[^<>&"']+$/,
                                        message: "用户昵称不能包含特殊字符",
                                    }
                                ]}
                            ></Form.Input>
                        </Col>

                        <Col span={12}>
                            <Form.Input
                                rules={[
                                    { required: true, message: "请输入账号名" },
                                    {
                                        min: 1,
                                        max: 30,
                                        message: "用户账号长度不能超过30个字符",
                                    },
                                    {
                                        pattern: /^[^<>&"']+$/,
                                        message: "用账号名不能包含特殊字符",
                                    }
                                ]}
                                placeholder={"请输入账号名"}
                                label="账号名"
                                field='userName'
                            ></Form.Input>
                        </Col>


                        <Col span={24}>
                            <Form.RadioGroup
                                rules={[{ required: true, message: "用户性别不能为空" }]}
                                initValue={"0"}
                                field="sex"
                                label='性别'
                            >
                                <Form.Radio value="0">男</Form.Radio>
                                <Form.Radio value="1">女</Form.Radio>
                            </Form.RadioGroup>
                        </Col>
                    </Row>
                </Section>
                <Section text={'联系信息'}>
                    <Row gutter={[10, 5]}>
                        <Col span={12}>
                            <Form.Input
                                rules={[
                                    { required: true, message: "请输入邮箱" },
                                    {
                                        min: 1,
                                        max: 50,
                                        message: "邮箱长度不能超过50个字符",
                                    },
                                    {
                                        pattern: /^[^<>&"']+$/,
                                        message: "邮箱不能包含特殊字符",
                                    },
                                    {
                                        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                                        message: "邮箱格式不正确",
                                    }
                                ]}
                                placeholder={"请输入邮箱"}
                                label="邮箱"
                                field='email'
                            ></Form.Input>
                        </Col>
                        <Col span={12}>
                            <Form.Input
                                placeholder={"请输入手机号"}
                                label="手机号"
                                field='phonenumber'
                                rules={[
                                    {
                                        pattern: /^1[3-9]\d{9}$/,
                                        message: "请输入正确的手机号",
                                    }
                                ]}
                            ></Form.Input>
                        </Col>
                    </Row>
                </Section>
                <Section text={'权限/身份组'}>
                    <Row gutter={[10, 5]}>
                        <Col span={12}>
                            <Form.Select
                                rules={[{ required: true, message: "角色不能为空" }]}
                                showClear
                                multiple
                                optionList={roles}
                                placeholder={"请选择角色"}
                                filter
                                className='w-full'
                                fieldClassName='w-full'
                                label="角色"
                                field='roleIds'
                            ></Form.Select>
                        </Col>
                    </Row></Section>

            </Form>
        </div>
    );
}

const EmailAdd = ({ roles, depts }: any) => {
    return (
        <div className='w-full overflow-x-hidden'>
            <Form>
                <Form.Input
                    label="邮箱"
                    rules={[
                        { required: true, message: "请输入邮箱地址" },
                        {
                            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                            message: "邮箱格式不正确",
                        }
                    ]}
                    field='email'
                    placeholder='请输入邮箱地址'
                    className='mt-2'
                />

                <Form.TreeSelect
                    placeholder={"请选择所属部门"}
                    filterTreeNode
                    className='w-full'
                    showClear
                    rules={[
                        { required: true, message: "所属部门不能为空" },
                    ]}
                    treeData={depts}
                    fieldClassName='w-full'
                    label="所属部门"
                    field='deptId'
                ></Form.TreeSelect>

                <Form.Select
                    rules={[{ required: true, message: "角色不能为空" }]}
                    showClear
                    multiple
                    optionList={roles}
                    placeholder={"请选择角色"}
                    filter
                    className='w-full'
                    fieldClassName='w-full'
                    label="角色"
                    field='roleIds'
                ></Form.Select>
            </Form>
        </div>
    );
}    