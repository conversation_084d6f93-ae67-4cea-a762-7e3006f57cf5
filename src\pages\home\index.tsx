import React, { useState, useEffect } from "react";
import { useNavigate } from "@tanstack/react-router";
import {
  Avatar,
  Button,
  Card,
  List,
  Typography,
  Calendar,
  Tag,
  Empty,
  Divider
} from "@douyinfe/semi-ui";
import Icon, {
  IconCalendar,
  IconChevronRight,
  IconMail,
  IconSearch,
  IconArrowRight
} from "@douyinfe/semi-icons";
import NoticeAnnouncement from "@/components/NoticeAnnouncement";
import { ArrowDownRight, ArrowRight, Bolt, Bot, Building2 } from "lucide-react";
import { useGlobalStore } from "@/store";
import { motion } from "framer-motion";

// 背景装饰组件
const BackgroundDecoration = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {/* 渐变背景 - 从浅黄色到粉色的渐变，使用蓝色作为主题色 */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: 'linear-gradient(120deg, #e6f7ff 0%, #f0f5ff 50%, #e6f7ff 100%)',
          opacity: '1'
        }}
      />

      {/* 右侧浮动圆形元素 */}
      <div
        className="absolute top-[20%] right-[10%] w-[80px] h-[80px] rounded-full"
        style={{
          background: 'rgba(24, 144, 255, 0.6)',
          boxShadow: '0 0 20px rgba(24, 144, 255, 0.3)'
        }}
      />

      {/* 右侧较大浮动圆形元素 */}
      <div
        className="absolute top-[35%] right-[15%] w-[120px] h-[120px] rounded-full"
        style={{
          background: 'rgba(24, 144, 255, 0.5)',
          boxShadow: '0 0 30px rgba(24, 144, 255, 0.2)'
        }}
      />

      {/* 半透明圆形元素 */}
      <div
        className="absolute top-[25%] right-[20%] w-[100px] h-[100px] rounded-full"
        style={{
          background: 'rgba(255, 255, 255, 0.6)',
          backdropFilter: 'blur(10px)'
        }}
      />

      {/* 微妙的网格背景 */}
      <div
        className="absolute inset-0 z-1"
        style={{
          backgroundImage: `
            linear-gradient(rgba(24, 144, 255, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(24, 144, 255, 0.03) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px',
          opacity: '0.7'
        }}
      />
    </div>
  );
};

export default function Home() {
  const navigate = useNavigate();
  const { Text, Title, Paragraph } = Typography;
  const [selectedDate, setSelectedDate] = useState(new Date());
  const { userInfo } = useGlobalStore();
  // 确保背景装饰在组件挂载后渲染
  useEffect(() => {
    // 强制重绘背景
    const container = document.querySelector('.home-container');
    if (container) {
      container.classList.add('bg-loaded');
    }
  }, []);

  // 应用列表数据 - 主要应用
  const mainApps = [
    {
      id: 1,
      title: "智能体应用",
      description: "创建、管理和部署AI智能体，支持多种场景应用和自定义对话模型。",
      icon: <Icon svg={<Bot />} />,
      bgColor: "#2ed573",
      path: "/platform/app/index",
    },
    {
      id: 2,
      title: "知识库管理",
      description: "构建和管理AI知识库，支持文档上传、向量检索和智能问答能力。",
      bgColor: "#ffa502",
      icon: <Icon svg={<Building2 />} />,
      path: "/platform/knowledge",
    },
    {
      id: 3,
      title: "模型管理",
      description: "管理和配置各类AI模型，支持OpenAI、DeepSeek等多种模型接入。",
      bgColor: "#3742fa",
      icon: <Icon svg={<Bolt />} />,
      path: "/platform/model",
    },
  ];

  // 全部应用数据
  const allApps = [
    {
      id: 1,
      title: "智能体应用",
      icon: "/src/assets/icons/app-icon1.svg",
      path: "/platform/app/index",
    },
    {
      id: 2,
      title: "知识库管理",
      icon: "/src/assets/icons/app-icon2.svg",
      path: "/platform/knowledge",
    },
    {
      id: 3,
      title: "模型管理",
      icon: "/src/assets/icons/app-icon3.svg",
      path: "/platform/model",
    },
    {
      id: 4,
      title: "Prompt管理",
      icon: "/src/assets/icons/app-icon4.svg",
      path: "/platform/resource/prompt",
    },
    {
      id: 5,
      title: "系统设置",
      icon: "/src/assets/icons/app-icon5.svg",
      path: "/platform/setting",
    },
    {
      id: 6,
      title: "MCP管理",
      icon: "/src/assets/icons/app-icon6.svg",
      path: "/platform/mcp",
    },
    {
      id: 7,
      title: "用户管理",
      icon: "/src/assets/icons/app-icon7.svg",
      path: "/system/org/user",
    },
    {
      id: 8,
      title: "角色权限",
      icon: "/src/assets/icons/app-icon8.svg",
      path: "/system/org/role",
    },
  ];

  // 通知公告数据
  const announcements = [
    {
      id: 1,
      title: "【系统更新】DeepSeek-R1模型已接入系统，支持更复杂的视觉问答任务。",
      date: "2024-05-07",
    },
    {
      id: 2,
      title: "知识库管理模块新增向量检索功能，提升智能问答准确率。",
      date: "2024-05-05",
    },
  ];

  // 待办事项数据
  const todoItems = [
    {
      id: 1,
      user: "赵丹林",
      title: "智能体「客服助手」需要更新知识库，请及时处理。",
      time: "2024-08-26 12:37",
      tag: "智能体",
    },
    {
      id: 2,
      user: "周霞",
      title: "OpenAI API Key即将过期，请尽快更新系统配置。",
      time: "2024-08-27 03:11",
      tag: "系统配置",
    },
    {
      id: 3,
      user: "李世斌",
      title: "新增的DeepSeek模型需要进行性能测试，请安排时间。",
      time: "2024-08-28 15:04",
      tag: "模型测试",
    },
    {
      id: 4,
      user: "郑文钧",
      title: "知识库索引模型需要更新，请检查向量模型配置。",
      time: "2024-08-29 09:14",
      tag: "知识库",
    },
  ];

  // 最新文档数据
  const recentDocs = [
    {
      id: 1,
      title: "智能体开发指南V2.0.xlsx",
      type: "xlsx",
      path: "/docs/1",
    },
    {
      id: 2,
      title: "DeepSeek-R1模型接入文档.ppt",
      type: "ppt",
      path: "/docs/2",
    },
    {
      id: 3,
      title: "知识库向量检索最佳实践.docx",
      type: "docx",
      path: "/docs/3",
    },
  ];

  // 常用联系人数据
  const contacts = [
    {
      id: 1,
      name: "张开颜",
      dept: "AI研发部",
      phone: "17712341234"
    },
    {
      id: 2,
      name: "钱科为",
      dept: "模型训练组",
      phone: "13009634533"
    },
    {
      id: 3,
      name: "王明",
      dept: "知识库管理",
      phone: "13800138000"
    }
  ];

  // 日程数据
  const scheduleEvents = [
    {
      id: 1,
      time: "8:00-11:00",
      type: "模型训练",
      content: "DeepSeek-R1模型微调与测试",
      color: "#409EFF"
    },
    {
      id: 2,
      time: "9:00-11:00",
      type: "智能体开发",
      content: "客服智能体对话流程优化",
      color: "#67C23A"
    },
    {
      id: 3,
      time: "9:00-11:00",
      type: "知识库更新",
      content: "产品文档知识库向量更新",
      color: "#E6A23C"
    }
  ];

  const navigateToPath = (path: string) => {
    navigate({
      to: path,
    });
  };

  // 日历事件渲染
  const renderDateCellContent = (date: Date) => {
    const day = date.getDate();
    const isToday = new Date().getDate() === day &&
      new Date().getMonth() === date.getMonth() &&
      new Date().getFullYear() === date.getFullYear();

    // 假设7号有事件
    const hasEvent = day === 7 || day === new Date().getDate();

    return (
      <div className={`${isToday ? 'text-[#409EFF] font-semibold' : ''} 
                      ${hasEvent ? 'font-bold' : ''} 
                      flex items-center justify-center w-full h-full`}>
        {day}
        {hasEvent && <div className="w-1 h-1 bg-[#409EFF] rounded-full absolute bottom-1"></div>}
      </div>
    );
  };

  // 获取天气信息
  const weather = {
    temperature: "24°",
    location: "河北·石家庄",
  };

  return (
    <div className="relative h-full w-full overflow-auto home-container">
      {/* 添加背景装饰 */}
      <BackgroundDecoration />

      <div className="p-4 relative z-10">
        {/* 整体左右结构布局 */}
        <div className="flex flex-col lg:flex-row gap-4">
          {/* 左侧内容区域 */}
          <div className="lg:w-4/6">
            {/* 顶部区域 - 问候语和通知公告 - 移除背景色和阴影 */}
            <div className="mb-4 overflow-hidden">
              <div className="flex items-center">
                <div className="p-4">
                  <div className="flex items-center">
                    <div>
                      <Title heading={4} style={{ fontSize: '30px', lineHeight: '28px', fontWeight: 600, color: '#303133', margin: 0 }}>
                        早上好，{userInfo?.user.userName}
                      </Title>
                      <div className="flex items-center mt-1">
                        <span className="text-yellow-500 mr-1">☀</span>
                        <Text type="tertiary" style={{ fontSize: '12px', lineHeight: '20px', color: '#909399' }}>
                          {weather.temperature} {weather.location}
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>
                <Divider layout="vertical" className="h-full" />
                <div className="p-4 flex-1">
                  <div className="flex items-center">
                    <Title heading={5} style={{ fontSize: '22px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                      通知公告
                    </Title>
                  </div>
                  {/* 使用自定义通知公告组件替换Carousel */}
                  <NoticeAnnouncement
                    notices={announcements}
                    interval={5000}
                    className="mt-2"
                  />
                </div>
              </div>
            </div>

            {/* 应用导航区域 - 调整为一排3个 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {mainApps.map((app) => (
                <motion.div
                  key={app.id}
                  className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer overflow-hidden group"
                  onClick={() => navigateToPath(app.path)}
                  whileHover={{
                    scale: 1.02,
                    y: -5,
                    transition: { duration: 0.3 }
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="p-5">
                    {/* 图标部分 */}
                    <div className="mb-4">
                      <div
                        className="w-[50px] h-[50px] rounded-lg flex items-center justify-center text-semi-color-white shadow-lg"
                        style={{
                          background: `linear-gradient(135deg, ${app.bgColor} 0%, ${app.bgColor}dd 100%)`,
                          boxShadow: `0 8px 16px -4px ${app.bgColor}66, 0 4px 6px -2px rgba(0,0,0,0.05)`,
                        }}
                      >
                        {app.icon}
                      </div>
                    </div>
                    {/* 标题和按钮在同一行 */}
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-xl text-semi-color-primary-active font-semibold">
                        {app.title}
                      </div>

                      <div className="flex flex-row gap-2 hover:bg-semi-color-fill-0 p-1 rounded-md transition-all duration-300 group-hover:bg-zinc-50">
                        <span className="text-semi-color-primary font-semibold">进入系统</span>
                        <motion.div
                          animate={{ x: [0, 3, 0] }}
                          transition={{
                            repeat: Infinity,
                            repeatType: "reverse",
                            duration: 1,
                            ease: "easeInOut"
                          }}
                        >
                          <Icon className="text-semi-color-primary" svg={<ArrowRight />} />
                        </motion.div>
                      </div>
                    </div>

                    {/* 描述文本 */}
                    <p className="text-[14px] text-[#64748b] line-clamp-2 mb-4">
                      {app.description}
                    </p>

                    {/* 底部分隔线 - 红框底部的线 */}
                    <div className="border-t border-[#e5e7eb] mt-2 pt-2"></div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* 待办区域 */}
            <Card className="shadow-sm border-0 mb-4" style={{ borderRadius: '8px' }}>
              <div className="flex justify-between items-center mb-3">
                <Title heading={5} style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                  待办
                </Title>
                <Button theme="borderless" type="tertiary" icon={<IconChevronRight />} style={{ color: '#909399' }}>
                  更多
                </Button>
              </div>
              {todoItems.length > 0 ? (
                <List
                  dataSource={todoItems}
                  renderItem={item => (
                    <List.Item
                      className="hover:bg-gray-50 cursor-pointer rounded-lg"
                      style={{ padding: '12px', borderBottom: '1px solid #DCDFE6' }}
                      main={
                        <div className="flex items-start">
                          <Avatar color="#409EFF" size="small" className="mr-3">{item.user.charAt(0)}</Avatar>
                          <div className="flex-1">
                            <div className="flex items-center mb-1">
                              <Text style={{ fontSize: '14px', lineHeight: '22px', fontWeight: 600, color: '#303133' }}>
                                {item.user}
                              </Text>
                              <Tag color="orange" size="small" className="ml-2">{item.tag}</Tag>
                            </div>
                            <Text style={{ fontSize: '14px', lineHeight: '22px', color: '#606266' }}>
                              {item.title}
                            </Text>
                            <div className="flex items-center mt-1">
                              <IconCalendar size="small" className="mr-1" style={{ color: '#909399' }} />
                              <Text style={{ fontSize: '12px', lineHeight: '20px', color: '#909399' }}>
                                {item.time}
                              </Text>
                            </div>
                          </div>
                        </div>
                      }
                      extra={
                        <Button
                          theme="borderless"
                          type="primary"
                          size="small"
                          style={{ color: '#409EFF' }}
                        >
                          前往处理
                        </Button>
                      }
                    />
                  )}
                />
              ) : (
                <Empty description="暂无待办" />
              )}
            </Card>

            {/* 全部应用区域 - 完全重构样式 */}
            <div className="bg-white rounded-lg shadow-sm p-4 mt-4 mb-4">
              <div className="flex justify-between items-center mb-4">
                <Title heading={5} style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                  全部应用
                </Title>
                <div className="flex items-center">
                  <Button theme="borderless" type="tertiary" icon={<IconSearch />} className="mr-2" style={{ color: '#909399' }}>
                    搜索
                  </Button>
                  <Button theme="borderless" type="tertiary" icon={<IconChevronRight />} style={{ color: '#909399' }}>
                    更多
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-4 md:grid-cols-5 gap-6">
                {allApps.map((app) => (
                  <div
                    key={app.id}
                    className="flex flex-col items-center cursor-pointer group"
                    onClick={() => navigateToPath(app.path)}
                  >
                    <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-2 bg-gray-100 group-hover:bg-gray-200 transition-all">
                      <img src={app.icon} alt={app.title} className="w-6 h-6" />
                    </div>
                    <Text className="text-center text-xs text-gray-600 group-hover:text-gray-800">
                      {app.title}
                    </Text>
                  </div>
                ))}
              </div>
            </div>
            {/* 最新文档区域 */}
            <Card className="shadow-sm border-0 mb-4 mt-4" style={{ borderRadius: '8px', marginBottom: "16px" }}>
              <div className="flex justify-between items-center mb-3">
                <Title heading={5} style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                  最新文档
                </Title>
                <Button theme="borderless" type="tertiary" icon={<IconChevronRight />} style={{ color: '#909399' }}>
                  更多
                </Button>
              </div>
              <List
                dataSource={recentDocs}
                renderItem={item => (
                  <List.Item
                    className="hover:bg-gray-50 cursor-pointer rounded-lg"
                    style={{ padding: '12px', borderBottom: '1px solid #DCDFE6' }}
                    main={
                      <div className="flex items-center">
                        <div
                          className={`w-8 h-8 rounded flex items-center justify-center mr-3 text-xs ${item.type === 'xlsx' ? 'bg-green-100 text-green-600' :
                            item.type === 'ppt' ? 'bg-orange-100 text-orange-600' :
                              'bg-blue-100 text-blue-600'
                            }`}
                        >
                          {item.type.toUpperCase()}
                        </div>
                        <Text
                          ellipsis={{ showTooltip: true }}
                          style={{ fontSize: '14px', lineHeight: '22px', color: '#606266' }}
                        >
                          {item.title}
                        </Text>
                      </div>
                    }
                  />
                )}
              />
            </Card>

          </div>

          {/* 右侧内容区域 */}
          <div className="lg:w-2/6 flex flex-col">
            {/* 日程管理区域 */}
            <Card className="shadow-sm border-0 mb-4 w-full" style={{ borderRadius: '8px', marginBottom: "16px" }}>
              <div className="flex justify-between items-center mb-3">
                <Title heading={5} style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                  日程管理
                </Title>
                <div className="flex">
                  <Button theme="borderless" type="tertiary" style={{ padding: '0 4px', color: '#909399' }}>‹</Button>
                  <Button theme="borderless" type="tertiary" style={{ padding: '0 4px', color: '#909399' }}>›</Button>
                </div>
              </div>

              <Calendar
                mode="month"
                height="300px"
                value={selectedDate}
                onChange={date => setSelectedDate(date)}
                renderDateCellContent={renderDateCellContent}
                style={{
                  '--semi-color-primary': '#409EFF',
                  '--semi-color-primary-hover': '#66b1ff'
                } as React.CSSProperties}
              />

              <Divider margin="12px" />
              <div className="space-y-3">
                {scheduleEvents.map(event => (
                  <div key={event.id}>
                    <div className="flex items-center mb-1">
                      <div
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: event.color }}
                      ></div>
                      <Text style={{ fontSize: '14px', lineHeight: '22px', fontWeight: 600, color: '#303133' }}>
                        {event.time}
                      </Text>
                    </div>
                    <div className="flex items-start pl-4">
                      <div
                        className="p-2 rounded-lg w-full"
                        style={{ backgroundColor: `${event.color}10` }}
                      >
                        <Text style={{ fontSize: '14px', lineHeight: '22px', fontWeight: 600, color: '#303133' }}>
                          {event.type}
                        </Text>
                        <div style={{ fontSize: '12px', lineHeight: '20px', color: '#606266' }}>
                          {event.content}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>


            {/* 常用联系人区域 */}
            <Card className="shadow-sm border-0" style={{ borderRadius: '8px', marginBottom: "16px" }}>
              <div className="flex justify-between items-center mb-3">
                <Title heading={5} style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133', margin: 0 }}>
                  常用联系人
                </Title>
                <Button theme="borderless" type="tertiary" icon={<IconChevronRight />} style={{ color: '#909399' }}>
                  更多
                </Button>
              </div>
              <List
                dataSource={contacts}
                renderItem={item => (
                  <List.Item
                    className="hover:bg-gray-50 cursor-pointer rounded-lg"
                    style={{ padding: '12px', borderBottom: '1px solid #DCDFE6' }}
                    main={
                      <div>
                        <Text style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 600, color: '#303133' }}>
                          {item.name}
                        </Text>
                        <Text style={{ fontSize: '12px', lineHeight: '20px', color: '#909399' }}>
                          {item.dept} · {item.phone}
                        </Text>
                      </div>
                    }
                  />
                )}
              />
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
