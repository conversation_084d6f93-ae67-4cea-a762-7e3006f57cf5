import React, { createContext, useContext, useState } from 'react';

interface CodeEditorContextType {
  isEditing: boolean;
  currentCode: string;
  language: string;
  elementId: string;
  onCodeChange?: (newCode: string) => void;
  openEditor: (code: string, language: string, elementId: string, onCodeChange?: (newCode: string) => void) => void;
  closeEditor: () => void;
  updateCode: (newCode: string) => void;
  saveChanges: () => void;
}

const CodeEditorContext = createContext<CodeEditorContextType>({
  isEditing: false,
  currentCode: '',
  language: '',
  elementId: '',
  openEditor: () => { },
  closeEditor: () => { },
  updateCode: () => { },
  saveChanges: () => { },
});

export const CodeEditorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [currentCode, setCurrentCode] = useState('');
  const [language, setLanguage] = useState('');
  const [elementId, setElementId] = useState('');
  const [onCodeChange, setOnCodeChange] = useState<((newCode: string) => void) | undefined>(undefined);

  const openEditor = (
    code: string,
    language: string,
    elementId: string,
    onCodeChange?: (newCode: string) => void
  ) => {
    setCurrentCode(code);
    setLanguage(language);
    setElementId(elementId);
    setOnCodeChange(() => onCodeChange);
    setIsEditing(true);
  };

  const closeEditor = () => {
    setIsEditing(false);
  };

  const updateCode = (newCode: string) => {
    setCurrentCode(newCode);
  };

  const saveChanges = () => {
    if (onCodeChange) {
      onCodeChange(currentCode);
    }
    closeEditor();
  };

  return (
    <CodeEditorContext.Provider
      value={{
        isEditing,
        currentCode,
        language,
        elementId,
        onCodeChange,
        openEditor,
        closeEditor,
        updateCode,
        saveChanges,
      }}
    >
      {children}
    </CodeEditorContext.Provider>
  );
};

export const useCodeEditor = () => useContext(CodeEditorContext);
