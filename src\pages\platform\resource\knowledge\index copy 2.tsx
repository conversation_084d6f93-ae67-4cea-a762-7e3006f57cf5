import { Knowledge } from "@/components/icon/KnowledgeIcon";
import Icon, {
  IconComment,
  IconMore,
  IconPlus,
  IconSearch,
  IconTickCircle,
} from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Form,
  Input,
  List,
  Row,
  Space,
  Table,
  Tag,
} from "@douyinfe/semi-ui";
import React, { useState, useEffect, useMemo } from "react";
import DatasetsForm from "./component/KnowledgeForm";
import { useBoolean } from "@/hooks";
import { getDatasetsList } from "@/api/platform/knowledge";
import "./styles/index.scss";
function Datasets() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  const [loading, setLoading] = useState(false);
  const [currentPage, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [dataSource, setData] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const columns = useMemo(
    () => [
      {
        title: "知识库",
        dataIndex: "name",
        render: (text: string) => {
          return (
            <div className="flex items-center gap-2">
              <Avatar
                shape="square"
                size="default"
                style={{ backgroundColor: "#87d068" }}
              >
                {text.substring(0, 2)}
              </Avatar>
              <div className="flex flex-col gap-1">
                <div>{text}</div>
                <Space>
                  <Tag size="small"> 本地</Tag>
                  <Tag size="small" color="light-blue">
                    测试
                  </Tag>
                  <Tag size="small" color="cyan">
                    其他
                  </Tag>
                </Space>
              </div>
            </div>
          );
        },
      },
      {
        title: "描述",
        dataIndex: "description",
      },
      {
        title: "创建人",
        dataIndex: "createdByName",
      },
      {
        title: "创建日期",
        dataIndex: "createdAt",
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (text: number) => {
          const tagConfig = {
            0: { color: "green", prefixIcon: <IconTickCircle />, text: "正常" },
            1: { color: "cyan", prefixIcon: <IconComment />, text: "停用" },
          };
          const tagProps = tagConfig[text];
          return (
            <Tag shape="circle" {...tagProps} style={{ userSelect: "text" }}>
              {tagProps.text}
            </Tag>
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        align: "center",
        render: () => {
          return <IconMore />;
        },
      },
    ],
    []
  );

  const getDatasets = (page = 1) => {
    setLoading(true);
    getDatasetsList({
      pageNum: page,
      pageSize: pageSize,
    })
      .then((ret) => {
        // setLoading(false);
        setData(ret?.rows);
        setTotal(ret.total as number);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handlePageChange = (page: number) => {
    setPage(page);
    getDatasets(page);
  };
  useEffect(() => {
    getDatasets();
  }, []);

  return (
    <div className="h-full relative flex flex-col gap-2">
      <div className="text-nowrap font-extrabold pl-4 pt-2 text-lg mb-2 flex items-center justify-between">
        <div>知识库管理</div>
        <div className="flex gap-2 items-center">
          <Button theme="solid" type="primary" onClick={setTrue}>
            创建知识库
          </Button>
          {/* <div className="w-[300px]">
            <Input prefix={<IconSearch />} className="w-[300px]" showClear />
          </div> */}
        </div>
      </div>
      <div className="justify-end flex items-center pl-1">
        <div className="w-[300px]">
          <Input prefix={<IconSearch />} className="w-full" showClear />
        </div>
      </div>
      <Card className="flex-1 w-full">
        <Table
          scroll={{
            x: "1200px",
            y: "calc(100vh  - 200px)",
          }}
          // loading={loading}
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          // pagination={{
          //   currentPage,
          //   pageSize: pageSize,
          //   total: total,
          //   onPageChange: handlePageChange,
          // }}
        />
      </Card>
      <DatasetsForm open={formOpen} onCancel={setFalse} onOK={setFalse} />
    </div>
  );
}

export default Datasets;
