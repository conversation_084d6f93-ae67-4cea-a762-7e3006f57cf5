import { getUserInfo } from "@/api/system/user";

export type UserInfoVo = {
  user: User,
  permissions:Array<string> | string,
  roles:Array<Role>,
  company:Company
};

// 角色类型
type Role = API.BaseEntity & {
  roleId: number;
  roleName: string;
  roleKey: string;
  roleSort: number;
  dataScope: string;
  menuCheckStrictly?: boolean | null;
  deptCheckStrictly?: boolean | null;
  status: string;
  flag?: boolean;
  superAdmin?: boolean;
};

// 公司类型
type Company = API.BaseEntity & {
  id: number;
  tenantId: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  address: string;
  domain: string;
  intro: string;
  packageId?: number | null;
  expireTime?: string | null;
  accountCount: number;
  status: string;
  logo: string;
  zipCode: string;
  industry: string;
  size: string;
};

// 用户类型（泛型）
type User<T extends Role = Role, U extends Company = Company> = API.BaseEntity & {
  userId: number;
  tenantId: string;
  deptId: number;
  userName: string;
  nickName: string;
  userType: "sys_user";
  email: string;
  phonenumber: string;
  sex: "0";
  avatar: string;
  status: "0";
  loginIp: string;
  loginDate: string;
  roles: T[];
  roleIds?: number[] | null;
  postIds?: number[] | null;
  roleId?: number | null;
  signature: string;
  birthday: string;
  permissions?: string[];
  company?: U;
};

export interface UserSliceState {
  userInfo: UserInfoVo | null;
  loading: boolean;
  error: string | null;
  fetchUserInfo: () => Promise<void>;
  clearUserInfo: () => void;
}

export const createUserSlice = (set: any, get: any): UserSliceState => ({
  userInfo: null,
  loading: false,
  error: null,

  fetchUserInfo: async () => {
    set({ loading: true, error: null });
    try {
      const response = await getUserInfo(); // 假设你已导入该 API
      set({ userInfo: response.data, loading: false });
    } catch (error) {
      const errorMessage = "Failed to fetch user info";
      set({ error: errorMessage, loading: false });
      // throw new Error(errorMessage);
    }
  },
  clearUserInfo: () => {
    set({ userInfo: null });
  }
});