import { getChatModelSelectOptions } from "@/api/platform/chatModel";
import { deleteDataset, getDatasetList } from "@/api/platform/dataset";
import { getKnowledge } from "@/api/platform/knowledge";
import Knowledge from "@/components/icon/Knowledge";
import { IconDelete, IconDeleteStroked, IconEdit, IconEditStroked, IconMore, IconPlus } from "@douyinfe/semi-icons";
import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import {
  Avatar,
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  DropdownDivider,
  Empty,
  Form,
  Input,
  Modal,
  Pagination,
  Popover,
  Row,
  Skeleton,
  Space,
  Table,
  TabPane,
  Tabs,
  Tag,
  Toast,
  Typography,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import { useNavigate, useParams } from "@tanstack/react-router";
import React, { useEffect, useMemo, useRef, useState } from "react";
import UploadTextForm from "./UploadTextForm";
import { useBoolean } from "@/hooks";
import useDictionary from "@/hooks/useDictionary";
import DictTag from "@/components/DictTag";
import KnowledgeDetailSidebar from "./KnowledgeDetailSidebar";
import SearchBar from "./SearchBar";
import KnowledgeDocumentsTable from "./KnowledgeDocumentsTable";
import RecallTestTab from "./RecallTestTab";
import { useTable } from "@/hooks/useTables";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import './styles.scss'
import { debounce } from "lodash-es";

export default function KnowledgeDetails() {
  const scroll = useMemo(() => ({ y: 600, x: "100px" }), []);
  const [aiModels, setAiModels] = useState([]);
  const navigate = useNavigate();
  const formRef = useRef();
  const [uploadOpen, { setTrue: openUpload, setFalse: closeOpen }] =
    useBoolean(false);
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_dataset_origin",
    "sys_normal_disable",
    "ai_chuck_type",
    "ai_chunk_status",
  ]);
  useEffect(() => {
    loadDictionary();
  }, []);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const { id } = useParams({ strict: false });
  const [knowledgeInfo, setKnowledgeInfo] = React.useState<any>({});
  useEffect(() => {
    if (id) {
      getDetails(id);
      getAiChatModel();
      // fetchData();
    }
  }, [id]);

  const getDetails = (idParams: any) => {
    getKnowledge(idParams).then(({ data }) => {
      setKnowledgeInfo(data);
      // @ts-expect-error
      formRef.current.setValues({
        embedModel: data?.embedModel,
      });
    });
  };
  const handleGotoSegment = (segmentId: any) => {
    navigate({
      to: `/platform/resource/knowledge/details/${id}/segment/${segmentId}`,
    });
  };

  const handleRemoveDataset = (id: number) => {
    const modal = Modal.warning({
      title: "确认删除数据集",
      content: "删除后将无法恢复，请谨慎操作！",
      // confirmLoading: false,
      onOk: async () => {
        // modal.update({
        //   confirmLoading: true,
        // });
        // 删除数据集
        return await deleteDataset(id)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getDatasetList,
    params: {
      knowledgeId: id,
    },
    columns: [
      {
        title: "名称",
        dataIndex: "name",
        ellipsis: true,
        toolTip: true,
        render: (text: string, row: any) => (
          <Typography.Text link={{ href: '#' }} onClick={() => {
            handleGotoSegment(row.id);
          }} ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "字符数",
        dataIndex: "wordCount",
      },
      {
        title: "分片量",
        dataIndex: "chunkNum",
      },
      {
        title: "来源",
        dataIndex: "origin",
        render: (origin: any) => {
          return (
            <DictTag
              dictType="ai_dataset_origin"
              dictValue={origin}
              dictionaryData={dictionaryData.ai_dataset_origin || []}
            />
          );
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (status: any) => {
          return (
            <DictTag
              dictType="sys_normal_disable"
              dictValue={String(status)}
              dictionaryData={dictionaryData.sys_normal_disable || []}
            />
          );
        },
      },
      {
        title: "分片状态",
        dataIndex: "chunkStatus",
        render: (chunkStatus: any) => {
          return (
            <DictTag
              showIcon={true}
              dictType="ai_chunk_status"
              dictValue={String(chunkStatus)}
              dictionaryData={dictionaryData.ai_chunk_status || []}
            />
          );
        },
      },
      {
        title: "上传时间",
        dataIndex: "createdAt",
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
        // render: (text: any) => {
        //   return <Typography.Text>{text ? text : "-"}</Typography.Text>;
        // },
      },
      {
        title: "",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <Dropdown
              zIndex={1}
              position="bottom"
              trigger={"click"}
              render={
                <Dropdown.Menu>
                  <Dropdown.Item onClick={(e) => {
                    e.stopPropagation()
                  }} icon={<IconEditStroked />}>重命名文件</Dropdown.Item>
                  <DropdownDivider />
                  <Dropdown.Item onClick={(e) => {
                    handleRemoveDataset(row.id)
                  }} type="danger" icon={<IconDeleteStroked />}>
                    删除
                  </Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                type="tertiary"
                theme="borderless"
                icon={<IconMore className="cursor-pointer" />}
              ></Button>
            </Dropdown>
          );
        },
      },
    ],
    onRowSelect: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  });
  const getAiChatModel = () => {
    getChatModelSelectOptions(1).then(({ data }) => {
      setAiModels(data);
    });
  };
  const [keyword, setKeyword] = useState<string>("");
  const handleQuery = () => {
    setSearchParams({
      name: keyword,
    });
    refresh();
  }

  // fetchData(initPageInfo.page, initPageInfo.pageSize, {
  //   name: keyword,
  // });
  const handleQueryReset = () => {
    // fetchData(initPageInfo.page, initPageInfo.pageSize);
  };

  // 新增事件处理函数
  const handleTagsUpdate = (newTags: string[]) => {
    // 实际应调用API更新标签
    setKnowledgeInfo((prev: any) => ({ ...prev, tags: newTags.join(",") }));
  };

  return (
    <div className="h-full rounded-md  flex gap-2 w-full p-2">
      <Row className="flex-1" type="flex" gutter={[12, 12]} justify="start">
        <Col span={20} className="h-full">
          <div className="h-full flex-col bg-semi-color-white flex rounded-md px-3 gap-2" style={{ height: "100%" }}>
            <Tabs type="line" className="knowledgeTab h-full w-full" contentStyle={{ height: "calc(100% - 70px)", width: "100%" }}>
              <TabPane tab="知识库文档" itemKey="1" style={{ height: "100%" }}>
                <div className="flex gap-2 flex-col pt-1" style={{ height: "100%", width: "100%" }}>
                  <SearchBar
                    keyword={keyword}
                    onChange={(v) => setKeyword(v)}
                    onSearch={handleQuery}
                    onReset={handleQueryReset}
                    onUpload={openUpload}
                  />
                  <KnowledgeDocumentsTable
                    columns={columns}
                    clasName={"h-full w-full"}
                    dataSource={dataSource}
                    loading={loading}
                    pagination={false}
                    rowSelection={rowSelection as any}
                  // onRow={(_record: any, index: any) => {
                  //   return {
                  //     onClick: () => {
                  //       handleGotoSegment(_record.id);
                  //     }, // 点击行
                  //     className: "cursor-pointer",
                  //   };
                  // }}
                  />
                  {pagination.total > 0 && (
                    <div className="flex justify-end">
                      <Pagination {...pagination}></Pagination>
                    </div>
                  )}


                </div>

              </TabPane>
              <TabPane tab="召回测试" itemKey="2">
                <RecallTestTab />
              </TabPane>
            </Tabs>
          </div>
        </Col>
        <Col span={4} className="h-full">
          <KnowledgeDetailSidebar
            knowledgeInfo={knowledgeInfo}
            aiModels={aiModels}
            formRef={formRef}
            onTagsUpdate={handleTagsUpdate}
          />
        </Col>
      </Row>
      {
        selectedRowKeys.length > 0 && (
          <div
            style={{ left: '50%', transform: 'translate(-50%, 0)' }}
            className="shadow-xl bg-semi-color-primary-light-default gap-1 text-sm flex items-center px-2 py-2 rounded-md fixed bottom-10 border border-solid border-semi-color-primary h-[40px] w-[500px] 
                 opacity-0 translate-y-4 transition-all duration-300 ease-out 
                 data-[show]:opacity-100 data-[show]:translate-y-0"
            data-show
          >
            <div className="flex items-center ">
              <span>已选择：</span>
              <Badge count={selectedRowKeys.length} />
            </div>
            <div className="mx-2">
              <Divider layout="vertical" />
            </div>
            <div>
              <Button type="tertiary" size="small" theme="borderless">禁用</Button>
            </div>
            <div>
              <Button type="danger" size="small" theme="borderless">删除</Button>
            </div>
          </div>
        )
      }

      <UploadTextForm
        open={uploadOpen}
        onCancel={closeOpen}
        knowledgeId={id}
        onOK={function (values: any): void {
          // throw new Error("Function not implemented.");
        }}
      />
    </div >
  );
}
