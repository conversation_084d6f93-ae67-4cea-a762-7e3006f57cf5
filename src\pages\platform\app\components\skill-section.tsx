import React from 'react';
import { Collapsible, But<PERSON>, Divider, Icon } from '@douyinfe/semi-ui';

interface SkillSectionProps {
    toolOpen: boolean;
    setToolOpen: (open: boolean) => void;
}

export const SkillSection: React.FC<SkillSectionProps> = ({
    toolOpen,
    setToolOpen
}) => {
    return (
        <Timeline.Item dot={<div className="w-5 h-5 rounded-full flex items-center justify-center bg-semi-color-primary-light-hover"><Icon svg={<JinengIcon />} /></div>}>
            <div className="text-sm font-semibold mb-1">技能</div>

            <div className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between my-1" onClick={() => setToolOpen(!toolOpen)}>
                <div className="text-semi-color-text-1">工具</div>
                <div className="flex items-center gap-2">
                    <Button size="small" theme="borderless" type="tertiary" icon="plus" />
                    <Divider layout="vertical" />
                    <Button size="small" theme="borderless" type="tertiary" icon={toolOpen ? "chevronUp" : "chevronDown"} onClick={(e) => { e.stopPropagation(); setToolOpen(!toolOpen); }} />
                </div>
            </div>

            <Collapsible isOpen={toolOpen}>
                <div className="flex items-center justify-between hover:bg-semi-color-fill-0 px-2 rounded-md">
                    <div className="flex items-center gap-2">
                        <IconWrench />
                        <div>天气查询</div>
                    </div>
                    <div>
                        <Button type="danger" theme="borderless" icon="deleteStroked" />
                    </div>
                </div>
            </Collapsible>
        </Timeline.Item>
    );
};