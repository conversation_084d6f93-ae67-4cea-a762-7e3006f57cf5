.gedit-selector-bounds-background {
    cursor: move;
    display: none !important;
}

.gedit-selector-bounds-foreground {
    cursor: move;
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 0;
    outline: 1px solid var(--g-playground-selectBox-outline);
    z-index: 33;
    background-color: var(--g-playground-selectBox-background);
}

.demo-editor {
    flex-grow: 1;
    position: relative;
    height: 100%;
}

.editor-container {
    position: absolute;
    left: 0px;
    top: 0px;
    display: flex;
    width: 100%;
    height: 100%;
    flex-direction: column;
}

.demo-tools {
    padding: 10px;
    display: flex;
    justify-content: space-between;
}

.demo-tools-group > * {
    margin-right: 8px;
}

.mouse-pad-option-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}
