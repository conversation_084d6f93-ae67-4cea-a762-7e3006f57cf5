/* 变量高亮样式 */
.variable-highlight {
  background-color: rgba(var(--semi-blue-0), 0.6);
  border-radius: 4px;
  padding: 2px 4px;
  display: inline-block;
}

/* 提示词输入框容器样式 */
.prompt-textarea-container {
  position: relative;
}

/* 提示词高亮覆盖层样式 */
.prompt-highlight-overlay {
  background-color: transparent;
  color: transparent;
  caret-color: transparent;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 可拖拽变量样式 */
.draggable-variable {
  cursor: grab;
  padding: 4px 8px;
  background-color: var(--semi-color-primary-light-default);
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: inline-block;
  user-select: none;
}

.draggable-variable:active {
  cursor: grabbing;
}