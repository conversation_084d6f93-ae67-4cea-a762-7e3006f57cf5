import { <PERSON><PERSON>, <PERSON><PERSON>, Divider } from '@douyinfe/semi-ui'
import { motion } from 'framer-motion'
import React from 'react';

export default function TableOperation({
    selectedRowKeys,
    children,
    width = "500px"
}: any) {
    React.useEffect(() => {
        console.log('TableOperation selectedRowKeys:', selectedRowKeys);
    }, [selectedRowKeys]);
    return (
        <>
            {selectedRowKeys.length > 0 && (
                <motion.div
                    key={selectedRowKeys.length}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                    style={{ left: '50%', transform: 'translateX(-50%)', width: width }}
                    className="shadow-xl z-10 bg-semi-color-primary-light-default gap-1 text-sm flex items-center px-2 py-2 rounded-md fixed bottom-10 border border-solid border-semi-color-primary h-[40px] "
                >
                    <div className="flex items-center">
                        <span>已选择：</span>
                        <Badge count={selectedRowKeys.length} />
                    </div>
                    <div className="mx-2">
                        <Divider layout="vertical" />
                    </div>
                    {
                        children
                    }
                </motion.div>
            )}
        </>
    )
}