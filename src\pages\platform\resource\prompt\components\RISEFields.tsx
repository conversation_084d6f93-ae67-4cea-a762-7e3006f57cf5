import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const RISEFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.role"
          label="角色"
          rows={6}
          placeholder="指定ChatGPT的角色"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.input"
          label="输入"
          rows={6}
          placeholder="描述要解决的问题"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.steps"
          label="步骤"
          rows={6}
          placeholder="简述操作步骤"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.expectation"
          label="期望"
          rows={6}
          placeholder="描述预期的结果"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default RISEFields;