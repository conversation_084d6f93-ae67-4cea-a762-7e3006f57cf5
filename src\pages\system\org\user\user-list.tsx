import { delUser, getUserList } from "@/api/system/user";
import DictTag from "@/components/DictTag";
import FilterComponent from "@/components/FilterComponent";
import useDictionary from "@/hooks/useDictionary";
import { useTable } from "@/hooks/useTables";
import { Avatar, Button, Divider, Dropdown, DropdownDivider, Empty, Input, Modal, Pagination, Table, Toast, Typography } from "@douyinfe/semi-ui";
import { useEffect, useState } from "react";
import { tableFilterSchema } from "./constatns";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import Icon, { IconDelete, IconEdit2Stroked, IconEditStroked, IconKeyStroked, IconMore, IconPlusStroked, IconRedoStroked, IconRefresh, IconRefresh2, IconUserListStroked } from "@douyinfe/semi-icons";
import TableColumnFilter from "@/components/TableColumnFilter";
import ManIcon from "@/components/icon/ManIcon";
import FemanIcon from "@/components/icon/FemanIcon";
import UserForm from "./user-form";
import TableOperation from "@/components/TableOperation";
interface UserAvatarWithNameProps {
  avatar?: string;
  name: string;
  sex: string
}
import RestPassword from "./rest-password";
import AuthRole from "./auth-role";
import EditUserForm from "./edut-user-form";

const UserAvatarWithName = ({ avatar, name, sex }: UserAvatarWithNameProps) => {
  const [avatarSrc, setAvatarSrc] = useState(avatar || '');

  const handleImageError = () => {
    setAvatarSrc('');
  };

  return (
    <div className="flex items-center gap-2">
      {avatarSrc ? (
        <Avatar src={avatarSrc} size="small" onError={handleImageError} />
      ) : (
        <Avatar size="small" style={{ color: '#f56a00', backgroundColor: '#fde3cf' }} >
          {name.slice(0, 2)}
        </Avatar>
      )}
      <Typography.Text ellipsis={{ showTooltip: true }}>
        {name}
      </Typography.Text>
      <div>
        {
          sex === "0" ? <Icon svg={<ManIcon />} /> : <Icon svg={<FemanIcon />} />
        }
      </div>
    </div>
  );
};
export const UserList = ({ deptId, postId, deptData }: any) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [authRoleOpen, setAuthRoleOpen] = useState(false);
  const [currentId, setCurrentId] = useState<any>(null);
  const [restPasswordOpen, setRestPasswordOpen] = useState(false);
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_user_sex"
  ]);
  const [open, setOpen] = useState(false);
  const [editFormOpen, setEditFormOpen] = useState(false);
  useEffect(() => {
    loadDictionary();
    if (deptId) {
      setSearchParams({
        deptId: deptId,
      });
      refresh();
    }
  }, [deptId]);

  useEffect(() => {
    if (postId) {
      setSearchParams({
        postId: postId,
      });
      refresh();
    }
  }, [postId]);
  const handleRemoveUser = (id: number) => {
    const modal = Modal.warning({
      title: "确认删除用户数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        return await delUser(id)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };
  const handleBatchDelete = () => {
    const modal = Modal.warning({
      title: "确认删除用户数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await delUser(selectedRowKeys)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            setSelectedRowKeys([])
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  }
  const {
    dataSource,
    loading,
    columns,
    visibleColumns,
    updateVisibleColumns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getUserList,
    params: {
      deptId: deptId,
    },
    columns: [
      {
        title: "用户名",
        dataIndex: "userName",
        ellipsis: true,
        fixed: "left",
        toolTip: true,
        render: (text: string, row: any) => (
          <UserAvatarWithName avatar={row.avatar} sex={row.sex} name={text} />
        )
      },
      {
        title: "昵称",
        dataIndex: "nickName",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "邮箱",
        dataIndex: "email",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "手机",
        dataIndex: "phonenumber",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      // {
      //   title: "生日",
      //   dataIndex: "birthday",
      //   render: (text: any) => {
      //     return (
      //       <Typography.Text ellipsis={{ showTooltip: true }}>
      //         {text ? text : "-"}
      //       </Typography.Text>
      //     );
      //   },
      // },
      // {
      //   title: "性别",
      //   dataIndex: "sex",
      //   render: (text: any) => {
      //     return (
      //       <DictTag
      //         dictType="sys_user_sex"
      //         dictValue={text}
      //         dictionaryData={dictionaryData.sys_user_sex || []}
      //       />
      //     );
      //   },
      // },

      {
        title: "状态",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_normal_disable"
              dictValue={text}
              dictionaryData={dictionaryData.sys_normal_disable || []}
            />
          );
        },
      },
      {
        title: "注册时间",
        dataIndex: "createdAt",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },

      {
        title: "操作",
        dataIndex: "operate",
        width: 130,
        fixed: "right",
        render: (text: any, row: any) => {
          return <>
            {
              row.userId !== 1 && (
                <div className="flex flex-row gap-1 items-center">
                  <Dropdown
                    className="min-w-[180px]"
                    zIndex={1000}
                    position="bottom"
                    trigger={"click"}
                    render={
                      <Dropdown.Menu>
                        <Dropdown.Item
                          icon={<IconEditStroked />}
                          onClick={() => {
                            setEditFormOpen(true)
                            setCurrentId(row.userId)
                          }}
                        >
                          修改信息
                        </Dropdown.Item>
                        <Dropdown.Item
                          onClick={() => {
                            setCurrentId(row.userId)
                            setRestPasswordOpen(true)
                          }}
                          icon={<IconKeyStroked />}
                        >
                          重置密码
                        </Dropdown.Item>
                        <Dropdown.Item
                          onClick={() => {
                            setCurrentId(row.userId)
                            setAuthRoleOpen(true)
                          }}
                          icon={<IconUserListStroked />}
                        >
                          分配角色
                        </Dropdown.Item>
                        <DropdownDivider />
                        <Dropdown.Item
                          type="danger"
                          icon={<IconDelete />}
                          onClick={() => handleRemoveUser(row.userId)}
                        // onClick={() => handleRemoveDictData(row.dictCode)}
                        >
                          删除用户
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    }
                  >
                    <Button
                      type="tertiary"
                      theme="borderless"
                      icon={<IconMore className="cursor-pointer" />}
                    ></Button>
                  </Dropdown>
                </div>
              )
            }</>
        },
      },
    ],
    onRowSelect: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  });
  return (
    <>
      <div className="h-full w-full flex px-6 py-4 flex-col gap-2">
        <div>人数（{pagination.total}）</div>
        <div className="mt-2 w-full flex justify-between gap-2">
          <div className="flex gap-2 items-center">
            <Input
              placeholder="搜索"
              showClear
              style={{ width: 250 }}
            />
            {/* <FilterComponent schema={tableFilterSchema as any} onFilter={() => { }} /> */}
          </div>
          <div className="flex items-center gap-2">
            <Button theme="solid"
              icon={<IconPlusStroked />} onClick={() => {
                setOpen(true)
              }}>添加成员</Button>
            <TableColumnFilter
              columns={columns}
              onColumnsChange={(selectedColumns) => {
                updateVisibleColumns(selectedColumns);
              }}
            />
            <Button icon={<IconRedoStroked />} type="tertiary" onClick={() => refresh()}>
            </Button>
          </div>
        </div>
        <div className="flex-1">
          <Table
            rowSelection={{
              ...rowSelection,
              fixed: true,
              selectedRowKeys: selectedRowKeys,
              getCheckboxProps: (record: any) => ({
                disabled: record.userId === 1,
              }),
            } as any}
            columns={visibleColumns}
            className="h-full"
            dataSource={dataSource}
            loading={loading}
            size="small"
            scroll={{ x: "1500px", y: "calc(100vh - 210px)" }}
            pagination={false}
            rowKey="userId"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
          />
        </div>
        {pagination.total > 0 && (
          <div className="flex justify-between items-center">
            <div className="text-sm text-semi-color-text-2 ">
              显示第 {pagination.currentPage} 页- 每页 {pagination.pageSize} 条，共 {pagination.total} 条
            </div>
            <Pagination  {...pagination}></Pagination>
          </div>
        )}
        <TableOperation width="200px" selectedRowKeys={selectedRowKeys}>
          <div className="flex gap-4 items-center">
            <div className="cursor-pointer select-none hover:bg-semi-color-fill-0 px-2 p-1 rounded-md text-semi-color-danger" onClick={() => { handleBatchDelete() }}>批量删除</div>
            {/* <Divider layout="vertical" /> */}
            {/* <div className="cursor-pointer select-none hover:bg-semi-color-fill-0 p-1 rounded-md text-semi-color-primary" onClick={() => { }}>批量禁用</div> */}
          </div>
        </TableOperation >
      </div >
      <UserForm deptId={deptId} open={open} onCancel={() => setOpen(false)} onRefresh={refresh} title="新增用户" detps={deptData} />
      <RestPassword id={currentId} onRefresh={refresh} open={restPasswordOpen} onCancel={() => {
        setRestPasswordOpen(false)
        setCurrentId(null)
      }} />
      <AuthRole id={currentId} onCancel={() => {
        setAuthRoleOpen(false)
        setCurrentId(null)
      }
      } onRefresh={refresh} open={authRoleOpen} />
      <EditUserForm id={currentId} onRefresh={refresh} depts={deptData} open={editFormOpen} onCancel={() => {
        setEditFormOpen(false)
        setCurrentId(null)
      }} />
    </>
  );
};
