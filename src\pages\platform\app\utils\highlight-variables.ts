/**
 * 高亮提示词中的变量
 * @param text 提示词文本
 * @returns 包含高亮HTML的字符串
 */
export function highlightVariables(text: string): string {
  if (!text) return '';
  
  // 使用正则表达式匹配 {{xxx}} 格式的变量
  const regex = /\{\{([^}]+)\}\}/g;
  
  // 替换变量为带有高亮样式的HTML
  return text.replace(regex, (match, variableName) => {
    return `<span class="variable-highlight" style="background-color: var(--semi-color-primary-light-default); border-radius: 4px; padding: 2px 4px;">${match}</span>`;
  });
}