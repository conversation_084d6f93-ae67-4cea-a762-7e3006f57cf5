import FileTextIcon from "@/components/icon/FileTextIcon";
import InputIcon from "@/components/icon/InputIcon";
import Icon, { IconPlus } from "@douyinfe/semi-icons";
import { Input, Button, Dropdown } from "@douyinfe/semi-ui";

interface Props {
  keyword: string;
  onSearch: () => void;
  onChange: (v: any) => void;
  onReset: () => void;
  onUpload: () => void;
}

export default function SearchBar({
  keyword,
  onSearch,
  onReset,
  onChange,
  onUpload,
}: Props) {
  return (
    <div className=" items-center flex justify-between">
      <div className="flex gap-2">
        <Input
          showClear
          placeholder="文档名称"
          style={{ width: "250px" }}
          value={keyword}
          onChange={onChange}
          onClear={onReset}
        />
        <Button theme="solid" type="primary" onClick={onSearch}>
          查询
        </Button>
      </div>
      <Dropdown
        className="min-w-[130px]"
        render={
          <Dropdown.Menu>
            <Dropdown.Item onClick={onUpload} icon={<Icon svg={<FileTextIcon />} />}>文本文件</Dropdown.Item>
            {/* <Dropdown.Item>同步飞书</Dropdown.Item> */}
            {/* <Dropdown.Item>网页抓取</Dropdown.Item> */}
            <Dropdown.Item icon={<Icon svg={<InputIcon />} />}>文本容器</Dropdown.Item>
          </Dropdown.Menu>
        }
      >
        <Button theme="solid" type="primary" icon={<IconPlus />}>
          新增文档
        </Button>
      </Dropdown >
    </div >
  );
}
