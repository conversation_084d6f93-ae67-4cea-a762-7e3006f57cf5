import React from 'react';
import { Table, Button } from '@douyinfe/semi-ui';

interface VariableData {
    key: string;
    label: string;
    required: boolean;
    maxLength?: number;
    description?: string;
    options?: Array<{ name: string }>;
    type: "text" | "number" | "options" | "custom";
}

interface VariablesTableProps {
    variables: VariableData[];
    onEdit: (variable: VariableData) => void;
    onDelete: (key: string) => void;
}

export const VariablesTable: React.FC<VariablesTableProps> = ({
    variables,
    onEdit,
    onDelete
}) => {
    const columns = [
        { title: '变量', dataIndex: 'key' },
        { title: '默认值', dataIndex: 'defaultValue' },
        { title: '描述', dataIndex: 'description' },
        {
            title: '操作',
            dataIndex: 'operate',
            render: (_: any, row: any) => (
                <div className="flex flex-row gap-1 items-center">
                    <Button
                        theme="borderless"
                        size="small"
                        type="tertiary"
                        icon="edit"
                        onClick={() => onEdit(row)}
                    />
                    <Button
                        theme="borderless"
                        size="small"
                        type="tertiary"
                        icon="deleteStroked"
                        onClick={() => onDelete(row.key)}
                    />
                </div>
            )
        }
    ];

    return (
        <Table
            size="small"
            dataSource={variables}
            columns={columns}
            rowKey="key"
            pagination={false}
        />
    );
};