import {
  IconArrowLeft,
  IconChevronDown,
  IconChevronUp,
  IconDeleteStroked,
  IconEdit,
  IconFolder,
  IconForward,
  IconHistory,
  IconInfoCircle,
  IconPlus,
  IconSetting,
  IconWrench,
} from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Collapsible,
  Divider,
  Form,
  Icon,
  Skeleton,
  Spin,
  Table,
  Tag,
  Timeline,
  Toast,
  Tooltip,
  Typography,
  Modal,
  IllustrationNoResult,
  Empty,
  Dropdown,
} from "@douyinfe/semi-ui";
import { PromptEditorField } from "@/components/MdEditor/PromptEditor";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from "react";
import classNames from "classnames";
import "./style/index.scss";
import { useParams, useRouter } from "@tanstack/react-router";
import AiGenIcon from "@/components/icon/AiGenIcon";
import { getAgent, updateAgent } from "@/api/platform/agent";
import { getModelProviderOptions } from "@/api/platform/model";
import PromptModel from "./components/PromptModel";
import ModelSetting from "./components/ModelSetting";
import VarModel from "./components/VarModel";
import ZhilingIcon from "@/components/icon/ZhilingIcon";
import ZhishiIcon from "@/components/icon/ZhishiIcon";
import JinengIcon from "@/components/icon/JinengIcon";
import DuihuaIcon from "@/components/icon/DuihuaIcon";
import ImportIcon from "@/components/icon/ImportIcon";
import AgentModalIcon from "@/components/icon/AgentModalIcon";
import AutoIcon from "@/components/icon/AutoIcon";
import PreviewPanel from "./components/preview-panel";
import Header from "./components/layout/header";
import Loading from "./components/layout/loading";
import { debounce } from "lodash-es";
import { useGlobalStore } from "@/store";

interface VariableData {
  key: string;
  default?: string;
  description?: string;
}
// 添加常量定义
const BASICS = "basics";
const CONFIG = "config";
const MODEL_CONFIG = "model";
const VARIABLES = "variables";
export default function AppDetails() {
  const { id } = useParams({ strict: false });
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [detailLoading, setDetailLoading] = useState(true);
  const [modalSettingOpen, setModalSettingOpen] = useState(false);
  const [currentTab, setCurrentTab] = useState<number>(0);
  const router = useRouter();
  const [welcomeText, setWelcomeText] = useState("");
  const [enabledWelcome, setEnabledWelcome] = useState(false);
  const [openVarModel, setOpenVarModel] = useState(false);
  const [modelList, setModelList] = useState<any>([]);
  const formApiRef = React.useRef<any>(null);
  const [variables, setVariables] = useState<VariableData[]>([]);
  const [welcomeOpen, setWelcomeOpen] = useState(false);

  const [editingVariable, setEditingVariable] = useState<VariableData | null>(
    null
  );
  const [zhishiOpen, setZhishiOpen] = useState(false);
  const [toolOpen, setToolOpen] = useState(false);
  const [varOpen, setVarOpen] = useState(false);
  const [selectModelId, setSelectModelId] = useState<any>("");
  const { userInfo } = useGlobalStore.getState();

  // 添加一个状态来保存当前光标位置
  const [cursorPosition, setCursorPosition] = useState<number | null>(null);

  useEffect(() => {
    const init = async () => {
      if (id) {
        await getModelList();
        await getDetails(id);
      }
    };

    init();
  }, [id]);

  // 在组件顶部添加状态来跟踪修改的字段
  const [modifiedSections, setModifiedSections] = useState<Set<string>>(
    new Set()
  );

  // 添加一个标志来跟踪表单是否正在初始化
  const [isFormInitializing, setIsFormInitializing] = useState(true);

  // 在组件顶部添加自动保存状态
  const [autoSaveStatus, setAutoSaveStatus] = useState<
    "idle" | "saving" | "saved" | "error"
  >("idle");

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const [detailData, setDetailData] = useState<any>([]);

  const getModelList = async () => {
    setLoading(true);
    return await getModelProviderOptions("0")
      .then(({ data }) => {
        setModelList(data);
        return Promise.resolve();
      })
      .catch((error) => {
        return Promise.reject();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getDetails = (id: number) => {
    setDetailLoading(true);
    getAgent(id)
      .then(({ data }) => {
        setDetailData(data);
        const enabledWelcome =
          data.config.enabledWelcome === "Y" ? true : false;

        console.log("Setting form values:", {
          "config.prompt": data.config.prompt,
          "config.welcomeText": data.config.welcomeText,
          "config.enabledWelcome": enabledWelcome,
          "model.modelId": data.modelConfig?.modelId,
          "model.modelName": data.modelConfig?.modelName,
          variables: data.variables,
        });

        // 标记表单正在初始化
        setIsFormInitializing(true);

        // 使用setTimeout确保表单已经初始化
        setTimeout(() => {
          formApiRef?.current.setValues({
            "config.prompt": data.config.prompt,
            "config.welcomeText": data.config.welcomeText,
            "config.enabledWelcome": enabledWelcome,
            "model.modelId": data.modelConfig?.modelId,
            "model.modelName": data.modelConfig?.modelName,
            variables: data.variables,
          });

          // 验证值是否设置成功
          console.log(
            "Form values after set:",
            formApiRef?.current.getValues()
          );

          // 表单初始化完成
          setTimeout(() => {
            setIsFormInitializing(false);
          }, 100); // 给一点时间让onValueChange处理完所有初始值
        }, 0);

        if (data.variables && data.variables.length > 0) {
          setVarOpen(true);
        }
        setVariables(data?.variables || []);
        setEnabledWelcome(enabledWelcome);
        if (enabledWelcome) {
          setWelcomeOpen(true);
          setWelcomeText(data.config.welcomeText);
        }
      })
      .finally(() => {
        setDetailLoading(false);
      });
  };

  const tabs = useMemo(() => {
    return [
      {
        value: 0,
        label: "智能体编排",
      },
      {
        value: 1,
        label: "对话日志",
      },
      {
        value: 2,
        label: "接入渠道",
      },
    ];
  }, []);

  const handleGoback = () => {
    router.history.back();
  };
  const handlePromptModelOk = (prompt: string) => {
    formApiRef.current?.setValue("config.prompt", prompt);
    setOpen(false);
  };

  const handlePromptModelCancel = () => {
    setOpen(false);
  };
  const handleWelcomeTextChange = (value: string) => {
    setWelcomeText(value);
  };

  // 在组件中添加一个调试函数
  const debugFormValues = () => {
    const values = formApiRef.current?.getValues();
    console.log("Current form values:", values);
    console.log("Current variables:", variables);
  };

  const handleSave = async () => {
    try {
      // 直接从表单获取值
      const values = formApiRef.current?.getValues();
      console.log("Form values:", values);

      if (!values) {
        Toast.error({ content: "无法获取表单数据" });
        return;
      }

      // 构建提交数据
      const submitData: any = {
        botId: id,
      };

      // 只更新被修改过的部分
      const sectionsToUpdate = Array.from(modifiedSections);

      // 如果没有检测到修改，提示用户
      if (sectionsToUpdate.length === 0) {
        Toast.info({ content: "没有检测到修改，无需保存" });
        return;
      }
      if (sectionsToUpdate.includes(CONFIG)) {
        submitData[CONFIG] = {
          prompt: values.config["prompt"] || "",
          welcomeText: values.config["welcomeText"] || "",
          enabledWelcome: values.config["enabledWelcome"] ? "Y" : "N",
        };
      }

      if (sectionsToUpdate.includes(MODEL_CONFIG)) {
        submitData[MODEL_CONFIG] = {
          modelId: values.model["modelId"] || "",
          modelName: values.model["modelName"] || "",
        };
      }

      // 变量的修改是通过UI操作直接更新state的，所以需要特殊处理
      if (sectionsToUpdate.includes(VARIABLES)) {
        submitData[VARIABLES] = variables;
      }

      updateAgent(submitData)
        .then((msg) => {
          Toast.success({ content: "保存成功" });
          setModifiedSections(new Set());
        })
        .catch((error) => { });
    } catch (error) {
      console.error("Save error:", error);
      Toast.error({ content: "保存失败，请重试" });
    }
  };

  const handleModelConfig = () => {
    if (
      formApiRef.current.getValue("model.modelId") === "" ||
      formApiRef.current.getValue("model.modelId") === undefined
    ) {
      Toast.error({ content: "请选择模型" });
      return;
    }
    setModalSettingOpen(true);
  };

  const handleToggleVar = () => {
    const newVarOpen = !varOpen;
    setVarOpen(newVarOpen);
  };

  const columns = useMemo(
    () => [
      {
        title: "变量",
        dataIndex: "key",
        key: "key",
        render: (text: string) => text,
      },
      {
        title: "默认值",
        dataIndex: "defaultValue",
        key: "defaultValue",
      },
      {
        title: "描述",
        dataIndex: "description",
        key: "description",
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button
                theme="borderless"
                size="small"
                type="tertiary"
                icon={<IconEdit />}
                onClick={() => handleEditVariable(row)}
              ></Button>
              <Button
                theme="borderless"
                size="small"
                type="tertiary"
                icon={<IconDeleteStroked />}
                onClick={() => handleDeleteVariable(row.key)}
              ></Button>
            </div>
          );
        },
      },
    ],
    []
  );

  const handleEditVariable = (variable: VariableData) => {
    setEditingVariable(variable);
    setOpenVarModel(true);
  };
  // 首先定义autoSaveRef，但不要在这里引用triggerAutoSave
  const autoSaveRef = React.useRef(
    debounce(async (sectionsToUpdate: string[]) => {
      try {
        if (sectionsToUpdate.length === 0) return;

        setAutoSaveStatus("saving");
        console.log("Auto saving sections:", sectionsToUpdate);
        console.log("Current variables state:", variables);

        // 添加更多调试信息
        console.log("Variables type:", typeof variables);
        console.log("Variables is array:", Array.isArray(variables));
        console.log("Variables length:", variables.length);

        // 获取表单值
        const values = formApiRef.current?.getValues();
        if (!values) {
          setAutoSaveStatus("error");
          return;
        }

        // 构建提交数据
        const submitData: any = {
          botId: id,
        };

        // 根据修改的部分添加相应数据
        if (sectionsToUpdate.includes(CONFIG)) {
          submitData[CONFIG] = {
            prompt: values.config["prompt"] || "",
            welcomeText: values.config["welcomeText"] || "",
            enabledWelcome: values.config["enabledWelcome"] ? "Y" : "N",
          };
        }

        if (sectionsToUpdate.includes(MODEL_CONFIG)) {
          submitData[MODEL_CONFIG] = {
            modelId: values.model["modelId"] || "",
            modelName: values.model["modelName"] || "",
          };
        }

        if (sectionsToUpdate.includes(VARIABLES)) {
          // 确保variables是一个非空数组
          if (Array.isArray(variables) && variables.length > 0) {
            // 创建一个新的变量数组，确保每个变量对象都有正确的属性
            const formattedVariables = variables.map(v => ({
              key: v.key,
              defaultValue: v.defaultValue || v.default || "",
              description: v.description || ""
            }));
            submitData[VARIABLES] = formattedVariables;
          } else {
            console.warn("Variables is empty or not an array:", variables);
            // 如果variables为空，可以选择不包含这个字段，或者发送一个空数组
            submitData[VARIABLES] = [];
          }
          console.log("Submitting variables in request:", submitData[VARIABLES]);
        }

        // 发送请求前记录完整的提交数据
        console.log("Full submit data:", JSON.stringify(submitData));

        // 发送请求
        updateAgent(submitData)
          .then((msg) => {
            // 保存成功
            setAutoSaveStatus("saved");
            console.log("Auto save success:", msg);

            // 清除修改记录
            setModifiedSections(new Set());

            // 2秒后重置状态为idle
            setTimeout(() => {
              setAutoSaveStatus("idle");
            }, 2000);
          })
          .catch((error) => {
            console.error("Auto save error:", error);
            setAutoSaveStatus("error");
          });
      } catch (error) {
        console.error("Auto save error:", error);
        setAutoSaveStatus("error");
      }
    }, 3000) // 1.5秒的防抖时间
  );

  // 然后定义triggerAutoSave
  const triggerAutoSave = useCallback((sectionsToUpdate: string[]) => {
    console.log("Triggering auto save for sections:", sectionsToUpdate);
    autoSaveRef.current(sectionsToUpdate);
  }, []);

  // 现在定义其他依赖于triggerAutoSave的函数
  const handleDeleteVariable = useCallback(
    (key: string) => {
      Modal.confirm({
        title: "确认删除",
        content: `确定要删除变量 "${key}" 吗？`,
        onOk: () => {
          setVariables((prevVars) => {
            const newVars = prevVars.filter((v) => v.key !== key);

            // 在状态更新后触发自动保存
            setTimeout(() => {
              setModifiedSections((prev) => {
                const newSections = new Set([...prev, VARIABLES]);
                triggerAutoSave(Array.from(newSections));
                return newSections;
              });
            }, 0);

            return newVars;
          });

          Toast.success({ content: "变量已删除" });
        }
      });
    },
    [triggerAutoSave]
  );

  // 添加一个函数来检查变量key是否重复
  const isDuplicateKey = (key: string) => {
    return variables.some(
      (variable) => variable.key.toLowerCase() === key.toLowerCase()
    );
  };

  // 在变量模态框中使用这个函数
  // 假设你有一个变量模态框组件，可以在其中添加这个检查
  // 例如，在表单提交前检查：
  // const handleAddVariable = (values: any) => {
  //   // 如果是编辑模式，需要排除当前编辑的变量
  //   const isEdit = !!editingVariable;
  //   const keyToCheck = values.key.trim();

  //   // 检查是否重复（编辑模式下，如果key没变，不算重复）
  //   if (!isEdit && isDuplicateKey(keyToCheck)) {
  //     Toast.error({ content: `变量 "${keyToCheck}" 已存在，请使用不同的名称` });
  //     return;
  //   }

  //   if (isEdit && editingVariable.key !== keyToCheck && isDuplicateKey(keyToCheck)) {
  //     Toast.error({ content: `变量 "${keyToCheck}" 已存在，请使用不同的名称` });
  //     return;
  //   }

  //   // 继续处理变量添加/编辑逻辑
  //   if (isEdit) {
  //     // 更新变量
  //     setVariables(prevVars =>
  //       prevVars.map(v =>
  //         v.key === editingVariable.key ? { ...values } : v
  //       )
  //     );
  //   } else {
  //     // 添加新变量
  //     setVariables(prevVars => [...prevVars, values]);
  //   }

  //   // 关闭模态框
  //   setOpenVarModel(false);
  //   setEditingVariable(null);

  //   // 触发自动保存
  //   setModifiedSections(prev => {
  //     const newSections = new Set([...prev, VARIABLES]);
  //     triggerAutoSave(Array.from(newSections));
  //     return newSections;
  //   });

  //   Toast.success({ content: isEdit ? "变量已更新" : "变量已添加" });
  // };

  // 处理VarModel提交
  const handleVarModelSubmit = useCallback(
    async (variableDataList: any[], continueAdding: boolean) => {
      console.log("Submitting variables from modal:", variableDataList);

      // 确保variableDataList是数组
      if (!Array.isArray(variableDataList)) {
        variableDataList = [variableDataList]; // 兼容单个变量的情况
      }

      // 过滤掉无效的变量（没有key的变量）
      const validVariables = variableDataList.filter(v => v && v.key && v.key.trim() !== '');

      if (validVariables.length === 0) {
        Toast.error({ content: "请至少添加一个有效变量" });
        return;
      }

      console.log("Valid variables to set:", validVariables);

      // 直接用新的变量列表替换旧的变量列表
      setVariables(validVariables);

      // 直接调用API保存变量，绕过自动保存机制
      try {
        setAutoSaveStatus("saving");

        // 格式化变量数据
        const formattedVariables = validVariables.map(v => ({
          key: v.key,
          defaultValue: v.defaultValue || v.default || "",
          description: v.description || ""
        }));

        // 构建提交数据
        const submitData = {
          botId: id,
          variables: formattedVariables
        };

        console.log("Directly submitting variables:", JSON.stringify(submitData));

        // 直接调用API
        await updateAgent(submitData);

        setAutoSaveStatus("saved");
        Toast.success({ content: "变量已保存" });

        // 2秒后重置状态
        setTimeout(() => {
          setAutoSaveStatus("idle");
        }, 2000);
      } catch (error) {
        console.error("Failed to save variables:", error);
        setAutoSaveStatus("error");
        Toast.error({ content: "保存变量失败" });
      }

      // 只有在不继续添加的情况下才关闭模态框
      if (!continueAdding) {
        setEditingVariable(null);
        setOpenVarModel(false);
      } else {
        // 如果继续添加，清除编辑状态但保持模态框打开
        setEditingVariable(null);

        // 重置表单为一个空行，方便继续添加
        setTimeout(() => {
          formApiRef.current?.setValue("variables", [
            {
              key: "",
              default: "",
              description: ""
            }
          ]);
        }, 100);
      }
    },
    [id, setAutoSaveStatus]
  );

  // 更新变量提取函数以支持{{变量名}}格式
  const extractVariablesFromText = (text: string): string[] => {
    if (!text) return [];

    // 使用正则表达式匹配 {{xxx}} 格式的变量
    const regex = /\{\{([^}]+)\}\}/g;
    let match;
    const variableNames = [];

    while ((match = regex.exec(text)) !== null) {
      if (match[1] && match[1].trim()) {
        variableNames.push(match[1].trim());
      }
    }

    // 去重
    return [...new Set(variableNames)];
  };

  // 在JSX部分使用标准的Form.TextArea
  <Form.TextArea
    field="config.prompt"
    maxCount={29024}
    rows={16}
    noLabel
    fieldStyle={{
      paddingBottom: "0",
    }}
    onBlur={(e) => {
      // 变量提取逻辑
      const promptText = e.target.value;
      const extractedVars = extractVariablesFromText(promptText);

      // 如果需要，可以在这里处理提取的变量
      if (extractedVars.length > 0) {
        console.log("提取到的变量:", extractedVars);
      }
    }}
  />

  {/* 添加一个简单的提示 */ }
  <div className="text-xs text-gray-500 mt-1 mb-4">
    <IconInfoCircle size="small" className="mr-1" />
    在提示词中使用 <code className="bg-gray-100 px-1 rounded">{"{{变量名}}"}</code> 格式插入变量，或点击上方"插入变量"按钮。
  </div>



  // 插入变量的函数
  const insertVariable = (variableKey: string) => {
    // 获取当前提示词文本
    const currentText = formApiRef.current?.getValue("config.prompt") || "";

    // 在文本末尾插入变量
    const newText = currentText + `{{${variableKey}}}`;

    // 更新表单值
    formApiRef.current.setValue("config.prompt", newText);
  };

  return (
    <div className="w-full h-full bg-gray-100 flex flex-col gap-2">
      <Header
        detailLoading={detailLoading}
        detailData={detailData}
        currentTab={currentTab}
        tabs={tabs}
        handleGoback={handleGoback}
        handleSave={handleSave}
        autoSaveStatus={autoSaveStatus}
      />
      <Loading detailLoading={detailLoading} />
      {!detailLoading && (
        <Form
          allowEmpty
          className="flex-1 overflow-hidden"
          getFormApi={getFormApi}
          onValueChange={(values, changedValues) => {
            console.log("Form values changed:", values);
            console.log("Changed values:", changedValues);

            // 如果表单正在初始化，不跟踪修改
            if (isFormInitializing) {
              console.log("Form is initializing, ignoring changes");
              return;
            }

            // 确定哪个部分被修改了
            const newModifiedSections = new Set(modifiedSections);

            // 检查变更的字段名称
            Object.keys(changedValues).forEach((key) => {
              if (key.startsWith("config.")) {
                newModifiedSections.add(CONFIG);
              } else if (key.startsWith("model.")) {
                newModifiedSections.add(MODEL_CONFIG);
              }
            });

            console.log("Modified sections:", Array.from(newModifiedSections));
            setModifiedSections(newModifiedSections);

            // 触发自动保存
            triggerAutoSave(Array.from(newModifiedSections));
          }}
        >
          <div className="flex flex-row gap-2 box-border overflow-hidden flex-1 w-full h-full  pb-2 px-2">
            <div className="flex flex-col bg-semi-color-white w-[50%] gap-2 h-full px-4 rounded-md py-4">
              <div>
                <div className="font-semibold text-lg">编排配置</div>
              </div>
              <div className="flex-1 overflow-auto overflow-x-hidden details-collapse">
                <div className="flex items-center justify-between pr-1">
                  <div className=" gap-2 pl-1 text-[14px] font-semibold flex items-center">
                    <div className="w-5 h-5 rounded-full flex items-center justify-center text-semi-color-primary bg-semi-color-primary-light-hover">
                      <Icon svg={<AgentModalIcon />} size="small" />
                    </div>
                    <span>模型配置</span>
                  </div>
                  <div>
                    <div className="items-center flex gap-2 flex-wrap ">
                      <Form.Select
                        field="model.modelId"
                        showClear
                        noLabel
                        onChange={(value: any) => {
                          setSelectModelId(value);
                        }}
                        fieldStyle={{ padding: 0 }}
                        placeholder="请选择模型"
                        searchPosition="dropdown"
                        className="min-w-[280px]"
                      >
                        {modelList.map((item: any) => {
                          return (
                            <Form.Select.OptGroup
                              key={item.value}
                              label={item.name}
                            >
                              {item?.children.map((child: any) => {
                                return (
                                  <Form.Select.Option
                                    key={child.value}
                                    showTick={true}
                                    value={child.value}
                                  >
                                    <div className="flex flex-row gap-2 items-center">
                                      <Avatar
                                        size="extra-small"
                                        src={child.extend2}
                                      />
                                      <div>{child.label}</div>
                                    </div>
                                  </Form.Select.Option>
                                );
                              })}
                            </Form.Select.OptGroup>
                          );
                        })}
                      </Form.Select>
                      <Button
                        type="tertiary"
                        onClick={() => handleModelConfig()}
                        icon={<Icon size="extra-large" svg={<IconSetting />} />}
                      ></Button>
                    </div>
                  </div>
                </div>
                <Timeline>
                  <Timeline.Item
                    className="w-[98%]"
                    dot={
                      <div className="w-5 h-5 rounded-full flex items-center justify-center text-semi-color-primary bg-semi-color-primary-light-hover">
                        <Icon svg={<ZhilingIcon />} size="small" />
                      </div>
                    }
                  >
                    <div>
                    </div>
                    <div className="text-sm font-semibold">指令</div>
                    <div className="flex justify-between items-center mt-2">
                      <div className="text-semi-color-text-1">提示词</div>
                      <div className="flex gap-2 items-center">
                        {/* 添加变量插入下拉菜单 */}
                        <Tooltip content="插入变量">
                          <Dropdown
                            trigger="click"
                            position="bottomRight"
                            className="min-w-[160px]"
                            render={
                              <Dropdown.Menu>
                                {variables.length > 0 ? (
                                  <>
                                    {variables.map(variable => {
                                      // 安全地获取当前提示词文本
                                      const currentText = formApiRef.current?.getValue?.("config.prompt") || "";
                                      // 检查变量是否已经在提示词中使用过
                                      const isUsed = currentText.includes(`{{${variable.key}}}`);

                                      return (
                                        <Dropdown.Item
                                          key={variable.key}
                                          onClick={() => !isUsed && insertVariable(variable.key)}
                                          disabled={isUsed}
                                        >
                                          <Tooltip content={variable.description}>
                                            <div className="flex items-center justify-between w-full">
                                              <span>{variable.key}</span>
                                              {isUsed && <span className="text-gray-400 text-xs ml-2">(已使用)</span>}
                                            </div>
                                          </Tooltip>
                                        </Dropdown.Item>
                                      );
                                    })}
                                  </>
                                ) : (
                                  <Dropdown.Item disabled>暂无变量</Dropdown.Item>
                                )}
                                <Dropdown.Divider />
                                <Dropdown.Item onClick={() => setOpenVarModel(true)}>
                                  <IconPlus size="small" /> 添加新变量
                                </Dropdown.Item>
                              </Dropdown.Menu>
                            }
                          >
                            <Button
                              type="tertiary"
                              size="small"
                              theme="light"
                              icon={<IconPlus />}
                            >
                              插入变量
                            </Button>
                          </Dropdown>
                        </Tooltip>
                        <Tooltip content="AI优化">
                          <Button
                            type="primary"
                            size="small"
                            theme="borderless"
                            icon={<Icon svg={<AiGenIcon />} />}
                            onClick={() => {
                              setOpen(true);
                            }}
                          />
                        </Tooltip>
                        <Divider layout="vertical" />
                        <Tooltip content="导入提示词模板">
                          <Button
                            type="primary"
                            size="small"
                            theme="borderless"
                            icon={<Icon svg={<ImportIcon />} />}
                            onClick={() => {
                              setOpen(true);
                            }}
                          />
                        </Tooltip>
                      </div>
                    </div>
                    <PromptEditorField
                      field="config.prompt"
                      noLabel
                      placeholder="请输入您的提示词..."
                      minHeight={400}
                      maxHeight={600}
                      onChange={(newValue: string) => {
                        // 保留原有的变量提取逻辑
                        const extractedVars = extractVariablesFromText(newValue);
                        if (extractedVars.length > 0) {
                          console.log("提取到的变量:", extractedVars);
                        }
                      }}
                      fieldStyle={{
                        paddingBottom: "0",
                      }}
                    />

                    {/* 可以添加一个简单的提示，告诉用户如何使用变量 */}
                    <div className="text-xs text-gray-500 mt-1 mb-4">
                      <IconInfoCircle size="small" className="mr-1" />
                      在提示词中使用 <code className="bg-gray-200 px-1 rounded">变量名</code> 格式插入变量，或点击上方"插入变量"按钮。
                    </div>
                  </Timeline.Item>
                  <Timeline.Item
                    className="w-[98%]"
                    dot={
                      <div className="w-5 h-5 rounded-full flex items-center justify-center text-semi-color-primary bg-semi-color-primary-light-hover">
                        <Icon svg={<ZhishiIcon />} size="small" />
                      </div>
                    }
                  >
                    <div className="text-sm font-semibold">知识</div>

                    <div
                      className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between my-1"
                      onClick={() => {
                        setZhishiOpen(!zhishiOpen);
                      }}
                    >
                      <div className="text-semi-color-text-1">知识库</div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="small"
                          theme="borderless"
                          type="tertiary"
                          icon={<IconPlus />}
                        />
                        <Divider layout="vertical" />
                        <Button
                          size="small"
                          theme="borderless"
                          type="tertiary"
                          icon={
                            zhishiOpen ? <IconChevronUp /> : <IconChevronDown />
                          }
                          onClick={() => {
                            setZhishiOpen(!zhishiOpen);
                          }}
                        />
                      </div>
                    </div>
                    <Collapsible isOpen={zhishiOpen}>
                      <div className="flex items-center justify-between hover:bg-semi-color-fill-0 px-2 rounded-md">
                        <div className="flex items-center gap-2">
                          <IconFolder />
                          <div>测试知识库</div>
                        </div>
                        <div>
                          <Button
                            type="danger"
                            theme="borderless"
                            icon={<IconDeleteStroked />}
                          />
                        </div>
                      </div>
                    </Collapsible>
                    <div className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between mb-1">
                      <div className="text-semi-color-text-1">联网搜索</div>
                      <Form.Switch
                        size="small"
                        noLabel
                        field="link"
                        fieldStyle={{ paddingBottom: "0px" }}
                      />
                    </div>
                  </Timeline.Item>
                  <Timeline.Item
                    className="w-[98%]"
                    dot={
                      <div className="w-5 h-5 rounded-full flex items-center justify-center text-semi-color-primary bg-semi-color-primary-light-hover">
                        <Icon svg={<JinengIcon />} size="small" />
                      </div>
                    }
                  >
                    <div className="text-sm font-semibold mb-1">技能</div>
                    <div
                      className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between my-1"
                      onClick={() => {
                        setToolOpen(!toolOpen);
                      }}
                    >
                      <div className="text-semi-color-text-1">工具</div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="small"
                          theme="borderless"
                          type="tertiary"
                          icon={<IconPlus />}
                        />
                        <Divider layout="vertical" />
                        <Button
                          size="small"
                          theme="borderless"
                          type="tertiary"
                          icon={
                            toolOpen ? <IconChevronUp /> : <IconChevronDown />
                          }
                          onClick={() => {
                            setToolOpen(!toolOpen);
                          }}
                        />
                      </div>
                    </div>
                    <Collapsible isOpen={toolOpen}>
                      <div className="flex items-center justify-between hover:bg-semi-color-fill-0 px-2 rounded-md">
                        <div className="flex items-center gap-2">
                          <IconWrench />
                          <div>天气查询</div>
                        </div>
                        <div>
                          <Button
                            type="danger"
                            theme="borderless"
                            icon={<IconDeleteStroked />}
                          />
                        </div>
                      </div>
                    </Collapsible>
                  </Timeline.Item>
                  <Timeline.Item
                    className="w-[98%]"
                    dot={
                      <div className="w-5 h-5 rounded-full flex items-center justify-center text-semi-color-primary bg-semi-color-primary-light-hover">
                        <Icon svg={<DuihuaIcon />} size="small" />
                      </div>
                    }
                  >
                    <div className="text-sm font-semibold">对话体验</div>
                    <div
                      className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between my-1"
                      onClick={() => {
                        setWelcomeOpen(!welcomeOpen);
                      }}
                    >
                      <div className="text-semi-color-text-1">开场白</div>
                      <div
                        className="flex items-center gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      >
                        <Tooltip content="是否开启开场白">
                          <Form.Switch
                            noLabel
                            rules={[
                              { required: true, message: "请选择是否开场白" },
                            ]}
                            size="small"
                            className="my-0"
                            checkedText="开"
                            onChange={(checked: boolean, e) => {
                              e.stopPropagation();
                              setEnabledWelcome(checked);
                            }}
                            fieldStyle={{ padding: 0 }}
                            uncheckedText="关"
                            field="config.enabledWelcome"
                          />
                        </Tooltip>
                        <Divider layout="vertical" />
                        <Tooltip content="生成开场白">
                          <Button
                            type="tertiary"
                            size="small"
                            theme="borderless"
                            icon={<Icon svg={<AutoIcon />} />}
                            onClick={(e) => {
                              e.stopPropagation();
                              setOpen(true);
                            }}
                          ></Button>
                        </Tooltip>
                        <Divider layout="vertical" />
                        <Button
                          size="small"
                          theme="borderless"
                          type="tertiary"
                          icon={
                            welcomeOpen ? (
                              <IconChevronUp />
                            ) : (
                              <IconChevronDown />
                            )
                          }
                          onClick={() => {
                            setWelcomeOpen(!welcomeOpen);
                          }}
                        />
                      </div>
                    </div>
                    <Collapsible isOpen={welcomeOpen}>
                      <Form.TextArea
                        noLabel
                        fieldStyle={{ paddingBottom: "0px" }}
                        maxCount={300}
                        placeholder="请输入开场白"
                        rows={4}
                        field="config.welcomeText"
                      />
                    </Collapsible>
                  </Timeline.Item>
                </Timeline>
              </div>
            </div>
            <PreviewPanel
              botId={id}
              welcomeText={welcomeText}
              enabledWelcome={enabledWelcome}
            />
          </div>
        </Form>
      )}
      <PromptModel
        open={open}
        onCancel={handlePromptModelCancel}
        onOk={handlePromptModelOk}
      />
      <ModelSetting
        selectModelId={selectModelId}
        open={modalSettingOpen}
        onCancel={() => setModalSettingOpen(false)}
        detailData={detailData}
        onOk={() => setModalSettingOpen(false)}
      />
      <VarModel
        open={openVarModel}
        onCancel={() => {
          setOpenVarModel(false);
          setEditingVariable(null);
        }}
        onSubmit={handleVarModelSubmit}
        variable={editingVariable} // 确保这个值正确传递
        allVariables={variables}
      />
    </div>
  );
}
