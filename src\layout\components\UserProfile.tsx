import {
  Avatar,
  Button,
  Col,
  Form,
  Modal,
  Row,
  TextArea,
  Toast,
  Typography,
  Upload,
} from '@douyinfe/semi-ui';
import React, { useState } from 'react';
import './style.scss';
import { IconCamera, IconClose, IconKeyStroked, IconUserStroked } from '@douyinfe/semi-icons';

export default function UserProfile({ open, onCancel }: any) {
  const [url, setUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = () => {
    setIsSubmitting(true);
    // 模拟提交过程
    setTimeout(() => {
      setIsSubmitting(false);
      Toast.success('修改已提交');
    }, 1000);
  };

  return (
    <Modal
      width={'60%'}
      visible={open}
      onCancel={onCancel}
      centered
      // closable={false}
      className="semi-light-scrollbar profile-modal"
      bodyStyle={{
        // height: "570px",
        overflowY: 'hidden',
        margin: 0,
        position: 'relative',
        padding: 0,
      }}
      // title={'编辑个人资料'}
      header={<div>
        <div className="flex justify-between items-center px-4 py-4 border-b border-solid border-semi-color-border">
          <div className="text-lg font-semibold">编辑个人资料</div>
          <Button theme="borderless" type="tertiary" icon={<IconClose />} onClick={onCancel} />
        </div>
      </div>}
      footer={null}
      closeOnEsc={true}
    >
      <div className='w-full overflow-y-hidden flex flex-col h-[560px] '>
        <div className='flex flex-row flex-1 relative'>
          <div className="w-[200px] flex flex-col gap-1  py-1 px-2 border-r border-solid border-semi-color-fill-1"><div>
            <div className="h-[37px] text-semi-color-primary rounded-md  bg-semi-color-primary-light-hover  cursor-pointer select-none hover:bg-semi-color-fill-0  px-2 flex items-center gap-3">
              <IconUserStroked />
              <div>基本信息</div>
            </div>
            <div className="h-[37px] cursor-pointer select-none hover:bg-semi-color-fill-0 rounded-md px-2 flex items-center gap-3">
              <IconKeyStroked />
              <div>安全设置</div>
            </div>
          </div>

          </div>
          <div className='flex-1 overflow-y-hidden overflow-x-hidden h-[500px]'>
            <Form onSubmit={handleSubmit} className="px-4 flex-1 h-full overflow-y-auto">
              <Row gutter={[10, 10]} className='overflow-y-auto'>
                <Col span={24} className='items-center flex justify-center mt-1'>
                  {/* <Form.Label>头像</Form.Label> */}
                  <div className='w-full flex flex-col items-center justify-center'>
                    <Upload
                      className="avatar-upload"
                      accept={'image/*'}
                      showUploadList={false}
                      onError={() => Toast.error('上传失败')}
                    >
                      <Avatar
                        src={url}
                        size="extra-large"
                        style={{
                          boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
                          transition: 'box-shadow 0.2s',
                        }}
                        hoverMask={
                          <div
                            style={{
                              backgroundColor: 'var(--semi-color-overlay-bg)',
                              height: '100%',
                              width: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'var(--semi-color-white)',
                            }}
                          >
                            <IconCamera />
                          </div>
                        }
                      />
                    </Upload>
                    <div className="text-center text-sm text-semi-color-text-2  mt-2">点击修改头像</div>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="用户昵称"
                      field="nickname"
                      placeholder="请输入昵称"
                      extraText="这是您的账号。您只能每30天更改一次。"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="账号邮箱"
                      field="email"
                      placeholder="请输入有效的邮箱地址"
                      extraText="我们将通过此邮箱发送通知"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-sm w-full">
                    <Form.Input
                      label="手机号码"
                      field="phone"
                      placeholder="请输入手机号码"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.RadioGroup field="gender" label="用户性别">
                      <Form.Radio value="male">男</Form.Radio>
                      <Form.Radio value="female">女</Form.Radio>
                    </Form.RadioGroup>
                  </div>
                </Col>
                <Col span={24}>
                  <div className="max-w-md w-full">
                    <Form.TextArea
                      label="个人简介"
                      field="bio"
                      placeholder="介绍一下自己吧"
                      rows={4}
                    />
                  </div>
                </Col>
                {/* <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="用户昵称"
                      field="nickname"
                      placeholder="请输入昵称"
                      extraText="这是您的账号。您只能每30天更改一次。"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-sm w-full">
                    <Form.Input
                      label="手机号码"
                      field="phone"
                      placeholder="请输入手机号码"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="账号邮箱"
                      field="email"
                      placeholder="请输入有效的邮箱地址"
                      extraText="我们将通过此邮箱发送通知"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.RadioGroup field="gender" label="用户性别">
                      <Form.Radio value="male">男</Form.Radio>
                      <Form.Radio value="female">女</Form.Radio>
                    </Form.RadioGroup>
                  </div>
                </Col>

                <Col span={24}>
                  <div className="max-w-md w-full">
                    <Form.TextArea
                      label="个人简介"
                      field="bio"
                      placeholder="介绍一下自己吧"
                      rows={4}
                    />
                  </div>
                </Col> */}
              </Row>
            </Form>
          </div>
        </div>
        <div className='h-[50px] flex items-center justify-end border-t border-solid border-semi-color-border p-4'>
          <div className='flex gap-2'>
            <Button
              type="primary"
              size='large'
              theme='outline'
            >
              取消
            </Button>
            <Button
              type="primary"
              size='large'
              theme="solid"
              htmlType="submit"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              保存更改
            </Button>
          </div>
        </div>
      </div>
      {/* <div className="flex flex-row overflow-hidden w-full h-[620px]">
        <div className="w-[230px] h-full px-2 border-r border-solid border-semi-color-fill-1">
          <div>
            <div className="h-[37px] cursor-pointer select-none hover:bg-semi-color-fill-0 rounded-md px-2 flex items-center gap-3">
              <IconUserStroked />
              <div>基本信息</div>
            </div>
            <div className="h-[37px] cursor-pointer select-none hover:bg-semi-color-fill-0 rounded-md px-2 flex items-center gap-3">
              <IconKeyStroked />
              <div>安全设置</div>
            </div>
          </div>
        </div>
        <div className="flex-1 px-4 h-full overflow flex flex-col">
          <div className="py-4 flex justify-between items-center">
            <Typography.Text className="shrink-0 text-lg">基本信息</Typography.Text>
            <Button theme="borderless" type="tertiary" icon={<IconClose />} onClick={onCancel} />
          </div>
          <div className="mt-2 flex-1 overflow-hidden">
            <Form className="flex flex-col overflow-hidden h-full w-full" onSubmit={handleSubmit}>
              <Row className="px-2 flex-1 overflow-y-auto" gutter={[10, 10]}>
                <Col span={24}>
                  <div className="max-w-md w-full">
                    <Form.Label>头像</Form.Label>
                    <Upload
                      className="avatar-upload"
                      accept={'image/*'}
                      showUploadList={false}
                      onError={() => Toast.error('上传失败')}
                    >
                      <Avatar
                        src={url}
                        size="large"
                        style={{
                          borderRadius: '12px',
                          boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
                          transition: 'box-shadow 0.2s',
                        }}
                        hoverMask={
                          <div
                            style={{
                              backgroundColor: 'var(--semi-color-overlay-bg)',
                              height: '100%',
                              width: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'var(--semi-color-white)',
                            }}
                          >
                            <IconCamera />
                          </div>
                        }
                      />
                    </Upload>
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="用户昵称"
                      field="nickname"
                      placeholder="请输入昵称"
                      extraText="这是您的账号。您只能每30天更改一次。"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-sm w-full">
                    <Form.Input
                      label="手机号码"
                      field="phone"
                      placeholder="请输入手机号码"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.Input
                      label="账号邮箱"
                      field="email"
                      placeholder="请输入有效的邮箱地址"
                      extraText="我们将通过此邮箱发送通知"
                    />
                  </div>
                </Col>
                <Col span={12}>
                  <div className="max-w-md w-full">
                    <Form.RadioGroup field="gender" label="用户性别">
                      <Form.Radio value="male">男</Form.Radio>
                      <Form.Radio value="female">女</Form.Radio>
                    </Form.RadioGroup>
                  </div>
                </Col>

                <Col span={24}>
                  <div className="max-w-md w-full">
                    <Form.TextArea
                      label="个人简介"
                      field="bio"
                      placeholder="介绍一下自己吧"
                      rows={4}
                    />
                  </div>
                </Col>
              </Row>
            </Form>
          </div>
        </div>
      </div> */}
    </Modal>
  );
}