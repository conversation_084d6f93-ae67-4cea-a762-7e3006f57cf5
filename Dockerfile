FROM node:20-slim AS build


ENV NODE_OPTIONS="--max-old-space-size=4096"
# Set the working directory in the container
WORKDIR /app

# Copy package.json and lock file to the container
COPY package*.json ./

# Install pnpm globally
RUN npm install -g pnpm

# Optionally set registry for faster installation
# RUN pnpm config set registry https://registry.npmmirror.com

RUN pnpm config set registry https://registry.npmmirror.com

# Install dependencies using pnpm
RUN pnpm install

# Copy the rest of the application code
COPY . .

# Build the React application using pnpm
RUN pnpm run build

# Use a lightweight Nginx image for the final stage
FROM nginx:stable-alpine

# Copy Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Copy the built application from the previous stage
COPY --from=build /app/dist /usr/share/nginx/html

# Expose the desired port
EXPOSE 8081
