import { http } from "@/utils/axios";
export function login(params: any) {
  return http.request<API.Result>(
    {
      url: "/auth/v1/login",
      method: "POST",
      params,
    },
    {
      isTransformResponse: false,
      withToken: true,
    }
  );
}

export function logout() {
  return http.request<API.Result>(
    {
      url: "/auth/v1/logout",
      method: "POST",
    },
    {
      isTransformResponse: false,
      withToken: true,
    }
  );
}

