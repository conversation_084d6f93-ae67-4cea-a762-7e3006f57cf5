import { http } from "@/utils/axios";

const base_url = "/monitor/online";

export function getOnlineList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}
// 强制退出登录
export function forceLogout(tokenId: string) {
  return http.request<any>({
    url: `${base_url}/${tokenId}`,
    method: "DELETE",
  });
}


export function myOnelineList() {
  return http.request<Log.OnlineVO>({
    url: `${base_url}`,
    method: "GET",
  });
}
