import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  <PERSON><PERSON>se,
  DatePicker,
  Divider,
  Dropdown,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Pagination,
  Radio,
  RadioGroup,
  Row,
  Space,
  Spin,
  Table,
  TabPane,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, {
  createRef,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import { LocalForageService as storage } from "../../../utils/storage";
import { OpenAIAttribute } from "../../../interface/llm";
import { initialOpenaiAttribute } from "../../../utils/initial-state";
import { MessageUtil } from "../../../utils/message-util";
import {
  IconDelete,
  IconEdit,
  IconHelpCircle,
  IconMore,
  IconRefresh,
  IconRefresh2,
  IconSearch,
} from "@douyinfe/semi-icons";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { useBoolean } from "@/hooks";
import ModelForm from "./components/ModelForm";
import { useTable } from "@/hooks/useTables";
import { debounce } from "lodash-es";

import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import { getModelList } from "@/api/platform/model";
import useDictionary from "@/hooks/useDictionary";
import DictTag from "@/components/DictTag";
import { dictConvetToOpention } from "@/utils";
import { models } from "@/enums/modelEnum";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";

function ModelIndex() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  // const [loading, setLoading] = useState(false);
  // const initPageInfo = {
  //   page: 1,
  //   pageSize: 10,
  // };
  // const [page, setPage] = useState(initPageInfo.page);
  // const [pageSize, setPageSize] = useState(initPageInfo.pageSize);

  // const [total, setTotal] = useState(0);
  // const [dataSource, setData] = useState([]);
  const [search, setSearch] = useState("");
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_model_type",
    "sys_normal_disable",
    "ai_model_provider",
  ]);
  useEffect(() => {
    loadDictionary();
  }, []);
  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getModelList,
    columns: [
      {
        title: "模型版本",
        dataIndex: "modelVersion",
        render: (text, record, index) => {
          return (
            <div className="flex items-center gap-2">
              <Avatar
                size="extra-small"
                shape="square"
                src={record.logo}
              ></Avatar>
              <div>{text}</div>
            </div>
          );
        },
      },
      {
        title: "别名",
        dataIndex: "aliasName",
      },
      {
        title: "供应商",
        dataIndex: "provider",
        // filters: getFilter(dictionaryData.ai_model_provider),
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_provider"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_provider || []}
            />
          );
        },
      },
      {
        title: "模型类型",
        dataIndex: "type",
        // filters: getFilter(dictionaryData.ai_model_type),
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_type"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_type || []}
            />
          );
        },
      },
      // {
      //   title: "上下文长度",
      //   dataIndex: "tokens",
      //   align: "center",
      //   // sorter: (a: number, b: number) => (a.tokens - b.tokens > 0 ? 1 : -1),
      //   render: (text: number) => `${text} K`,
      // },
      {
        title: "描述",
        width: 250,
        ellipsis: { showTitle: true },
        dataIndex: "descrption",
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              type="badge"
              dictType="sys_normal_disable"
              dictValue={text}
              dictionaryData={dictionaryData.sys_normal_disable || []}
            />
          );
        },
      },
      {
        title: "更新日期",
        dataIndex: "updatedAt",
      },
      {
        title: "",
        dataIndex: "operate",
        render: () => {
          return (
            <Dropdown
              position="bottom"
              className="w-[100px]"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item>编 辑</Dropdown.Item>
                  <Dropdown.Item type="danger">删 除</Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                size="small"
                theme="borderless"
                type="primary"
                icon={<IconMore className="cursor-pointer" />}
              />
            </Dropdown>
          );
        },
      },
    ],
  });
  // const getFilter = (data: any) => {
  //   return data?.map((element: any) => {
  //     return {
  //       text: element.dictLabel,
  //       value: element.dictValue,
  //     };
  //   });
  // };
  // const columns = useMemo(() => {
  // return [
  //   {
  //     title: "模型版本",
  //     dataIndex: "modelVersion",
  //     render: (text, record, index) => {
  //       return (
  //         <div className="flex items-center gap-2">
  //           <Avatar
  //             size="extra-small"
  //             shape="square"
  //             src={record.logo}
  //           ></Avatar>
  //           <div>{text}</div>
  //         </div>
  //       );
  //     },
  //   },
  //   {
  //     title: "别名",
  //     dataIndex: "aliasName",
  //   },
  //   {
  //     title: "供应商",
  //     dataIndex: "provider",
  //     // filters: getFilter(dictionaryData.ai_model_provider),
  //     render: (text: any) => {
  //       return (
  //         <DictTag
  //           dictType="ai_model_provider"
  //           dictValue={text}
  //           dictionaryData={dictionaryData.ai_model_provider || []}
  //         />
  //       );
  //     },
  //   },
  //   {
  //     title: "模型类型",
  //     dataIndex: "type",
  //     // filters: getFilter(dictionaryData.ai_model_type),
  //     render: (text: any) => {
  //       return (
  //         <DictTag
  //           dictType="ai_model_type"
  //           dictValue={text}
  //           dictionaryData={dictionaryData.ai_model_type || []}
  //         />
  //       );
  //     },
  //   },
  //   // {
  //   //   title: "上下文长度",
  //   //   dataIndex: "tokens",
  //   //   align: "center",
  //   //   // sorter: (a: number, b: number) => (a.tokens - b.tokens > 0 ? 1 : -1),
  //   //   render: (text: number) => `${text} K`,
  //   // },
  //   {
  //     title: "描述",
  //     width: 250,
  //     ellipsis: { showTitle: true },
  //     dataIndex: "descrption",
  //   },
  //   {
  //     title: "状态",
  //     dataIndex: "status",
  //     render: (text: any) => {
  //       return (
  //         <DictTag
  //           type="badge"
  //           dictType="sys_normal_disable"
  //           dictValue={text}
  //           dictionaryData={dictionaryData.sys_normal_disable || []}
  //         />
  //       );
  //     },
  //   },
  //   {
  //     title: "更新日期",
  //     dataIndex: "updatedAt",
  //   },
  //   {
  //     title: "",
  //     dataIndex: "operate",
  //     render: () => {
  //       return (
  //         <Dropdown
  //           position="bottom"
  //           className="w-[100px]"
  //           render={
  //             <Dropdown.Menu>
  //               <Dropdown.Item>编 辑</Dropdown.Item>
  //               <Dropdown.Item type="danger">删 除</Dropdown.Item>
  //             </Dropdown.Menu>
  //           }
  //         >
  //           <Button
  //             size="small"
  //             theme="borderless"
  //             type="primary"
  //             icon={<IconMore className="cursor-pointer" />}
  //           />
  //         </Dropdown>
  //       );
  //     },
  //   },
  // ];
  // }, []);

  // const fetchData = (
  //   currentPage = initPageInfo.page,
  //   pageSize = initPageInfo.pageSize,
  //   formValues = {}
  // ) => {
  //   setLoading(true);
  //   setPage(currentPage);
  //   getModelList({
  //     pageNum: currentPage,
  //     pageSize,
  //     ...formValues,
  //   })
  //     .then((res) => {
  //       setLoading(false);
  //       // @ts-expect-error
  //       setData(res.rows);
  //       setTotal(res.total);
  //     })
  //     .catch(() => {
  //       setLoading(false);
  //     });
  // };
  // const handlePageChange = (page: number) => {
  //   fetchData(page);
  // };
  // const handlePageSizeChane = (pageSize: number) => {
  //   fetchData(page, pageSize);
  // };

  // useEffect(() => {
  //   fetchData();
  // }, []);
  // const handleSearch = (formValues: any) => {
  //   fetchData(initPageInfo.page, initPageInfo.pageSize, formValues);
  // };
  const handleReset = () => {
    // fetchData(initPageInfo.page, initPageInfo.pageSize);
    refresh();
  };
  // 防抖提交（300ms）
  const debouncedSubmit = useCallback(
    debounce((values: any) => {
      setSearchParams(values);
      refresh();
    }, 300),
    []
  );
  const handleCancel = () => {
    setFalse();
  };

  return (
    <div className=" w-full h-full py-2 px-2">
      <div className="bg-semi-color-white py-4 px-2 w-full h-full flex items-stretch gap-2 sm:overflow-auto flex-col  rounded-md ">
        <div className="font-semibold text-xl px-2 my-2">模型管理</div>
        <div className="flex  flex-row items-center justify-between gap-2 px-2 ">
          <Form<typeof searchParams>
            initValues={searchParams}
            autoScrollToError={{ behavior: "smooth", block: "center" }}
            showValidateIcon
            onValueChange={(values) => {
              debouncedSubmit(values);
            }}
            onReset={() => {
              resetSearchParams();
              refresh();
            }}
            onSubmit={(values) => {
              setSearchParams(values);
              refresh();
            }}
          >
            <Space wrap>
              <Form.Input
                noLabel
                style={{ width: 260 }}
                placeholder={"请输入模型名称"}
                field="aliasName"
              />
              <Form.Select
                filter
                noLabel
                optionList={dictConvetToOpention(
                  dictionaryData["ai_model_provider"]
                )}
                showClear
                style={{ width: 260 }}
                placeholder={"请选择模型供应商"}
                field="provider"
              />
              <Space>
                <Button theme="solid" type="primary" htmlType="submit">
                  提交
                </Button>
                <Button theme="outline" type="tertiary" htmlType="reset">
                  重置
                </Button>
              </Space>
            </Space>
          </Form>
          <Button theme="solid" type="primary" onClick={setTrue}>
            新增模型
          </Button>
        </div>
        <div className="flex-1 sm:overflow-hidden">
          <Table
            rowSelection={rowSelection as any}
            loading={loading}
            columns={columns}
            className="h-full"
            dataSource={dataSource}
            scroll={{ x: 962, y: "calc(100vh - 210px)" }}
            rowKey="id"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
            pagination={false}
          />
        </div>
        {pagination.total > 0 && (
          <div className="flex justify-end">
            <Pagination {...pagination}></Pagination>
          </div>
        )}
      </div>
      <ModelForm
        open={formOpen}
        onCancel={handleCancel}
        onRefresh={handleReset}
        providers={dictConvetToOpention(dictionaryData["ai_model_provider"])}
        types={dictConvetToOpention(dictionaryData["ai_model_type"])}
      />
    </div>
  );
}

export default ModelIndex;
