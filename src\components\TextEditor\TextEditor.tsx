import "@mdxeditor/editor/style.css";
import "./index.scss";

import { useEffect, useRef, type ForwardedRef } from "react";
import {
  MDXEditor,
  UndoRedo,
  BoldItalicUnderlineToggles,
  toolbarPlugin,
  MDXEditorMethods,
  MDXEditorProps,
  BlockTypeSelect,
  ChangeCodeMirrorLanguage,
  InsertTable,
  ListsToggle,
  headingsPlugin,
  listsPlugin,
  markdownShortcutPlugin,
  quotePlugin,
  thematicBreakPlugin,
  frontmatterPlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  AdmonitionDirectiveDescriptor,
  CodeBlockEditorDescriptor,
  useCodeBlockEditorContext,
  CodeMirrorEditor,
  diffSourcePlugin,
  directivesPlugin,
  tablePlugin,
} from "@mdxeditor/editor";
import { withField } from "@douyinfe/semi-ui";
import { debounce } from "lodash-es";
function TextEditor({ ...props }: MDXEditorProps & any) {
  let value = props.value || "";
  const ref = useRef<ForwardedRef<MDXEditorMethods>>();
  let { onChange, ...rest } = props;
  useEffect(() => {
    // @ts-expect-error
    ref?.current?.setMarkdown(value);
  }, [value]);
  const debouncedHandleOnChange = debounce((md: string) => {
    // console.log(md);
    onChange(md);
  }, 300);
  // const PlainTextCodeEditorDescriptor: CodeBlockEditorDescriptor = {
  //   // always use the editor, no matter the language or the meta of the code block
  //   match: (language, meta) => true,
  //   // You can have multiple editors with different priorities, so that there's a "catch-all" editor (with the lowest priority)
  //   priority: 0,
  //   // The Editor is a React component
  //   Editor: (props) => {
  //     const cb = useCodeBlockEditorContext();
  //     // stops the propagation so that the parent lexical editor does not handle certain events.
  //     return (
  //       <div onKeyDown={(e) => e.nativeEvent.stopImmediatePropagation()}>
  //         <textarea
  //           rows={3}
  //           cols={20}
  //           defaultValue={props.code}
  //           onChange={(e) => cb.setCode(e.target.value)}
  //         />
  //       </div>
  //       // <ChangeCodeMirrorLanguage></ChangeCodeMirrorLanguage>
  //     );
  //   },
  // };

  return (
    <MDXEditor
      {...rest}
      ref={ref}
      className="overflow-y-auto box-border"
      markdown={value}
      onChange={debouncedHandleOnChange}
      contentEditableClassName="prose"
      plugins={[
        directivesPlugin({
          directiveDescriptors: [AdmonitionDirectiveDescriptor],
        }),
        tablePlugin(),
        listsPlugin(),
        headingsPlugin(),
        quotePlugin(),
        thematicBreakPlugin(),
        codeBlockPlugin({
          codeBlockEditorDescriptors: [
            { priority: -10, match: (_) => true, Editor: CodeMirrorEditor },
          ],
        }),
        codeMirrorPlugin({
          codeBlockLanguages: {
            jsx: "JavaScript (react)",
            js: "JavaScript",
            css: "CSS",
            java: "Java",
            ts: "TypeScript",
            sql: "SQL",
            tsx: "TypeScript (react)",
          },
        }),
        frontmatterPlugin(),
        markdownShortcutPlugin(),
      ]}
    />
  );
}

const FormTextEditor = withField(TextEditor, {
  valueKey: "value",
  onKeyChangeFnName: "onChange",
  // valuePath: "target.value",
});

export { TextEditor, FormTextEditor };
