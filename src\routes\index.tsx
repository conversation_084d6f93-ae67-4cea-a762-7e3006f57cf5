import systemRoutes from "./system";
import platformRoutes from "./platform";
import { loginRoute } from "./auth";
import { homeRoute, profileRoute, readict } from "./home";
import { appDetailRoute, authNoLayoutRoute, authRoute, homeLayoutRoute, platformRoute } from "./base";
import { platformFlowRoute } from "./flow";
import { createRoute } from "@tanstack/react-router";

//authroutes

const authRoutes = authRoute.addChildren([
  readict,
  //   @ts-ignore
  ...systemRoutes.map(routeConfig => createRoute(routeConfig)),
]);

const authNoLayoutRoutes = authNoLayoutRoute.addChildren([
  appDetailRoute
])

const platforms = platformRoute.addChildren([
  ...platformRoutes.map(routeConfig => createRoute(routeConfig)),

])
const homeLayoutRoutes  = homeLayoutRoute.addChildren([
  homeRoute,
  profileRoute,
])
export const routes = [
  platformFlowRoute,
  homeLayoutRoutes,
  authNoLayoutRoutes,
  platforms,
  loginRoute,
  authRoutes
  // systemRoutes,
  // platformRoutes
]