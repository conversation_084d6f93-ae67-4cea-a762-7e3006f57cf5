import { addDictData, editDictData, getDictData } from "@/api/system/dict";
import { useBoolean } from "@/hooks";
import {
  Avatar,
  Button,
  Col,
  Form,
  Modal,
  Row,
  Spin,
  Toast,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState } from "react";
import classNames from "classnames";
import { IconCamera } from "@douyinfe/semi-icons";
import { getAccessToken } from "@/utils/auth";
import { ResultEnum } from "@/enums/httpEnum";
import { logos } from "../constants";
import { addAgent } from "@/api/platform/agent";

function AppModelForm({ onCancel, open, onRefresh, title, id }: any) {
  // logos[0].url
  const [logo, setLogo] = useState();
  const formApiRef = React.useRef(null);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const [spinning, { setTrue: openSpinning, setFalse: closeSpinning }] =
    useBoolean(false);
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const handleCancel = () => {
    onCancel();
    onRefresh();
  };
  const handleReset = () => {
    // @ts-expect-error
    formApiRef.current?.reset();
    onCancel();
  };
  const handleAddSubmit = async (values: any) => {
    try {
      debugger;
      const data = {
        ...values,
        tags: values.tags.join(","),
      };
      delete data.files;
      setLoading();
      const res = await addAgent(data);
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const handleEditSubmit = async (values: any) => {
    try {
      setLoading();
      // const res = await editDictData({
      //   ...values,
      //   dictCode: id,
      //   dictType,
      // });
      // Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const submitForm = async () => {
    try {
      debugger;
      // @ts-expect-error
      const values = await formApiRef.current?.validate();
      if (values.files.length < 0) {
        Toast.error("请上传图标");
      }

      id
        ? await handleEditSubmit(values)
        : await handleAddSubmit({
          ...values,
          logo: values.files[0].response.data.ossId,
        });
    } catch (errors) {
      console.error(errors);
    }
  };
  const getDictDataDetail = async () => {
    try {
      openSpinning();
      // setLoading();
      const { data } = await getDictData(id);
      // @ts-expect-error
      formApiRef.current?.setValues(data);
      closeSpinning();
    } catch (err) {
      closeSpinning();
      console.error(err);
    }
  };
  useEffect(() => {
    if (id) {
      getDictDataDetail();
    }
  }, [open, id]);

  return (
    <div>
      <Modal
        title={title}
        width={600}
        centered
        footer={
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button style={{ marginRight: 8 }} onClick={handleReset}>
              取消
            </Button>
            <Button theme="solid" loading={loading} onClick={submitForm}>
              提交
            </Button>
          </div>
        }
        visible={open}
        onCancel={handleReset}
        closeOnEsc={false}
      >
        <Spin tip="正在加载..." spinning={spinning}>
          <Form
            initValues={{
              name: "",
              description: "",
              tags: ["智能体"],
              logo: logos[0].url,
            }}
            className="w-full"
            getFormApi={getFormApi}
            wrapperCol={{ span: 24 }}
          >
            <Row gutter={[10, 10]}>
              <Col span={24}>
                <Form.Input
                  field="name"
                  rules={[{ required: true, message: "请填写智能体名称" }]}
                  label="智能体名称"
                  placeholder={"请输入智能体名称"}
                  required
                />
              </Col>
              <Col span={24}>
                <Form.TextArea
                  field="description"
                  rules={[{ required: true, message: "请填写智能体功能介绍" }]}
                  placeholder={"介绍智能体的主要功能，展示给用户查看！"}
                  label="智能体功能介绍"
                  required
                />
              </Col>
              <Col span={24}>
                <Form.TagInput
                  className="w-full"
                  placeholder="给智能体加上标签，方便用户搜索！"
                  rules={[{ required: true, message: "请输入标签" }]}
                  field="tags"
                  initValue={["智能体"]}
                  label="标签"
                  trigger="blur"
                />
              </Col>
              <Col span={24}>
                <Form.Upload
                  label="智能体图标"
                  fileName="file"
                  name="file"
                  field="files"
                  className="avatar-upload"
                  rules={[{ required: true, message: "请上传文件" }]}
                  maxSize={5 * 1024 * 1024}
                  limit={1}
                  action={
                    location.hash +
                    import.meta.env.VITE_GLOB_API_URL_PREFIX +
                    "/resource/oss/upload"
                  }
                  // onSuccess={onSuccess}
                  accept={"image/*"}
                  headers={{
                    clientid: import.meta.env.VITE_GLOB_CLIENT_ID,
                    Authorization: `Bearer ${getAccessToken()}`,
                  }}
                  afterUpload={({ response, file }) => {
                    console.log("上传响应:", response); // 调试日志
                    if (response.code == ResultEnum.SUCCESS) {
                      const { data } = response;
                      setLogo(data.url);
                      return {
                        autoRemove: false,
                        status: "success",
                        name: data.fileName,
                        url: data.url,
                        ...data,
                      };
                    }
                    return {};
                  }}
                  showUploadList={false}
                  onError={() => Toast.error("上传失败")}
                >
                  <Avatar
                    className="rounded-lg"
                    shape="square"
                    size="large"
                    src={logo}
                    hoverMask={
                      <div className="bg-semi-color-tertiary-light-hover w-full h-full flex items-center justify-center">
                        <IconCamera />
                      </div>
                    }
                  />
                </Form.Upload>
              </Col>
            </Row>
          </Form>
        </Spin>
      </Modal>
    </div>
  );
}
export default AppModelForm;
