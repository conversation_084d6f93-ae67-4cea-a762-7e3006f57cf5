import { Descriptions, Modal, Table } from "@douyinfe/semi-ui";
import React, { useMemo } from "react";
import { models } from "@/enums/modelEnum";
const ModelDetails: React.FC<any> = ({ open, onCancel, model }) => {
  const scroll = useMemo(() => ({ y: 400, x: "600px" }), []);
  const columns = [
    {
      title: "模型版本",
      dataIndex: "label",
    },
    {
      title: "模型类型",
      dataIndex: "type",
    },
  ];

  return (
    <Modal
      width={800}
      title="支持模型列表"
      visible={open}
      onOk={onCancel}
      onCancel={onCancel}
    >
      <Table
        columns={columns}
        scroll={scroll}
        dataSource={models[model?.key] as any[]}
        pagination={false}
      />
    </Modal>
  );
};
export default ModelDetails;
