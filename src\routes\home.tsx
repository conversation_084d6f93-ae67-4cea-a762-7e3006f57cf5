import { createRoute, Outlet, redirect } from "@tanstack/react-router";
import { authRoute, homeLayoutRoute, plateFormRoute, rootRoute } from "./base";
import HomeLayout from "@/layout/home";
import Home from "@/pages/home";
import Profile from "@/pages/profile";
//创建后台管理路由

const readict = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  loader: () => {
    redirect({
      to: "/home",
      throw: true,
    });
  },
  component: () => <Home />,
});
const homeRoute = createRoute({
  getParentRoute: () => homeLayoutRoute,
  path: "/home",
  component: () => <Home />,
});

const profileRoute = createRoute({
  getParentRoute: () => homeLayoutRoute,
  path: "/profile",
  component: () => <Profile />,
});

// const profileRoute = createRoute();
// const homeRoutes = homeRouteLayout.addChildren([homeRoute]);

export { homeRoute, profileRoute, readict };
