import { visit } from "unist-util-visit";
import { Node } from "unist";

/**
 * Remark plugin to transform <thinks> tags into custom elements
 */
export function RemarkThinksPlugin() {
  return (tree: Node) => {
    // 存储所有thinks内容
    let allThinksContent = "";
    let hasThinks = false;

    // 第一步：收集所有thinks标签内容
    visit(tree, "html", (node: any) => {
      const value = node.value || "";

      // 匹配<thinks>标签内容
      const thinksMatch = value.match(/<thinks>(.*?)<\/thinks>/s);
      if (thinksMatch) {
        allThinksContent += thinksMatch[1];
        hasThinks = true;
        // 将原始thinks标签替换为空，避免重复处理
        node.value = "";
      }
    });

    // 如果有thinks内容，添加特殊标记
    if (hasThinks && allThinksContent) {
      // 使用特殊标记包装thinks内容，以便在MessageContent组件中处理
      const encodedContent = encodeURIComponent(allThinksContent);
      visit(tree, "root", (node: any) => {
        if (node.children && node.children.length > 0) {
          node.children.unshift({
            type: "html",
            value: `{{THINKING_BLOCK:${encodedContent}}}`,
          });
        }
      });
    }
  };
}
