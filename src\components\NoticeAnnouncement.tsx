import React, { useState, useEffect } from 'react';
import { Icon, Typography } from '@douyinfe/semi-ui';
import { motion, AnimatePresence } from 'framer-motion';
import { Bell } from 'lucide-react';

interface NoticeItem {
  id: number;
  title: string;
  date: string;
}

interface NoticeAnnouncementProps {
  notices: NoticeItem[];
  interval?: number; // 轮播间隔，单位毫秒
  className?: string;
}

const NoticeAnnouncement: React.FC<NoticeAnnouncementProps> = ({
  notices,
  interval = 3000,
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { Text } = Typography;

  useEffect(() => {
    if (notices.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % notices.length);
    }, interval);

    return () => clearInterval(timer);
  }, [notices.length, interval]);

  if (!notices || notices.length === 0) {
    return <div className={className}>暂无通知</div>;
  }

  return (
    <div className={`relative h-[30px] overflow-hidden ${className} bg-white gap-2 rounded-2xl flex  flex-rowitems-center`}>

      <AnimatePresence mode="wait">
        <motion.div
          key={notices[currentIndex].id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className=" w-full flex items-center gap-1 px-2"
        >
          <Icon className='text-semi-color-primary' svg={<Bell size="1em" />} />
          <Text
            ellipsis={{ showTooltip: true }}
            style={{ width: '100%', fontSize: '12px', lineHeight: '20px', color: '#606266' }}
          >
            {notices[currentIndex].title}
          </Text>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default NoticeAnnouncement;