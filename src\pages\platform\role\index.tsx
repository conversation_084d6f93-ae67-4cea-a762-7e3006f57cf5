import useBoolean from "@/hooks/useBoolean";
import { Ava<PERSON>, Button, Card, Input, List, Space } from "@douyinfe/semi-ui";
import React from "react";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { IconSearch } from "@douyinfe/semi-icons";

function Role() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);

  return (
    <div className="h-full relative flex flex-col">
      <div className="text-nowrap font-extrabold pl-4 pt-2 text-lg mb-2 flex  items-center justify-between">
        <div>角色预设</div>
        <div className="flex gap-2 items-center">
          <div className="w-[300px]">
            <Input prefix={<IconSearch />} className="w-[300px] " showClear />
          </div>
          <Button theme="solid" type="primary" onClick={setTrue}>
            创建角色
          </Button>
        </div>
      </div>
      <div>
        <List
          split={false}
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          dataSource={[{}, {}, {}, {}, {}]}
          layout="horizontal"
          renderItem={(item) => (
            <List.Item className="w-full">
              <Card
                style={{ maxWidth: 400 }}
                headerLine={false}
                shadows="hover"
                actions={[
                  <div className="flex justify-between items-center">
                    1·123
                  </div>,
                ]}
              >
                <Meta
                  title="DeepSeek R1"
                  description={
                    <Space>
                      <div>描述</div>
                      <div>描述</div>
                      <div>描述</div>
                    </Space>
                  }
                  avatar={
                    <Avatar
                      shape="square"
                      alt="Card meta img"
                      src="https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/MODEL_ICON/deepseek_v2.png"
                    ></Avatar>
                  }
                />
                Semi Design 是由抖音前端团队与 UED
                团队共同设计开发并维护的设计系统。{" "}
              </Card>
            </List.Item>
          )}
        />
      </div>
    </div>
  );
}
export default Role;
