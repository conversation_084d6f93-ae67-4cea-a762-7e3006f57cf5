import React from "react";

export default function LoginBanner(props: any) {
  return (
    <svg
      {...props}
      viewBox="0 0 800 600"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* 背景渐变 */}
      <defs>
        <linearGradient
          id="bgGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#3873FA" stopOpacity="0.1" />
          <stop offset="100%" stopColor="#6C5CE7" stopOpacity="0.1" />
        </linearGradient>

        <linearGradient
          id="accentGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" stopColor="#3873FA">
            <animate
              attributeName="stop-color"
              values="#3873FA; #6C5CE7; #3873FA"
              dur="8s"
              repeatCount="indefinite"
            />
          </stop>
          <stop offset="100%" stopColor="#6C5CE7">
            <animate
              attributeName="stop-color"
              values="#6C5CE7; #3873FA; #6C5CE7"
              dur="8s"
              repeatCount="indefinite"
            />
          </stop>
        </linearGradient>

        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feGaussianBlur in="SourceAlpha" stdDeviation="15" />
          <feOffset dx="0" dy="10" result="offsetblur" />
          <feComponentTransfer>
            <feFuncA type="linear" slope="0.2" />
          </feComponentTransfer>
          <feMerge>
            <feMergeNode />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      {/* 背景形状 */}
      <rect width="800" height="600" rx="20" >
        <animate
          attributeName="opacity"
          values="0.8;1;0.8"
          dur="10s"
          repeatCount="indefinite"
        />
      </rect>

      {/* 装饰圆形 - 脉动效果 */}
      <circle cx="200" cy="150" r="100" fill="url(#accentGradient)" opacity="0.05">
        <animate
          attributeName="r"
          values="100;110;100"
          dur="15s"
          repeatCount="indefinite"
        />
        <animate
          attributeName="opacity"
          values="0.05;0.08;0.05"
          dur="10s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="650" cy="450" r="120" fill="url(#accentGradient)" opacity="0.05">
        <animate
          attributeName="r"
          values="120;130;120"
          dur="12s"
          repeatCount="indefinite"
        />
        <animate
          attributeName="opacity"
          values="0.05;0.07;0.05"
          dur="8s"
          repeatCount="indefinite"
        />
      </circle>

      {/* 主要设备组 */}
      <g filter="url(#shadow)">
        {/* 左侧手机 - 更真实的现代智能手机 */}
        <g>
          <animateTransform
            attributeName="transform"
            type="translate"
            values="0,0; 0,-8; 0,0"
            dur="7s"
            repeatCount="indefinite"
          />
          {/* 手机外壳 - 更薄的边框 */}
          <rect x="120" y="250" width="80" height="160" rx="10" fill="#1A1A1A" />

          {/* 手机屏幕 - 全面屏设计 */}
          <rect x="123" y="253" width="74" height="154" rx="8" fill="#F8F9FC" />

          {/* 手机顶部刘海/打孔 */}
          <rect x="155" y="253" width="10" height="5" rx="2.5" fill="#1A1A1A" />

          {/* 手机内容 - 更像真实的应用界面 */}
          <rect x="128" y="263" width="64" height="12" rx="2" fill="#3873FA" opacity="0.8" />

          {/* 应用图标网格 */}
          <rect x="133" y="285" width="14" height="14" rx="3" fill="#FF6B6B" />
          <rect x="153" y="285" width="14" height="14" rx="3" fill="#FFD93D" />
          <rect x="173" y="285" width="14" height="14" rx="3" fill="#6BCB77" />

          <rect x="133" y="305" width="14" height="14" rx="3" fill="#3873FA" />
          <rect x="153" y="305" width="14" height="14" rx="3" fill="#9775FA" />
          <rect x="173" y="305" width="14" height="14" rx="3" fill="#FF8FA3" />

          <rect x="133" y="325" width="14" height="14" rx="3" fill="#63E6BE" />
          <rect x="153" y="325" width="14" height="14" rx="3" fill="#FFD43B" />
          <rect x="173" y="325" width="14" height="14" rx="3" fill="#4DABF7" />

          {/* 底部导航栏 */}
          <rect x="128" y="380" width="64" height="20" rx="10" fill="#F0F2F5" />
          <rect x="148" y="385" width="24" height="10" rx="5" fill="#3873FA" opacity="0.8" />
          <circle cx="138" cy="390" r="3" fill="#1A1A1A" />
          <circle cx="182" cy="390" r="3" fill="#1A1A1A" />
        </g>

        {/* 中间笔记本电脑 - 更真实的现代笔记本 */}
        <g>
          <animateTransform
            attributeName="transform"
            type="translate"
            values="0,0; 0,-5; 0,0"
            dur="6s"
            repeatCount="indefinite"
          />
          {/* 笔记本屏幕部分 - 更薄的边框 */}
          <rect x="250" y="200" width="300" height="200" rx="5" fill="#1A1A1A" />
          <rect x="254" y="204" width="292" height="192" rx="3" fill="#F8F9FC" />

          {/* 笔记本底座部分 - 更扁平的设计 */}
          {/* <path d="M 240,400 L 560,400 L 570,420 L 230,420 Z" fill="#1A1A1A" /> */}
          {/* <rect x="270" y="402" width="260" height="16" rx="2" fill="#333333" /> */}

          {/* 笔记本Logo */}
          {/* <circle cx="400" cy="410" r="3" fill="#F8F9FC" /> */}

          {/* 屏幕内容 - 更像真实的操作系统界面 */}
          {/* 顶部菜单栏 */}
          <rect x="254" y="204" width="292" height="20" rx="3" fill="#333333" />
          <circle cx="265" cy="214" r="3" fill="#FF6B6B" />
          <circle cx="275" cy="214" r="3" fill="#FFD93D" />
          <circle cx="285" cy="214" r="3" fill="#6BCB77" />
          <rect x="300" y="210" width="20" height="8" rx="2" fill="#FFFFFF" opacity="0.5" />
          <rect x="330" y="210" width="20" height="8" rx="2" fill="#FFFFFF" opacity="0.5" />
          <rect x="360" y="210" width="20" height="8" rx="2" fill="#FFFFFF" opacity="0.5" />
          <rect x="510" y="210" width="25" height="8" rx="2" fill="#FFFFFF" opacity="0.5" />

          {/* 主窗口 */}
          <rect x="270" y="234" width="260" height="150" rx="3" fill="#FFFFFF" stroke="#E0E0E0" strokeWidth="1" />

          {/* 窗口标题栏 */}
          <rect x="270" y="234" width="260" height="25" rx="3" fill="#F0F2F5" />
          <rect x="280" y="242" width="100" height="8" rx="2" fill="#333333" opacity="0.7" />
          <circle cx="515" cy="246" r="3" fill="#333333" opacity="0.7" />
          <circle cx="500" cy="246" r="3" fill="#333333" opacity="0.7" />

          {/* 窗口内容 - 数据可视化 */}
          <rect x="280" y="269" width="120" height="10" rx="2" fill="#F0F2F5" />
          <rect x="280" y="269" width="0" height="10" rx="2" fill="#3873FA">
            <animate
              attributeName="width"
              values="0;80"
              dur="3s"
              repeatCount="indefinite"
            />
          </rect>

          <rect x="280" y="289" width="120" height="10" rx="2" fill="#F0F2F5" />
          <rect x="280" y="289" width="0" height="10" rx="2" fill="#3873FA" opacity="0.8">
            <animate
              attributeName="width"
              values="0;60"
              dur="3s"
              repeatCount="indefinite"
            />
          </rect>

          <rect x="280" y="309" width="120" height="10" rx="2" fill="#F0F2F5" />
          <rect x="280" y="309" width="0" height="10" rx="2" fill="#3873FA" opacity="0.6">
            <animate
              attributeName="width"
              values="0;100"
              dur="3s"
              repeatCount="indefinite"
            />
          </rect>

          {/* 右侧圆形图表 */}
          <circle cx="470" cy="290" r="35" fill="transparent" stroke="#F0F2F5" strokeWidth="6" />
          <circle
            cx="470"
            cy="290"
            r="35"
            fill="transparent"
            stroke="#3873FA"
            strokeWidth="6"
            strokeLinecap="round"
            strokeDasharray="220"
            strokeDashoffset="220"
          >
            <animate
              attributeName="stroke-dashoffset"
              from="220"
              to="0"
              dur="3s"
              repeatCount="indefinite"
            />
          </circle>

          {/* 底部状态栏 */}
          <rect x="270" y="359" width="260" height="25" rx="3" fill="#F0F2F5" />
          <rect x="280" y="367" width="40" height="8" rx="2" fill="#333333" opacity="0.7" />
          <rect x="480" y="367" width="40" height="8" rx="2" fill="#333333" opacity="0.7" />
        </g>

        {/* 右侧平板 */}
        <g>
          <animateTransform
            attributeName="transform"
            type="translate"
            values="0,0; 0,-7; 0,0"
            dur="8s"
            repeatCount="indefinite"
          />
          {/* 平板外壳 */}
          <rect x="600" y="230" width="100" height="180" rx="8" fill="#1A1A1A" />

          {/* 平板屏幕 */}
          <rect x="604" y="234" width="92" height="172" rx="6" fill="#F8F9FC" />

          {/* 平板顶部摄像头 */}
          <circle cx="650" cy="242" r="2" fill="#333333" />

          {/* 平板内容 - 图表应用 */}
          <rect x="614" y="254" width="72" height="20" rx="3" fill="#3873FA" opacity="0.8" />
          <rect x="624" y="260" width="52" height="8" rx="2" fill="#FFFFFF" />

          {/* 图表区域 */}
          <rect x="614" y="284" width="72" height="70" rx="3" fill="#FFFFFF" stroke="#E0E0E0" strokeWidth="1" />

          {/* 柱状图 */}
          <rect x="624" y="334" width="8" height="10" rx="1" fill="#3873FA" opacity="0.8">
            <animate
              attributeName="height"
              values="10;30;10"
              dur="4s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="y"
              values="334;314;334"
              dur="4s"
              repeatCount="indefinite"
            />
          </rect>

          <rect x="637" y="334" width="8" height="10" rx="1" fill="#3873FA" opacity="0.6">
            <animate
              attributeName="height"
              values="10;20;10"
              dur="4s"
              begin="0.5s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="y"
              values="334;324;334"
              dur="4s"
              begin="0.5s"
              repeatCount="indefinite"
            />
          </rect>

          <rect x="650" y="334" width="8" height="10" rx="1" fill="#3873FA" opacity="0.7">
            <animate
              attributeName="height"
              values="10;25;10"
              dur="4s"
              begin="1s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="y"
              values="334;319;334"
              dur="4s"
              begin="1s"
              repeatCount="indefinite"
            />
          </rect>

          <rect x="663" y="334" width="8" height="10" rx="1" fill="#3873FA" opacity="0.5">
            <animate
              attributeName="height"
              values="10;15;10"
              dur="4s"
              begin="1.5s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="y"
              values="334;329;334"
              dur="4s"
              begin="1.5s"
              repeatCount="indefinite"
            />
          </rect>

          {/* 平板底部内容 */}
          <rect x="614" y="364" width="72" height="8" rx="2" fill="#3873FA" opacity="0.8" />
          <rect x="614" y="377" width="52" height="8" rx="2" fill="#3873FA" opacity="0.6" />
          <rect x="614" y="390" width="62" height="8" rx="2" fill="#3873FA" opacity="0.7" />

          {/* 平板底部Home键/指示器 */}
          <rect x="635" y="405" width="30" height="1" rx="0.5" fill="#333333" />
        </g>

        {/* 连接线 */}
        <path
          d="M 200 300 Q 225 250, 250 300"
          stroke="#3873FA"
          strokeWidth="2"
          strokeDasharray="4 4"
          fill="none"
        />

        <path
          d="M 550 300 Q 575 350, 600 300"
          stroke="#3873FA"
          strokeWidth="2"
          strokeDasharray="4 4"
          fill="none"
        />

        {/* 移动的小圆点 */}
        <circle cx="200" cy="300" r="4" fill="#3873FA">
          <animate
            attributeName="cx"
            values="200;250"
            dur="3s"
            repeatCount="indefinite"
          />
          <animate
            attributeName="cy"
            values="300;300"
            dur="3s"
            repeatCount="indefinite"
          />
        </circle>

        <circle cx="550" cy="300" r="4" fill="#3873FA">
          <animate
            attributeName="cx"
            values="550;600"
            dur="3s"
            repeatCount="indefinite"
          />
          <animate
            attributeName="cy"
            values="300;300"
            dur="3s"
            repeatCount="indefinite"
          />
        </circle>
      </g>

      {/* 装饰元素 - 浮动圆点 */}
      <circle cx="150" cy="150" r="3" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="150;140;150"
          dur="8s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="180" cy="180" r="2" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="180;190;180"
          dur="10s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="120" cy="200" r="4" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="200;210;200"
          dur="7s"
          repeatCount="indefinite"
        />
      </circle>

      <circle cx="650" cy="150" r="3" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="150;140;150"
          dur="9s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="680" cy="180" r="2" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="180;170;180"
          dur="6s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="620" cy="200" r="4" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="200;190;200"
          dur="8s"
          repeatCount="indefinite"
        />
      </circle>

      <circle cx="400" cy="100" r="3" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="100;110;100"
          dur="7s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="430" cy="80" r="2" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="80;70;80"
          dur="9s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="370" cy="120" r="4" fill="#3873FA" opacity="0.5">
        <animate
          attributeName="cy"
          values="120;130;120"
          dur="8s"
          repeatCount="indefinite"
        />
      </circle>

      {/* AI 元素 - 旋转和脉动效果 */}
      <g transform="translate(400, 150) scale(0.8)">
        <animateTransform
          attributeName="transform"
          type="rotate"
          from="0 400 150"
          to="360 400 150"
          dur="60s"
          repeatCount="indefinite"
          additive="sum"
        />
        <circle cx="0" cy="0" r="40" fill="url(#accentGradient)" opacity="0.1">
          <animate
            attributeName="r"
            values="40;45;40"
            dur="5s"
            repeatCount="indefinite"
          />
          <animate
            attributeName="opacity"
            values="0.1;0.2;0.1"
            dur="5s"
            repeatCount="indefinite"
          />
        </circle>
        <path d="M -20 -10 L 0 -25 L 20 -10 L 20 15 L 0 30 L -20 15 Z" stroke="url(#accentGradient)" strokeWidth="2" fill="none">
          <animate
            attributeName="stroke-width"
            values="2;3;2"
            dur="5s"
            repeatCount="indefinite"
          />
        </path>
        <path d="M 0 -25 L 0 30" stroke="url(#accentGradient)" strokeWidth="2">
          <animate
            attributeName="stroke-width"
            values="2;3;2"
            dur="5s"
            repeatCount="indefinite"
          />
        </path>
        <path d="M -20 -10 L 20 -10" stroke="url(#accentGradient)" strokeWidth="2">
          <animate
            attributeName="stroke-width"
            values="2;3;2"
            dur="5s"
            repeatCount="indefinite"
          />
        </path>
        <path d="M -20 15 L 20 15" stroke="url(#accentGradient)" strokeWidth="2">
          <animate
            attributeName="stroke-width"
            values="2;3;2"
            dur="5s"
            repeatCount="indefinite"
          />
        </path>
      </g>

      {/* 数据流动效果 - 笔记本到平板 */}
      <circle cx="250" cy="300" r="2" fill="url(#accentGradient)">
        <animate
          attributeName="cx"
          values="250;170"
          dur="3s"
          begin="0s;6s"
          fill="freeze"
        />
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="3s"
          begin="0s;6s"
          fill="freeze"
        />
      </circle>

      {/* 数据流动效果 - 笔记本到手机 */}
      <circle cx="550" cy="300" r="2" fill="url(#accentGradient)">
        <animate
          attributeName="cx"
          values="550;635"
          dur="3s"
          begin="2s;8s"
          fill="freeze"
        />
        <animate
          attributeName="opacity"
          values="0;1;0"
          dur="3s"
          begin="2s;8s"
          fill="freeze"
        />
      </circle>
    </svg>
  );
}
