// Create a new file: navigationUtils.ts
export const getDeepestChild = (menu: MenuProps) => {
  let current = menu;
  //   @ts-ignore
  while (current?.children?.length > 0) {
    //   @ts-ignore
    current = current?.children[0];
  }
  return current;
};

export const getTitleFromMenu = (menu: MenuProps) => {
  const deepest = getDeepestChild(menu);
  return deepest?.label ?? menu.label ?? "LynkzHub";
};
