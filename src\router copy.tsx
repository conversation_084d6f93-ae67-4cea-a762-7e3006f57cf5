import {
  createRootRoute,
  createRoute,
  Outlet,
  redirect,
} from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/router-devtools";
import React from "react";
import CommentIndex from "@/pages/comment/Index";
import SystemLayout from "@/layout/defaultLayout/index";
import BaseLayout from "@/layout/base/index";
import ModelIndex from "@/pages/platform/model/index";
import Role from "@/pages/platform/role";
import PluginIndex from "@/pages/setting/PluginIndex";
import Workbench from "@/pages/workbench";
import Login from "@/pages/auth/login";
import Knowledge from "./pages/platform/resource/knowledge";

//创建跟路由
const rootRoute = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  return (
    <React.Fragment>
      <Outlet />
      {/* <TanStackRouterDevtools position="bottom-left" /> */}
    </React.Fragment>
  );
}

//创建认证路由
const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/login",
  component: () => <Login />,
});

//创建首页聊天对话框
const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/",
  component: CommentIndex,
});

//创建后台管理路由

//1.平台路由
const platformRoute = createRoute({
  getParentRoute: () => rootRoute,
  id: "platform",
  beforeLoad: ({ context, location }) => {
    //判断是否登录
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: PlatformRoute,
});
function PlatformRoute() {
  return (
    <BaseLayout>
      <Outlet />
    </BaseLayout>
  );
}

//1.平台路由model
const platformModelRoute = createRoute({
  getParentRoute: () => platformRoute,
  path: "/platform/model",
  component: ModelIndex,
});
//1.平台路由role
const platformRoleRoute = createRoute({
  getParentRoute: () => platformRoute,
  path: "/platform/role",
  component: Role,
});

//1.平台路由插件
const platformPluginRoute = createRoute({
  getParentRoute: () => platformRoute,
  path: "/platform/plugin",
  component: PluginIndex,
});

//创建工作台路由
const workbenchRoute = createRoute({
  getParentRoute: () => platformRoute,
  path: "/workbench",
  component: Workbench,
});

//知识库路由
const datasetsRoute = createRoute({
  getParentRoute: () => platformRoute,
  path: "/platform/knowledge",
  component: Knowledge,
});

const routeTree = rootRoute.addChildren([
  loginRoute,
  platformRoute.addChildren([
    datasetsRoute,
    workbenchRoute,
    platformPluginRoute,
    platformModelRoute,
    platformRoleRoute,
  ]),
  indexRoute,
]);

export default routeTree;
