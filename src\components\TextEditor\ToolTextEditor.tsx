import "@mdxeditor/editor/style.css";
import "./index.scss";

import { useEffect, useRef, type ForwardedRef } from "react";
import {
  MDXEditor,
  UndoRedo,
  BoldItalicUnderlineToggles,
  toolbarPlugin,
  MDXEditorMethods,
  MDXEditorProps,
  CodeToggle,
  BlockTypeSelect,
  ChangeCodeMirrorLanguage,
  InsertTable,
  ListsToggle,
  headingsPlugin,
  listsPlugin,
  markdownShortcutPlugin,
  quotePlugin,
  thematicBreakPlugin,
  frontmatterPlugin,
  codeBlockPlugin,
  codeMirrorPlugin,
  AdmonitionDirectiveDescriptor,
  CodeBlockEditorDescriptor,
  useCodeBlockEditorContext,
  CodeMirrorEditor,
  diffSourcePlugin,
  directivesPlugin,
  tablePlugin,
  ChangeAdmonitionType,
  ShowSandpackInfo,
  InsertImage,
  DiffSourceToggleWrapper,
  Separator,
  CreateLink,
  imagePlugin,
  InsertAdmonition,
  InsertFrontmatter,
  InsertSandpack,
  sandpackPlugin,
  InsertThematicBreak,
} from "@mdxeditor/editor";
import { withField } from "@douyinfe/semi-ui";
import { debounce } from "lodash-es";
import classNames from "classnames";
function ToolTextEditor({ ...props }: MDXEditorProps & any) {
  let value = props.value || "";
  const ref = useRef<ForwardedRef<MDXEditorMethods>>();
  let { onChange, className, ...rest } = props;
  useEffect(() => {
    // @ts-expect-error
    ref?.current?.setMarkdown(value);
  }, [value]);
  const debouncedHandleOnChange = debounce((md: string) => {
    // console.log(md);
    onChange(md);
  }, 300);
  // const PlainTextCodeEditorDescriptor: CodeBlockEditorDescriptor = {
  //   // always use the editor, no matter the language or the meta of the code block
  //   match: (language, meta) => true,
  //   // You can have multiple editors with different priorities, so that there's a "catch-all" editor (with the lowest priority)
  //   priority: 0,
  //   // The Editor is a React component
  //   Editor: (props) => {
  //     const cb = useCodeBlockEditorContext();
  //     // stops the propagation so that the parent lexical editor does not handle certain events.
  //     return (
  //       <div onKeyDown={(e) => e.nativeEvent.stopImmediatePropagation()}>
  //         <textarea
  //           rows={3}
  //           cols={20}
  //           defaultValue={props.code}
  //           onChange={(e) => cb.setCode(e.target.value)}
  //         />
  //       </div>
  //       // <ChangeCodeMirrorLanguage></ChangeCodeMirrorLanguage>
  //     );
  //   },
  // };

  async function imageUploadHandler(image: File) {
    // const formData = new FormData();
    // formData.append("image", image);
    // // send the file to your server and return
    // // the URL of the uploaded image in the response
    // const response = await fetch("/uploads/new", {
    //   method: "POST",
    //   body: formData,
    // });
    // const json = (await response.json()) as { url: string };
    // return json.url;

    //image 转base64
    const data = new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = reader.result as string;
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(image);
    });
    console.log(data);
    return data;
  }

  return (
    <MDXEditor
      className={classNames("overflow-y-auto box-border", className)}
      {...rest}
      ref={ref}
      markdown={value}
      onChange={debouncedHandleOnChange}
      contentEditableClassName="prose-content"
      // translation={(key, defaultValue, interpolations) => {
      //   // return { key, defaultValue, interpolations };
      //   // return i18n.t(key, defaultValue, interpolations);
      //   // return ;
      // }}
      plugins={[
        toolbarPlugin({
          // toolbarClassName: "my-classname",
          toolbarContents: () => (
            <>
              {/* <BlockTypeSelect />
              <UndoRedo />
              <ShowSandpackInfo />
              <BoldItalicUnderlineToggles /> */}
              {/* <DiffSourceToggleWrapper> */}
              <UndoRedo />
              <BoldItalicUnderlineToggles />
              <ListsToggle />
              <Separator />
              <BlockTypeSelect />
              <CreateLink />
              <InsertImage />
              <Separator />
              {/* <InsertAdmonition /> */}
              {/* <InsertFrontmatter /> */}
              <InsertTable />
              {/* <InsertSandpack /> */}
              <InsertThematicBreak />

              {/* </DiffSourceToggleWrapper> */}
              {/* <ChangeAdmonitionType /> */}
              {/* <ChangeCodeMirrorLanguage /> */}
            </>
          ),
        }),

        tablePlugin(),
        listsPlugin(),
        headingsPlugin(),
        thematicBreakPlugin(),
        quotePlugin(),
        sandpackPlugin({
          sandpackConfig: {
            defaultPreset: "",
            presets: [],
          },
        }),

        codeBlockPlugin({
          codeBlockEditorDescriptors: [
            { priority: -10, match: (_) => true, Editor: CodeMirrorEditor },
          ],
        }),
        codeMirrorPlugin({
          autoLoadLanguageSupport: true,
          codeBlockLanguages: {
            jsx: "JavaScript (react)",
            js: "JavaScript",
            css: "CSS",
            java: "Java",
            ts: "TypeScript",
            sql: "SQL",
            tsx: "TypeScript (react)",
          },
        }),
        frontmatterPlugin(),
        markdownShortcutPlugin(),
        imagePlugin({
          imageUploadHandler: imageUploadHandler,
          // imageAutocompleteSuggestions: [
          //   "https://picsum.photos/200/300",
          //   "https://picsum.photos/200",
          // ],
        }),
      ]}
    />
  );
}

const FormToolTextEditor = withField(ToolTextEditor, {
  valueKey: "value",
  onKeyChangeFnName: "onChange",
  // valuePath: "target.value",
});

export { ToolTextEditor, FormToolTextEditor };
