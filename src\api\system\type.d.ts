type ClientVO = API.BaseEntity<{
  /** token活跃超时时间 */
  activeTimeout: number;
  /** 客户端id */
  clientId: string;
  /** 客户端key */
  clientKey: string;
  /** 客户端秘钥 */
  clientSecret: string;
  /** 设备类型 */
  deviceType: null | string;
  /** 授权类型 */
  grantTypeList: string[];
  /** id */
  id: number | string;
  /** 状态（0正常 1停用） */
  status: string;
  /** token固定超时 */
  timeout: number;
}>;

type NoticeVO = API.BaseEntity<{
  createdByName: string;
  deadline: any | number | string;
  filterCondition: any | number | string;
  filterType: any | number | string;
  immortal: number | string;
  noticeContent: string;
  noticeId: number | string;
  noticeTitle: string;
  noticeType: any | string;
  receiveMode: any | number | string;
  remark: string;
  status: string;
}>;

type ClientChangeStatus = {
  clientId: number | string;
  status: string;
};


interface SysTenantVo {
  id: number | null;
  tenantId: string;
  contactUserName: string;
  contactPhone: string;
  companyName: string;
  licenseNumber: string;
  address: string;
  domain: string;
  intro: string;
  remark: string;
  packageId: number | null;
  expireTime: Date | null;
  accountCount: number | null;
  status: string;
  logo: string;
  zipCode: string;
  industry: string;
  size: string;
}


interface SysUserVo {
  userId: number | null;
  tenantId: string;
  deptId: number | null;
  userName: string;
  nickName: string;
  userType: string;
  email: string;
  phonenumber: string;
  sex: string;
  avatar: string | null;
  password: string;
  status: string;
  loginIp: string;
  loginDate: Date | null;
  remark: string;
  createdAt: Date | null;
  deptName: string;
  roles: SysRoleVo[];
  roleIds: number[] | null;
  postIds: number[] | null;
  roleId: number | null;
  signature: string;
  birthday: Date | null;
}

interface UserInfoVo<TUser = SysUserVo, TCompany = SysTenantVo> {
  user: TUser;
  permissions: Set<string>;
  roles: Set<string>;
  company?: TCompany;
}
interface SysRoleVo {
  /**
   * 角色ID
   */
  roleId: number | null;

  /**
   * 角色名称
   */
  roleName: string;

  /**
   * 角色权限字符串
   */
  roleKey: string;

  /**
   * 显示顺序
   */
  roleSort: number | null;

  /**
   * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
   */
  dataScope: string;

  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;

  /**
   * 部门树选择项是否关联显示
   */
  deptCheckStrictly?: boolean;

  /**
   * 角色状态（0正常 1停用）
   */
  status: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 创建时间
   */
  createdAt?: Date | null;

  /**
   * 用户是否存在此角色标识 默认不存在
   */
  flag: boolean;

  /**
   * 是否是超级管理员
   */
  superAdmin: boolean;
}