import { create } from "zustand";
import { persist, devtools, PersistStorage, StorageValue } from "zustand/middleware";

interface AuthState {
  tokenData: any | null;
  isAuthenticated: boolean;
  setTokenData: (token: any) => void;
  clearTokenData: () => void;
}
const storage: PersistStorage<AuthState> = {
  getItem: (name): StorageValue<AuthState> | Promise<StorageValue<AuthState> | null> | null => {
    const item = localStorage.getItem(name);
    return item ? JSON.parse(item) : null;
  },
  setItem: (name, value) => {
    localStorage.setItem(name, JSON.stringify(value));
  },
  removeItem: (name) => {
    localStorage.removeItem(name);
  },
};

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      tokenData: null,
      isAuthenticated: false,
      setTokenData: (token) => {
        set({ tokenData: token, isAuthenticated: !!token });
      },
     //删除缓存的数据
      clearTokenData: () => {
        // set({ tokenData: null, isAuthenticated: false });
        // storage.removeItem('auth-storage')
      },
   
    }),
    {
      name: "auth-storage", // 存储到 localStorage 的 key 名称
      storage: storage, // 使用 localStorage 持久化
    }
  )
);