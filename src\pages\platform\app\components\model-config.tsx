import React from 'react';
import { <PERSON>, Button, Avatar } from '@douyinfe/semi-ui';
import { IconSetting } from '@douyinfe/semi-icons';

interface ModelConfigProps {
    modelList: any[];
    selectModelId: string;
    setSelectModelId: (value: string) => void;
    onModelConfigOpen: () => void;
}

const ModelConfig: React.FC<ModelConfigProps> = ({
    modelList,
    selectModelId,
    setSelectModelId,
    onModelConfigOpen,
}) => {
    return (
        <div className="flex items-center justify-between pr-1">
            <div className="gap-2 pl-1 text-[14px] font-semibold flex items-center">
                <div className="w-5 h-5 rounded-full flex items-center justify-center bg-semi-color-primary-light-hover">
                    <span>AI</span>
                </div>
                <span>模型配置</span>
            </div>

            <div className="items-center flex gap-2 flex-wrap">
                <Form.Select
                    field="model.modelId"
                    showClear
                    noLabel
                    onChange={setSelectModelId}
                    placeholder="请选择模型"
                    className="min-w-[280px]"
                >
                    {modelList.map((item) => (
                        <Form.Select.OptGroup key={item.value} label={item.name}>
                            {item.children.map((child: any) => (
                                <Form.Select.Option key={child.value} value={child.value}>
                                    <div className="flex flex-row gap-2 items-center">
                                        <Avatar size="extra-small" src={child.extend2} />
                                        <div>{child.label}</div>
                                    </div>
                                </Form.Select.Option>
                            ))}
                        </Form.Select.OptGroup>
                    ))}
                </Form.Select>

                <Button type="tertiary" onClick={onModelConfigOpen} icon={<IconSetting />} />
            </div>
        </div>
    );
};

export default ModelConfig;