import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const CRISPEFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.capacityAndRole"
          label="能力与角色"
          rows={6}
          placeholder="ChatGPT扮演什么角色"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.insight"
          label="见解"
          rows={6}
          placeholder="呈现见解、假设和上下文"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.statement"
          label="声明"
          rows={6}
          placeholder="你要求ChatGPT做什么"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.personality"
          label="个性"
          rows={6}
          placeholder="希望以何种风格、个性方式回应"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.experiment"
          label="实验"
          rows={6}
          placeholder="为问题做多个示例"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default CRISPEFields;