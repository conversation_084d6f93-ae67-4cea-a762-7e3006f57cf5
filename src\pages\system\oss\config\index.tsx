import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import Icon, { IconConfigStroked, IconDelete, IconDeleteStroked, IconEdit, IconEditStroked, IconLoopTextStroked, IconMore, IconSearch, IconSync } from "@douyinfe/semi-icons";
import {
  Badge,
  Button,
  Card,
  Dropdown,
  Empty,
  Form,
  Input,
  List,
  Modal,
  OverflowList,
  Pagination,
  Table,
  Tag,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import { useEffect, useState, useCallback } from "react";
import { debounce } from "lodash-es";
import { useTable } from "@/hooks/useTables";
import { changeOssConfigStatus, delOssConfig, getOssConfigList } from "@/api/system/oss-config";
import EmtpyBox from "@/components/icon/EmtpyBox";
import { IllustrationNoContentDark } from "@douyinfe/semi-illustrations";
import StorageIcon from "@/components/icon/StorageIcon";
import ConfigForm from "./config-form";
export default function Oss() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_is_http",
    "sys_oss_config_enable",
    "sys_oss_config_access",
  ]);
  const [open, setOpen] = useState(false);
  const [currentId, setCurrentId] = useState(null);
  const [ossEnableOptions, setOssEnableOptions] = useState([]);
  useEffect(() => {
    setTimeout(() => {
      loadDictionary();
      setOssEnableOptions(() => {
        return dictionaryData?.sys_oss_config_enable?.map((item: any) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          };
        });
      });
    }, 0);
  }, []);

  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getOssConfigList,
    params: {},
    columns: [
      {
        title: "配置键",
        dataIndex: "configKey",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "访问站点",
        dataIndex: "endpoint",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "桶名称",
        dataIndex: "bucketName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "前缀",
        dataIndex: "prefix",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "自定义域名",
        dataIndex: "domain",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "域",
        dataIndex: "region",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "桶权限类型",
        dataIndex: "accessPolicy",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_oss_config_access"
              dictValue={text}
              dictionaryData={dictionaryData.sys_oss_config_access || []}
            />
          );
        },
      },
      {
        title: "是否启用",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_oss_config_enable"
              dictValue={text}
              dictionaryData={dictionaryData.sys_oss_config_enable || []}
            />
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button theme="borderless">详情</Button>
              <Dropdown
                className="min-w-[120px]"
                zIndex={1000}
                position="bottom"
                trigger={"click"}
                render={
                  <Dropdown.Menu>
                    <Dropdown.Item
                      icon={<IconEdit />}
                    // onClick={() => handleEditForm(row)}
                    >
                      修改
                    </Dropdown.Item>
                    <Dropdown.Item
                      type="danger"
                      icon={<IconDelete />}
                    // onClick={() => handleRemoveDictData(row.dictCode)}
                    >
                      删除
                    </Dropdown.Item>
                  </Dropdown.Menu>
                }
              >
                <Button
                  type="tertiary"
                  theme="borderless"
                  icon={<IconMore className="cursor-pointer" />}
                ></Button>
              </Dropdown>
            </div>
          );
        },
      },
    ],
  });
  const handleRemoveClient = (id: number) => {
    const modal = Modal.warning({
      title: "确认删除存储配置吗？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await delOssConfig(id)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  // const handleBatchDelete = () => {
  //   const modal = Modal.warning({
  //     title: "批量删除提醒",
  //     content: "确认删除客户端数据？删除后无法恢复，请谨慎操作！",
  //     confirmLoading: false,
  //     onOk: async () => {
  //       modal.update({
  //         confirmLoading: true,
  //       });
  //       // 删除字典数据
  //       return await delOssConfig(selectedRowKeys)
  //         .then(({ msg }) => {
  //           Toast.success(msg);
  //           //刷新列表
  //           refresh();
  //           return Promise.resolve();
  //         })
  //         .catch(() => {
  //           return Promise.reject();
  //         });
  //     },
  //   });
  // };
  const enableConfig = async (item: any) => {
    const modal = Modal.warning({
      title: "确定启用该配置",
      content: "只允许设置一个配置为启用，启用后将覆盖其他配置",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 修改配置
        return await changeOssConfigStatus({
          ossConfigId: item.ossConfigId,
          status: "0"
        })
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  }
  return (
    <div className="h-full w-full overflow-auto relative flex flex-col py-2 px-2">
      {/* 标题和操作区域 */}
      <div className="text-nowrap font-extrabold  gap-2 pt-2 text-lg mb-2 flex-col flex  justify-between">
        <div className="flex flex-row justify-between items-center">
          <div>存储配置</div>
          <div className="flex gap-2 items-center">
            <Button theme="solid" type="primary" onClick={() => setOpen(true)}>
              创建配置
            </Button>
          </div>
        </div>
        <div className="flex  flex-row">
          <div>
            <Input prefix={<IconSearch />} className="w-[400px]" showClear />
          </div>
        </div>
      </div>
      <div style={{ height: "calc(100vh - 120px)" }} className="overflow-y-auto overflow-x-hidden">
        <List
          split={false}
          className="h-full"
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          loading={loading}
          emptyContent={
            <Empty
              image={<EmtpyBox />}
              darkModeImage={<IllustrationNoContentDark />}
              description="暂无数据"
            />
          }
          dataSource={dataSource}
          layout="horizontal"
          renderItem={(item) => (
            <List.Item
              className="w-full"
              onClick={(e) => {
                e.stopPropagation()
                // handleGotoDetails(item)
              }}
            >
              {/* 卡片具体内容 */}
              <Card
                className="w-full relative"
                bodyStyle={{ width: "100%", padding: "20px" }}
                shadows="hover"
                footerStyle={{
                  padding: "10px 10px"
                }}
                footer={
                  <div className="flex justify-between items-center">
                    <div className="flex gap-2 items-center">
                      <DictTag
                        dictType="sys_oss_config_access"
                        dictValue={item.accessPolicy}
                        dictionaryData={dictionaryData.sys_oss_config_access}
                      />
                      <Tag color='amber'>
                        {
                          item.isHttps === "Y" ? "https" : "http"
                        }
                      </Tag>
                      {
                        item.prefix && <Tag color='teal'>
                          {item.prefix}
                        </Tag>
                      }

                    </div>
                    <div className="flex gap-1 items-center">
                      <Typography.Text type="secondary">
                        {item.createdAt}
                      </Typography.Text>
                      {/* 操作下拉菜单 */}
                      <Dropdown
                        position="bottom"
                        className="w-[140px]"
                        render={
                          <Dropdown.Menu>
                            <Dropdown.Item onClick={(e) => {
                              e.stopPropagation();
                              enableConfig(item)
                            }} icon={<IconLoopTextStroked />}>启用存储</Dropdown.Item>
                            <Dropdown.Item onClick={(e) => {
                              e.stopPropagation()
                              setOpen(true);
                              setCurrentId(item.ossConfigId);
                            }}
                              icon={
                                <IconEditStroked />
                              }>修改配置</Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item type="danger" onClick={(e) => {
                              e.stopPropagation()
                              handleRemoveClient(item.ossConfigId)
                            }
                            } icon={<IconDeleteStroked />}>删除配置</Dropdown.Item>
                          </Dropdown.Menu>
                        }
                      >
                        <Button
                          theme="borderless"
                          type="tertiary"
                          onClickCapture={(e) => e.stopPropagation()}
                          icon={<IconMore />}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </Dropdown>
                    </div>
                  </div>
                }
              >
                {
                  item.status === "0" && <div className="absolute  right-[-12px] top-[10px] z-10">
                    <div className="inline-block bg-semi-color-primary text-semi-color-white rotate-45 px-3 py-1 text-center bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs font-semibold whitespace-nowrap shadow-md rounded-sm transform transition-transform hover:scale-105">
                      正在使用
                    </div>
                  </div>
                }

                {/* 卡片内容 */}
                <Card.Meta
                  avatar={<Icon svg={<StorageIcon />} />}
                  title={
                    <div className="flex flex-col">
                      <Typography.Text strong>{item.configKey}</Typography.Text>
                      <Typography.Text type="tertiary" className="text-sx" size="small">
                        {item.domain ? item.domain : "-"}
                      </Typography.Text>
                    </div>
                  }
                />
                <div className="h-[40px] flex flex-col gap-1 overflow-y-auto">
                  <Typography.Text
                    copyable
                    type="secondary"
                    ellipsis={{
                      rows: 2,
                    }}
                    className="text-sm px-1 "
                  >
                    {item.endpoint ? item.endpoint : "-"}
                  </Typography.Text>
                </div>
              </Card>
            </List.Item >
          )
          }
        />
      </div >
      <div className="flex justify-end  px-2 py-2  rounded-lg">
        <Pagination
          {
          ...pagination
          }
        />
      </div>
      <ConfigForm open={open} id={currentId} onRefresh={refresh} onCancel={() => {
        setOpen(false);
        setCurrentId(null);
      }} sys_oss_config_access={dictionaryData.sys_oss_config_access} sys_is_http={dictionaryData.sys_is_http} />
    </div >
  );
}
