import { Avatar, Divider, Dropdown, DropdownItem } from "@douyinfe/semi-ui";

import {
  IconBell,
  IconChevronRight,
  IconCustomerSupport,
  IconExit,
  IconInfoCircle,
  IconLanguage,
  IconUser,
} from "@douyinfe/semi-icons";
import classNames from "classnames";
import AboutModel from "./AboutModel";
import { useState } from "react";
import UserProfile from "./UserProfile";
const SiderAvatar: React.FC<SiderAvatarProps> = ({
  className,
  position,
  collapsed = false,
}) => {
  const [open, setOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);

  const handleOpenAbout = () => {
    setOpen(true);
  };
  const handleOpenProfile = () => {
    setProfileOpen(true);
  };
  return (
    <>
      <Dropdown
        zIndex={1000}
        trigger={"click"}
        className="w-[230px] min-h-[120px]"
        position={position ? position : "bottom"}
        render={
          <Dropdown.Menu className="w-full">
            <div className="px-3 py-2 flex gap-2 hover:bg-semi-color-fill-0 cursor-pointer">
              <Avatar
                style={{ backgroundColor: "#1890ff" }}
                className="rounded-lg"
                size="default"
                shape="square"
              >
                He
              </Avatar>
              <div className="flex flex-col items-start">
                <div className="font-semibold">Herther</div>
                <div className="text-semi-color-text-2">12312312</div>
              </div>
            </div>
            <Divider />
            <DropdownItem icon={<IconUser />} onClick={handleOpenProfile}>
              账户设置
            </DropdownItem>
            {/* <DropdownItem icon={<IconBell />}>消息通知</DropdownItem> */}
            <Divider />
            <Dropdown
              zIndex={1000}
              trigger={"click"}
              className="w-[230px]"
              position={position ? position : "left"}
              render={
                <Dropdown.Menu className="w-full">
                  <DropdownItem>
                    <div>简体中文</div>
                  </DropdownItem>
                  <DropdownItem>
                    <div>English</div>
                  </DropdownItem>
                </Dropdown.Menu>
              }
            >
              <DropdownItem icon={<IconLanguage />}>
                <div className="flex w-full justify-between flex-row items-center">
                  <div>语言切换</div>
                  <IconChevronRight />
                </div>
              </DropdownItem>
            </Dropdown>

            <DropdownItem icon={<IconInfoCircle />} onClick={handleOpenAbout}>
              关于
            </DropdownItem>
            <Divider />
            <DropdownItem
              icon={<IconExit className="text-semi-color-danger" />}
            >
              退出登录
            </DropdownItem>
          </Dropdown.Menu>
        }
      >
        <div
          className={classNames(
            className,
            "flex items-center justify-start gap-2  hover:bg-semi-color-fill-0 rounded-lg px-2 py-1 cursor-pointer"
          )}
        >
          <Avatar
            style={{ backgroundColor: "#87d068" }}
            className="w-full"
            size="small"
            alt="Alice Swift"
          >
            AS
          </Avatar>
          {collapsed ? null : <div>Herther</div>}
        </div>
      </Dropdown>
      <AboutModel open={open} onCancel={() => setOpen(false)} />
      <UserProfile open={profileOpen} onCancel={() => setProfileOpen(false)} />
    </>
  );
};

export default SiderAvatar;
