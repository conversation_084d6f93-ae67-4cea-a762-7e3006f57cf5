import {
  Avatar,
  Button,
  Card,
  Col,
  <PERSON>lapse,
  DatePicker,
  Divider,
  Form,
  Input,
  List,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Space,
  Tag,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { createRef, useEffect, useState } from "react";
import { LocalForageService as storage } from "../../../utils/storage";
import { OpenAIAttribute } from "../../../interface/llm";
import { initialOpenaiAttribute } from "../../../utils/initial-state";
import { MessageUtil } from "../../../utils/message-util";
import { IconHelpCircle, IconSearch } from "@douyinfe/semi-icons";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { useBoolean } from "@/hooks";
import ModelForm from "./components/ModelForm";

function ModelIndex() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  const [openaiAttribute, setOpenaiAttribute] = useState<OpenAIAttribute>(
    initialOpenaiAttribute
  );

  const openaiAttributeFormRef = createRef<Form<OpenAIAttribute>>();

  useEffect(() => {
    fetchOpenaiAttribute();
  }, []);

  const fetchOpenaiAttribute = async () => {
    const llmOpenaiAttribute = await storage.getItem<OpenAIAttribute>(
      "llm_openai_attribute"
    );
    const newOpenaiAttribute =
      MessageUtil.openaiAttributeDefaultConvert(llmOpenaiAttribute);
    setOpenaiAttribute(newOpenaiAttribute);
    openaiAttributeFormRef.current?.formApi.setValues(newOpenaiAttribute);
  };

  const openaiAttributeChange = (
    llmOpenaiAttribute: OpenAIAttribute,
    field: any
  ) => {
    if (Object.keys(field).length > 1) {
      return;
    }
    setOpenaiAttribute(llmOpenaiAttribute);
    storage.setItem("llm_openai_attribute", llmOpenaiAttribute);
  };

  return (
    <div className="h-full relative flex flex-col">
      <div className="text-nowrap font-extrabold pl-4  text-lg mb-2 flex  items-center justify-between">
        <div>模型管理</div>
        <div className="flex gap-4 items-center">
          <Button theme="solid" type="primary" onClick={setTrue}>
            添加模型
          </Button>
          <div className="w-[300px]">
            <Input prefix={<IconSearch />} className="w-[300px] " showClear />
          </div>
        </div>
      </div>
      <div className="flex  rounded-2xl  p-1  flex-1 gap-2">
        <div className="h-full w-full">
          <List
            size="small"
            className="h-full"
            dataSource={[{}, {}]}
            grid={{
              gutter: [12, 12],
              xs: 24,
              sm: 24,
              md: 12,
              lg: 12,
              xl: 8,
              xxl: 6,
            }}
            renderItem={(item) => (
              <List.Item style={{ padding: "0px 0px 10px 0px" }}>
                <Card
                  className="w-full"
                  // shadows="hover"
                  bodyStyle={{ padding: "20px 20px 0px 20px" }}
                  footer={
                    <div className="flex justify-end items-center">
                      {/* <div>
                        <Typography.Text type="secondary">
                          @字节跳动
                        </Typography.Text>
                        <Divider layout="vertical" margin="12px" />
                        <Typography.Text type="secondary">
                          上下文：12K
                        </Typography.Text>
                        <Divider layout="vertical" margin="12px" />
                        <Typography.Text type="secondary">
                          2025-01-16 更新
                        </Typography.Text>
                      </div> */}
                      <Space>
                        <Button type="tertiary" theme="light">
                          查看详情
                        </Button>
                        <Button theme="solid" type="primary">
                          开始使用
                        </Button>
                      </Space>
                    </div>
                  }
                >
                  <Meta
                    title="DeepSeek R1"
                    description={
                      <Space>
                        <Tag size="small" color="light-blue">
                          文本模型
                        </Tag>
                        <Tag size="small" color="cyan">
                          高速模型
                        </Tag>
                      </Space>
                    }
                    avatar={
                      <Avatar
                        shape="square"
                        alt="Card meta img"
                        size="default"
                        src="https://lf-coze-web-cdn.coze.cn/obj/coze-web-cn/MODEL_ICON/deepseek_v2.png"
                      ></Avatar>
                    }
                  />
                  <div className="text-semi-color-text-2 w-full px-1 py-1 theme='borderless' text-sm">
                    <Typography.Text
                      type="secondary"
                      ellipsis={{
                        rows: 2,
                        showTooltip: {
                          opts: {
                            content:
                              "  DeepSeek-R1-Distill-Qwen-7B,能够应用于更复杂、更广泛的视觉问答任务。",
                          },
                        },
                      }}
                    >
                      DeepSeek-R1-Distill-Qwen-7B,能够应用于更复杂、更广泛的视觉问答任务。
                    </Typography.Text>
                  </div>
                  {/* <div className="mt-2">
                    DeepSeek-R1-Distill-Qwen-7B,能够应用于更复杂、更广泛的视觉问答任务。
                  </div> */}
                </Card>
              </List.Item>
            )}
          />
        </div>
        {/* <div className="h-full flex flex-col gap-2 relative">
          <div className="flex-1">
            <Card
              className="w-[300px]  h-full"
              bodyStyle={{ padding: "10px" }}
              bordered={false}
            >
              <Collapse>
                <Collapse.Panel
                  style={{ borderBottom: "none" }}
                  header="模型类型"
                  itemKey="1"
                >
                  <RadioGroup
                    direction="vertical"
                    name="demo-radio-group-vertical"
                  >
                    <Radio value={2}>文本模型</Radio>
                    <Radio value={3}>图像模型</Radio>
                    <Radio value={4}>语音模型</Radio>
                    <Radio value={1}>向量模型</Radio>
                  </RadioGroup>
                </Collapse.Panel>
              </Collapse>
            </Card>
          </div>
        </div> */}
      </div>
      <ModelForm open={formOpen} onCancel={setFalse} />
    </div>
  );
}

export default ModelIndex;
