import { create } from "zustand";
import { createUserSlice, UserSliceState } from "./userStore";
import { createThemeSlice, ThemeSliceState } from "./themeStore";
import { createDictionarySlice, DictionaryState, useDictionaryStore } from "./useDictionaryStore";

interface GlobalStore extends UserSliceState, ThemeSliceState,DictionaryState {}

const useGlobalStore = create<GlobalStore>()((set, get) => ({
  ...createUserSlice(set, get),
  ...createThemeSlice(set, get),
  ...useDictionaryStore.getState(),
}));

export default useGlobalStore;