import AppList from "@/layout/components/AppList";
import SiderAvatar from "@/layout/components/SiderAvatar";
import SiderTeamProject from "@/layout/components/SiderTeamProject";
import Icon, {
  IconAppCenter,
  IconArrowLeft,
  IconBell,
  IconBox,
  IconChevronLeft,
  IconChevronRight,
  IconChevronUpDown,
  IconCustomerSupport,
  IconExit,
  IconInfoCircle,
  IconSearch,
  IconUser,
  IconUserGroup,
} from "@douyinfe/semi-icons";
import {
  IllustrationSuccess,
  IllustrationSuccessDark,
} from "@douyinfe/semi-illustrations";
import {
  Avatar,
  Button,
  Divider,
  Dropdown,
  DropdownItem,
  Empty,
  Input,
  List,
  Popover,
  Tooltip,
} from "@douyinfe/semi-ui";
import Header from "@douyinfe/semi-ui/lib/es/navigation/Header";
import { useNavigate, useRouter } from "@tanstack/react-router";
import React from "react";
import AvatarMenu from "./AvatarMenu";
import Search from "./Search";
import { ArrowLeft, ChevronLeft, Monitor } from "lucide-react";
import Logo from "./logo";

const LayoutHeader: React.FC<any> = ({
  enabledBack = false,
  showSystem = true,
  backText = "返回",
  showUserName = false,
}) => {
  const navigate = useNavigate();
  const goHome = () => {
    navigate({
      to: "/",
    });
  };
  const toSystem = () => {
    navigate({
      to: "/system/org/user",
    });
  };
  return (
    <div className="h-[60px] border-b z-[99] border-solid border-semi-color-border px-[24px] flex justify-between items-center">
      {!enabledBack && <Logo onClick={goHome} />}
      {enabledBack && (
        <Tooltip position="bottom" content="返回">
          <div className="flex items-center gap-1">
            <Button
              onClick={goHome}
              theme="borderless"
              type="tertiary"
              icon={<Icon svg={<ChevronLeft size="1.5em" />} />}
            ></Button>
            <span className="text-md text-black">{backText}</span>
          </div>
        </Tooltip>
      )}
      <div className="flex items-center gap-2">
        {showSystem && (
          <Tooltip content="系统管理" position="bottom">
            <div
              onClick={(e) => {
                e.stopPropagation();
                toSystem();
              }}
              className="flex w-[34px] h-[34px] items-center justify-center hover:bg-semi-color-fill-0 cursor-pointer rounded-md"
            >
              <Icon svg={<Monitor size="1em" />} />
            </div>
          </Tooltip>
        )}
        <Search />
        <AvatarMenu showName position="bottom" />
      </div>
    </div>
  );
};

export default LayoutHeader;
