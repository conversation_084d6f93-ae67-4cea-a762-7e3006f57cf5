import React from "react";
import { createRoot } from "react-dom/client";
// import { BrowserRouter } from "react-router-dom";
import Index from "./App";

// 应用样式
import "./markdown.css";
// import "./markdown-dark.css";
import "katex/dist/katex.min.css";

// 主题样式
import "./styles.scss";
// import "./preflight.css";

import App from "./App";

// 启动主题
const themeQuery = window.matchMedia("(prefers-color-scheme: dark)");
document.body.setAttribute("theme-mode", themeQuery.matches ? "dark" : "light");

// import { PersistGate } from "redux-persist/integration/react";
// import { store, persistor } from "@/store";
// 渲染根节点
createRoot(document.getElementById("root") as HTMLElement).render(
  // <Provider store={store}>
  // <PersistGate persistor={persistor}>
  <App />
  // </PersistGate>
  // </Provider>
);
