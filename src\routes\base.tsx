import {
  createRootRoute,
  createRoute,
  Outlet,
  redirect,
} from "@tanstack/react-router";
import React from "react";
import Profile from "@/pages/profile";
import HomeLayout from "@/layout/home";
import AppDetails from "@/pages/platform/app/app-details";
import { useAuthStore, useGlobalStore, useLoadingStore } from "@/store";
import { Toast } from "@douyinfe/semi-ui";
import { IconSpin } from "@douyinfe/semi-icons";
import PlateFormLayout from "@/layout/platform";
import DefaultLayout from "@/layout/defaultLayout";
//创建跟路由
const rootRoute = createRootRoute({
  component: () => {
    return (
      <React.Fragment>
        <Outlet />
      </React.Fragment>
    );
  },
});

const appDetailRoute = createRoute({
  getParentRoute: () => authNoLayoutRoute,
  path: "/platform/app/detail/$id",
  component: () => <AppDetails />,
});

const authRoute = createRoute({
  getParentRoute: () => rootRoute,
  id: "authRoute",
  beforeLoad: async ({ context, location }) => {
    // const { showLoading, hideLoading } = useLoadingStore();
    const { isAuthenticated } = useAuthStore.getState();
    // @ts-ignore
    if (!isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
    const { userInfo, fetchUserInfo } = useGlobalStore.getState();

    if (!userInfo) {
      const toastId = Toast.success({
        content: "正在加载....",
        icon: <IconSpin spin />,
        duration: 0,
        showClose: false,
      });
      await fetchUserInfo(); // 调用 slice 中定义的方法
      Toast.close(toastId);
    }
  },
  component: () => {
    return (
      <DefaultLayout>
        <Outlet />
      </DefaultLayout>
    );
  },
});

const platformRoute = createRoute({
  getParentRoute: () => rootRoute,
  id: "platformFormRoute",
  beforeLoad: async ({ context, location }) => {
    // const { showLoading, hideLoading } = useLoadingStore();
    const { isAuthenticated } = useAuthStore.getState();
    // @ts-ignore
    if (!isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
    const { userInfo, fetchUserInfo } = useGlobalStore.getState();

    if (!userInfo) {
      const toastId = Toast.success({
        content: "正在加载....",
        icon: <IconSpin spin />,
        duration: 0,
        showClose: false,
      });
      await fetchUserInfo(); // 调用 slice 中定义的方法
      Toast.close(toastId);
    }
  },
  component: () => {
    return (
      <PlateFormLayout>
        <Outlet />
      </PlateFormLayout>
    );
  },
})

const authNoLayoutRoute = createRoute({
  getParentRoute: () => rootRoute,
  id: "authNoLayoutRoute",
  beforeLoad: async ({ context, location }) => {
    // const { showLoading, hideLoading } = useLoadingStore();
    const { isAuthenticated } = useAuthStore.getState();
    // @ts-ignore
    if (!isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
    const { userInfo, fetchUserInfo } = useGlobalStore.getState();
    if (!userInfo) {
      await fetchUserInfo(); // 调用 slice 中定义的方法
    }
  },
  component: () => {
    return <Outlet />;
  },
});

const homeLayoutRoute = createRoute({
  getParentRoute: () => rootRoute,
  id: "HomeLayoutRoute",
  beforeLoad: async ({ context, location }) => {
    // const { showLoading, hideLoading } = useLoadingStore();
    const { isAuthenticated } = useAuthStore.getState();
    // @ts-ignore
    if (!isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirect: location.href,
        },
      });
    }
    const { userInfo, fetchUserInfo } = useGlobalStore.getState();
    if (!userInfo) {
      await fetchUserInfo(); // 调用 slice 中定义的方法
    }
  },
  component: () => {
    return <HomeLayout>
      <Outlet />
    </HomeLayout>;
  },
});

export {
  rootRoute,
  authRoute,
  platformRoute,
  homeLayoutRoute,
  authNoLayoutRoute,
  appDetailRoute,
};
