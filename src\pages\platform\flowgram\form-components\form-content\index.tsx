import React from "react";

import { FlowNodeRegistry } from "@flowgram.ai/free-layout-editor";

import { useIsSidebar, useNodeRenderContext } from "../../hooks";
import { FormTitleDescription, FormWrapper } from "./styles";
import { Collapse } from "@douyinfe/semi-ui";

/**
 * @param props
 * @constructor
 */
export function FormContent(props: { children?: React.ReactNode }) {
  const { node, expanded } = useNodeRenderContext();
  const isSidebar = useIsSidebar();
  const registry = node.getNodeRegistry<FlowNodeRegistry>();
  return (
    <FormWrapper>
      {expanded ? (
        <>
          {/* {isSidebar && (
            <FormTitleDescription>
              {registry.info?.description}
            </FormTitleDescription>
          )} */}
          {/* {isSidebar ? (
            <Collapse>
              <Collapse.Panel header="输入" itemKey="1" extra="1234">
                {props.children}
              </Collapse.Panel>
            </Collapse>
          ) : (
            props.children
          )} */}
          {props.children}
        </>
      ) : undefined}
      {/* 123 */}
    </FormWrapper>
  );
}
