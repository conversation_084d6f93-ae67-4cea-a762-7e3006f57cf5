import { useNavigate } from "@tanstack/react-router";
import AppListIcon from "@/components/icon/AppList";
import { Button, Icon, Popover } from "@douyinfe/semi-ui";
import { Bo<PERSON>, LayoutGrid, MonitorDot } from "lucide-react";
const AppList = () => {
  const navigate = useNavigate();
  return (
    <Popover
      content={
        <div className="w-[200px] flex flex-col justify-between  min-h-[100px] p-2">
          <div className=" cursor-pointer mb-2">
            <div
              className="py-2 px-2 flex items-center gap-2 text-semi-color-text-0 rounded-md hover:bg-semi-color-fill-0"
              onClick={() => {
                navigate({
                  to: "/home",
                });
              }}
            >
              <Icon svg={<Bot />} className="text-semi-color-text-0" />
              <span>AIGC</span>
            </div>
            {/* <div
              className="py-2 px-2 flex items-center gap-2 text-semi-color-text-0 rounded-md hover:bg-semi-color-fill-0"
              onClick={() => {
                navigate({
                  to: "/system/dictionary",
                });
              }}
            > <Icon svg={<MonitorDot />} className="text-semi-color-text-0" />
              <span>后台管理</span>
            </div> */}
          </div>
        </div>
      }
      trigger="click"
    >
      <Button
        icon={<Icon className="text-semi-color-text-1" svg={<LayoutGrid size="1.2em" />} />}
        theme="borderless"
        type="tertiary"
      />
    </Popover>
  );
};

export default AppList;
