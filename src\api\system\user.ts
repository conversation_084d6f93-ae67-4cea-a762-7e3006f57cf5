import { http } from "@/utils/axios";

const base_url = "system/user";

// export function getDeptTree() {
//   return http.request<any>({
//     url: `${base_url}/deptTree`,
//     method: "GET",
//   });
// }

export function getUserList(params: any) {
  return http.request<any>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}
export function getUserInfo() {
  return http.request<API.Result>({
    url: "system/user/getInfo",
    method: "GET",
  });
}

export function getDeptTree() {
  return http.request<any>({
    url: `${base_url}/deptTree`,
    method: "GET",
  });
}

export function getUser(id?: number) {
  if (!id) {
    return http.request<any>({
      url: `${base_url}/`,
      method: "GET",
    });
  }
  return http.request<any>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}

export function addUser(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}
export function editUser(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}

export function authRole(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}/authRole`,
    method: "PUT",
    data,
  });
}

export function resetPwd(data: any, type: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}/resetPwd/${type}`,
    method: "PUT",
    data,
  });
}

export function delUser(id: string | number) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${id}`,
    method: "DELETE",
  });
}
