import React, { useState, useEffect, useRef } from 'react';
import { MdEditor } from 'md-editor-rt';
import { Hash, List, Quote, Code, Bold, Italic, Link, Image, Table } from 'lucide-react';
import 'md-editor-rt/lib/style.css';

interface SlashCommand {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  shortcut?: string;
  insertText: string;
}

interface CustomMdEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  maxHeight?: number;
}

const CustomMdEditor: React.FC<CustomMdEditorProps> = ({
  value = '',
  onChange,
  placeholder = '输入您的提示词...',
  className = '',
  minHeight = 300,
  maxHeight = 600,
}) => {
  const [content, setContent] = useState(value);
  const [showSlashMenu, setShowSlashMenu] = useState(false);
  const [slashMenuPosition, setSlashMenuPosition] = useState({ x: 0, y: 0 });
  const [selectedCommandIndex, setSelectedCommandIndex] = useState(0);
  const [slashStartPos, setSlashStartPos] = useState(0);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    setContent(value);
  }, [value]);

  // 斜杠命令配置
  const slashCommands: SlashCommand[] = [
    {
      id: 'heading1',
      title: '一级标题',
      description: '创建一级标题',
      icon: <Hash size={16} />,
      shortcut: 'H1',
      insertText: '# '
    },
    {
      id: 'heading2',
      title: '二级标题',
      description: '创建二级标题',
      icon: <Hash size={16} />,
      shortcut: 'H2',
      insertText: '## '
    },
    {
      id: 'bold',
      title: '粗体',
      description: '加粗文本',
      icon: <Bold size={16} />,
      shortcut: '⌘B',
      insertText: '**粗体文本**'
    },
    {
      id: 'italic',
      title: '斜体',
      description: '倾斜文本',
      icon: <Italic size={16} />,
      shortcut: '⌘I',
      insertText: '*斜体文本*'
    },
    {
      id: 'code',
      title: '代码',
      description: '行内代码',
      icon: <Code size={16} />,
      shortcut: '⌘E',
      insertText: '`代码`'
    },
    {
      id: 'quote',
      title: '引用',
      description: '创建引用块',
      icon: <Quote size={16} />,
      shortcut: '>',
      insertText: '> '
    },
    {
      id: 'list',
      title: '无序列表',
      description: '创建无序列表',
      icon: <List size={16} />,
      shortcut: '-',
      insertText: '- '
    },
    {
      id: 'variable',
      title: '变量',
      description: '插入变量占位符',
      icon: <span style={{ fontSize: '14px', fontWeight: 'bold' }}>{{}}</span>,
      shortcut: '{{}}',
      insertText: '{{变量名}}'
    }
  ];

  // 处理内容变化
  const handleChange = (newValue: string) => {
    setContent(newValue);
    onChange?.(newValue);

    // 检查是否输入了斜杠
    const lastChar = newValue[newValue.length - 1];
    const prevChar = newValue[newValue.length - 2];

    // 检查是否在行首或空格后输入斜杠
    if (lastChar === '/' && (!prevChar || prevChar === '\n' || prevChar === ' ')) {
      setSlashStartPos(newValue.length - 1);
      setShowSlashMenu(true);
      setSelectedCommandIndex(0);
      // 设置菜单位置（简化版本）
      setSlashMenuPosition({ x: 100, y: 100 });
    } else if (showSlashMenu && (newValue.length < content.length || lastChar === ' ' || lastChar === '\n')) {
      // 如果删除了字符或输入了空格/换行，隐藏菜单
      setShowSlashMenu(false);
    }
  };

  // 处理键盘事件
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!showSlashMenu) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedCommandIndex(prev =>
            prev < slashCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedCommandIndex(prev =>
            prev > 0 ? prev - 1 : slashCommands.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          const selectedCommand = slashCommands[selectedCommandIndex];
          insertCommand(selectedCommand);
          break;
        case 'Escape':
          event.preventDefault();
          setShowSlashMenu(false);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showSlashMenu, selectedCommandIndex, slashCommands]);

  // 插入命令
  const insertCommand = (command: SlashCommand) => {
    const newContent = content.slice(0, slashStartPos) + command.insertText + content.slice(slashStartPos + 1);
    setContent(newContent);
    onChange?.(newContent);
    setShowSlashMenu(false);
  };

  // 处理命令点击
  const handleCommandClick = (command: SlashCommand) => {
    insertCommand(command);
  };

  return (
    <div className={`border w-full border-gray-200 rounded-lg overflow-hidden shadow-sm ${className}`} style={{ position: 'relative' }}>
      <MdEditor
        ref={editorRef}
        value={content}
        className='prompt-editor'
        onChange={handleChange}
        placeholder={placeholder}
        preview={false}
        toolbars={[]}
        footers={[]}
        showCodeRowNumber={false}
        style={{
          minHeight: `${minHeight}px`,
          maxHeight: `${maxHeight}px`,
        }}
        theme="light"
        previewTheme="default"
        codeTheme="atom"
      />

      {/* 斜杠命令下拉菜单 */}
      {showSlashMenu && (
        <div
          className="slash-command-dropdown"
          style={{
            left: slashMenuPosition.x,
            top: slashMenuPosition.y,
          }}
        >
          {slashCommands.map((command, index) => (
            <div
              key={command.id}
              className={`slash-command-item ${index === selectedCommandIndex ? 'selected' : ''}`}
              onClick={() => handleCommandClick(command)}
            >
              <div className="command-icon">
                {command.icon}
              </div>
              <div className="command-content">
                <div className="command-title">{command.title}</div>
                <div className="command-description">{command.description}</div>
              </div>
              {command.shortcut && (
                <div className="command-shortcut">{command.shortcut}</div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 自定义状态栏 */}
      <div className="flex items-center justify-between px-3 py-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-500">
        <div>
          {content.length} 字符 · {content.split('\n').length} 行
        </div>
        <div>
          自定义提示词编辑器 · 输入 / 显示命令菜单
        </div>
      </div>
    </div>
  );
};

export default CustomMdEditor;
