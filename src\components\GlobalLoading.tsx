import React from "react";
import { motion } from "framer-motion";
import { useLoadingStore } from "@/store";

export const GlobalLoading: React.FC = () => {
    const { isLoading } = useLoadingStore();

    console.log("GlobalLoading 状态:", isLoading); // 👈 调试日志

    if (!isLoading) return null;

    return (
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed top-0 left-0 right-0 bottom-0 z-[9999] flex items-center justify-center bg-white bg-opacity-80 backdrop-blur-sm"
        >
            <div className="flex flex-col items-center">
                <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-12 h-12 border-4 border-blue-200 border-t-blue-500 rounded-full"
                />
                <p className="mt-4 text-gray-700">正在进入系统...</p>
            </div>
        </motion.div>
    );
};