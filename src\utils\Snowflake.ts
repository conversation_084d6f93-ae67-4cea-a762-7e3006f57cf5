class Snowflake {
  private static EPOCH = 1746007407108n; // 2021-01-01T00:00:00Z in bigint

  private static SEQUENCE_BITS = 12n;
  private static MACHINE_ID_BITS = 10n;
  private static TIMESTAMP_BITS = 41n;

  private static MAX_SEQUENCE = ~(-1n << Snowflake.SEQUENCE_BITS); // 4095
  private static MAX_MACHINE_ID = ~(-1n << Snowflake.MACHINE_ID_BITS); // 1023

  private static TIMESTAMP_LEFT_SHIFT = Snowflake.SEQUENCE_BITS;
  private static MACHINE_ID_LEFT_SHIFT = Snowflake.SEQUENCE_BITS + Snowflake.TIMESTAMP_BITS;

  private machineId: bigint;
  private sequence = 0n;
  private lastTimestamp = -1n;

  constructor(machineId: number) {
    const mid = BigInt(machineId);
    if (mid < 0n || mid > Snowflake.MAX_MACHINE_ID) {
      throw new Error(`Machine ID must be between 0 and ${Snowflake.MAX_MACHINE_ID}`);
    }
    this.machineId = mid;
  }

  private tilNextMillis(lastTimestamp: bigint): bigint {
    let timestamp = this.timeGen();
    while (timestamp <= lastTimestamp) {
      timestamp = this.timeGen();
    }
    return timestamp;
  }

  private timeGen(): bigint {
    return BigInt(Date.now());
  }

  public nextId(): string {
    let timestamp = this.timeGen();

    if (timestamp < this.lastTimestamp) {
      throw new Error(`Clock moved backwards. Refusing to generate id for ${this.lastTimestamp - timestamp} milliseconds`);
    }

    if (this.lastTimestamp === timestamp) {
      this.sequence = (this.sequence + 1n) & Snowflake.MAX_SEQUENCE;
      if (this.sequence === 0n) {
        timestamp = this.tilNextMillis(this.lastTimestamp);
      }
    } else {
      this.sequence = 0n;
    }

    this.lastTimestamp = timestamp;

    const shiftedTimestamp = (timestamp - Snowflake.EPOCH) << Snowflake.TIMESTAMP_LEFT_SHIFT;
    const shiftedMachineId = this.machineId << Snowflake.MACHINE_ID_LEFT_SHIFT;
    const id = shiftedTimestamp | shiftedMachineId | this.sequence;

    return id.toString();
  }
}

// 示例用法
const snowflake = new Snowflake(1); // 机器ID为1
export default snowflake;