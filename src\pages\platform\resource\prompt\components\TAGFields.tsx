import { Row, Col, Form } from "@douyinfe/semi-ui";
import React from "react";

const TAGFields = () => {
  return (
    <Row>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.task"
          label="任务"
          rows={6}
          placeholder="定义任务"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.action"
          label="行动"
          rows={6}
          placeholder="定义行动步骤"
          trigger="blur"
        />
      </Col>
      <Col span={24}>
        <Form.TextArea
          field="rowContent.goal"
          label="目标"
          rows={6}
          placeholder="定义最终目标"
          trigger="blur"
        />
      </Col>
    </Row>
  );
};

export default TAGFields;