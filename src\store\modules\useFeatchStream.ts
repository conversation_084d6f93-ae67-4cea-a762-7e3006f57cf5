import { chatStream } from "@/api/platform/chat";
import { deepClone } from "@/utils";
import { MessageUtil } from "@/utils/message-util";
import snowflake from "@/utils/Snowflake";
import { Toast } from "@douyinfe/semi-ui";
import { nanoid } from "nanoid";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { useShallow } from "zustand/react/shallow";

interface Message {
  conversationId: string | number;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
  name?: string;
  id: string;
  role: string;
  status: "loading" | "incomplete" | "complete" | "error";
  content: string;
  createAt?: number;
  botId?: string; // Add botId to track which bot the message belongs to
}

interface BotConversation {
  messages: Record<string, Message>;
  messageIds: string[];
  currentMessageId: string | null;
  responding: boolean;
  conversationId: string; // 每个bot只有一个conversationId
}

type Store = {
  conversations: Record<string, BotConversation>; // Group conversations by botId
  activeBotId: string | null; // Track current active bot

  // Actions
  setActiveBotId: (botId: string) => void;
  appendMessage: (stream: Message, botId: string) => void;
  changeCurrentMessageId: (currentMessageId: string, botId: string) => void;
  updateMessage: (stream: Message, botId: string) => void;
  updateMessages: (messages: Message[], botId: string) => void;
  deleteMessage: (id: string, botId: string) => void;
  changeResponding: (responding: boolean, botId: string) => void;
  clearMessages: (botId: string) => void;
  addSystemMessage: (content: string, botId: string) => void;
  updateSystemMessage: (id: string, content: string, botId: string) => void;
  clearSystemMessages: (botId: string) => void;
};

// Helper to create a new conversation
const createNewConversation = (): BotConversation => ({
  messages: {},
  messageIds: [],
  currentMessageId: null,
  responding: false,
  conversationId: snowflake.nextId(), // 生成唯一ID
});

export const useFeatchStream = create<Store>()(
  persist(
    (set, get) => ({
      conversations: {},
      activeBotId: null,

      setActiveBotId: (botId: string) => {
        set({ activeBotId: botId });
        // Initialize conversation for this bot if it doesn't exist
        if (!get().conversations[botId]) {
          set((state) => ({
            conversations: {
              ...state.conversations,
              [botId]: createNewConversation(),
            },
          }));
        }
      },

      addSystemMessage: (content: string, botId: string) => {
        const id = nanoid();
        const newMessage = {
          id,
          role: "system" as const,
          content,
          conversationId: snowflake.nextId(),
          createAt: Date.now(),
          status: "complete" as const,
          botId,
        };

        set((state) => {
          const botConversation =
            state.conversations[botId] || createNewConversation();
          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messages: { ...botConversation.messages, [id]: newMessage },
                messageIds: [...botConversation.messageIds, id],
              },
            },
          };
        });
      },

      changeCurrentMessageId: (currentMessageId: string, botId: string) => {
        set((state) => {
          const botConversation =
            state.conversations[botId] || createNewConversation();
          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                currentMessageId,
              },
            },
          };
        });
      },

      updateSystemMessage: (id: string, content: string, botId: string) => {
        set((state) => {
          const botConversation = state.conversations[botId];
          if (!botConversation) return state;

          const message = botConversation.messages[id];
          if (!message || message.role !== "system") return state;

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messages: {
                  ...botConversation.messages,
                  [id]: { ...message, content },
                },
              },
            },
          };
        });
      },

      changeResponding: (responding: boolean, botId: string) => {
        set((state) => {
          const botConversation =
            state.conversations[botId] || createNewConversation();
          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                responding,
              },
            },
          };
        });
      },

      clearSystemMessages: (botId: string) => {
        set((state) => {
          const botConversation = state.conversations[botId];
          if (!botConversation) return state;

          const newMessages = Object.values(botConversation.messages).reduce(
            (acc, msg) => {
              if (msg.role !== "system") {
                acc[msg.id] = msg;
              }
              return acc;
            },
            {} as Record<string, Message>
          );

          const messageIds = Object.keys(newMessages);

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messages: newMessages,
                messageIds,
              },
            },
          };
        });
      },

      appendMessage: (stream, botId: string) => {
        const messageWithBotId = { ...stream, botId };
        set((state) => {
          const botConversation =
            state.conversations[botId] || createNewConversation();
          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messageIds: [...botConversation.messageIds, stream.id],
                messages: {
                  ...botConversation.messages,
                  [stream.id]: messageWithBotId,
                },
              },
            },
          };
        });
      },

      updateMessage: (stream, botId: string) => {
        const messageWithBotId = { ...stream, botId };
        set((state) => {
          const botConversation = state.conversations[botId];
          if (!botConversation) return state;

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messages: {
                  ...botConversation.messages,
                  [stream.id]: messageWithBotId,
                },
              },
            },
          };
        });
      },

      updateMessages: (messagesList, botId: string) => {
        set((state) => {
          const botConversation = state.conversations[botId];
          if (!botConversation) return state;

          const newMessages = { ...botConversation.messages };
          messagesList.forEach((m) => {
            newMessages[m.id] = { ...m, botId };
          });

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messages: newMessages,
              },
            },
          };
        });
      },

      deleteMessage: (id, botId: string) => {
        set((state) => {
          const botConversation = state.conversations[botId];
          if (!botConversation) return state;

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                ...botConversation,
                messageIds: botConversation.messageIds.filter(
                  (msgId) => msgId !== id
                ),
                messages: Object.fromEntries(
                  Object.entries(botConversation.messages).filter(
                    ([msgId]) => msgId !== id
                  )
                ),
              },
            },
          };
        });
      },

      clearMessages: (botId: string) => {
        set((state) => {
          const existingConversation = state.conversations[botId];
          if (!existingConversation) return state;

          // 保留原来的conversationId
          const conversationId = existingConversation.conversationId;

          return {
            conversations: {
              ...state.conversations,
              [botId]: {
                messages: {}, // 清空消息
                messageIds: [], // 清空消息ID列表
                currentMessageId: null,
                responding: false,
                conversationId, // 保留原来的conversationId
              },
            },
          };
        });
      },
    }),
    {
      name: "chat-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Helper functions for the current active bot
function getActiveBotConversation() {
  const state = useFeatchStream.getState();
  const botId = state.activeBotId;
  if (!botId) return null;

  return state.conversations[botId] || null;
}

// 获取或创建会话ID
function getOrCreateConversationId(botId: string): string {
  const state = useFeatchStream.getState();

  // 如果bot会话不存在，先创建一个
  if (!state.conversations[botId]) {
    useFeatchStream.getState().setActiveBotId(botId);
  }

  // 现在bot会话一定存在，返回其conversationId
  return state.conversations[botId].conversationId;
}

export async function sendMessage(
  content: string,
  botId: string,
  abortSignal?: AbortSignal
) {
  // Set active bot
  useFeatchStream.getState().setActiveBotId(botId);

  const botConversation = useFeatchStream.getState().conversations[botId];
  if (botConversation?.responding) {
    Toast.warning("当前正在回答，请等待完成后再发送新消息");
    return;
  }

  // 获取或创建会话ID
  const conversationId = getOrCreateConversationId(botId);

  if (content != null) {
    appendMessage(
      {
        conversationId,
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0,
        id: nanoid(),
        role: "user",
        status: "complete",
        name: "user",
        content: content,
      },
      botId
    );
  }

  const assistantMessageId = nanoid();
  appendMessage(
    {
      conversationId,
      id: assistantMessageId,
      role: "assistant",
      name: "assistant",
      content: "",
      status: "loading",
    },
    botId
  );

  const stream = chatStream(
    {
      prompt: content,
      conversationId,
      botId,
    },
    abortSignal
  );

  setResponding(true, botId);
  let messageId: string;

  try {
    let index = 0;
    for await (const event of stream) {
      if (event.type === "error") {
        const errorMessage = event.row.error || "未知错误";
        throw new Error(errorMessage);
      }

      const data = {
        ...event.row.data.message,
        conversationId, // 使用已有的conversationId
        ...event.row.data.metadata,
      };

      console.log(`index ${index}, data: `, data);

      messageId = data.id;

      if (existsMessage(assistantMessageId, botId)) {
        useFeatchStream.getState().deleteMessage(assistantMessageId, botId);
      }

      if (!existsMessage(messageId, botId)) {
        appendMessage(data, botId);
        continue;
      }

      const message = getMessage(messageId, botId);

      if (message && message.status !== "complete") {
        if (data.status === "completed") {
          setResponding(false, botId);
          updateMessage(
            {
              ...message,
              status: "complete",
              content: message.content + data.content,
              completionTokens: data.completionTokens,
              totalTokens: data.totalTokens,
              promptTokens: data.promptTokens,
            },
            botId
          );

          useFeatchStream.getState().changeCurrentMessageId(null, botId);
        } else {
          useFeatchStream.getState().changeCurrentMessageId(messageId, botId);
          const newMessage = mergeMessage(message, data);
          updateMessage(
            {
              ...newMessage,
              content: newMessage.content,
              id: messageId,
              status: "incomplete",
            },
            botId
          );
        }
      }
      index++;
    }
  } catch (error: Error | any) {
    Toast.error(error?.message);
    appendMessage(
      {
        conversationId, // 使用已有的conversationId
        id: assistantMessageId,
        role: "assistant",
        status: "error",
        name: "assistant",
        content: error?.message,
      },
      botId
    );
  } finally {
    setResponding(false, botId);
    useFeatchStream.getState().changeCurrentMessageId(null, botId);
  }
}

export function stopReponding() {
  const state = useFeatchStream.getState();
  const botId = state.activeBotId;
  if (!botId) return;

  const botConversation = state.conversations[botId];
  if (!botConversation) return;

  useFeatchStream.getState().changeResponding(false, botId);

  if (botConversation.currentMessageId) {
    updateMessage(
      {
        ...getMessage(botConversation.currentMessageId, botId),
        status: "complete",
      },
      botId
    );
  }
}

export function mergeMessage(message: Message, event: Message) {
  mergeTextMessage(message, event);
  return deepClone(message);
}

function mergeTextMessage(storeMessage: Message, event: Message) {
  if (event.content) {
    storeMessage.content += event.content;
  } else if (!event.content && !storeMessage.content) {
    return;
  }
}

function appendMessage(message: Message, botId: string) {
  useFeatchStream.getState().appendMessage(message, botId);
}

function setResponding(value: boolean, botId: string) {
  useFeatchStream.getState().changeResponding(value, botId);
}

function existsMessage(id: string, botId: string) {
  const state = useFeatchStream.getState();
  const botConversation = state.conversations[botId];
  return botConversation?.messageIds.includes(id) || false;
}

function getMessage(id: string, botId: string) {
  const state = useFeatchStream.getState();
  const botConversation = state.conversations[botId];
  return botConversation?.messages[id];
}

// Updated hooks to work with the new structure
export const useMessages = (botId?: string) => {
  return useFeatchStream(
    useShallow((state) => {
      const activeBotId = botId || state.activeBotId;
      if (!activeBotId || !state.conversations[activeBotId]) {
        return [] as Message[];
      }

      const botConversation = state.conversations[activeBotId];
      const retMessages = [] as Message[];

      Object.keys(botConversation.messages).forEach((key) => {
        const message = botConversation.messages[key];
        if (message) {
          retMessages.push(message);
        }
      });

      return retMessages;
    })
  );
};

function updateMessage(message: any, botId: string) {
  useFeatchStream.getState().updateMessage(message, botId);
}

export function useMessage(
  messageId: string | null | undefined,
  botId?: string
) {
  return useFeatchStream(
    useShallow((state) => {
      const activeBotId = botId || state.activeBotId;
      if (!messageId || !activeBotId || !state.conversations[activeBotId]) {
        return undefined;
      }

      return state.conversations[activeBotId].messages[messageId];
    })
  );
}

export function useMessageIds(botId?: string) {
  return useFeatchStream(
    useShallow((state) => {
      const activeBotId = botId || state.activeBotId;
      if (!activeBotId || !state.conversations[activeBotId]) {
        return [] as string[];
      }

      return state.conversations[activeBotId].messageIds;
    })
  );
}

export function useResponding(botId?: string) {
  return useFeatchStream(
    useShallow((state) => {
      const activeBotId = botId || state.activeBotId;
      if (!activeBotId || !state.conversations[activeBotId]) {
        return false;
      }

      return state.conversations[activeBotId].responding;
    })
  );
}

// 清空消息并重置conversationId
export function clearMessages(botId: string) {
  const state = useFeatchStream.getState();
  const botConversation = state.conversations[botId];
  if (!botConversation) return;

  // 生成新的conversationId
  const newConversationId = snowflake.nextId();

  // 更新会话，清空消息并设置新的conversationId
  useFeatchStream.setState((state) => ({
    conversations: {
      ...state.conversations,
      [botId]: {
        messages: {},
        messageIds: [],
        currentMessageId: null,
        responding: false,
        conversationId: newConversationId, // 设置新的conversationId
      },
    },
  }));

  // 成功提示
  Toast.success({
    content: "对话已清空",
    duration: 2,
  });
} // 清空消息的辅助函数

// 添加一个获取会话ID的hook
export function useConversationId(botId?: string) {
  return useFeatchStream(
    useShallow((state) => {
      const activeBotId = botId || state.activeBotId;
      if (!activeBotId || !state.conversations[activeBotId]) {
        return undefined;
      }

      return state.conversations[activeBotId].conversationId;
    })
  );
}
