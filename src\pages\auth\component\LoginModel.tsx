import { Avatar, Button, Col, Form, Modal, Row, Toast } from "@douyinfe/semi-ui";
import React from "react";
import { LocalForageService as storage } from "@/utils/storage";

const LoginModel: React.FC<LoginModelProps> = ({ open, onCancel }) => {
  // storage.setItem();
  const handleSubmit = () => {
    storage.setItem("loginUser","true");
    Toast.success("登录成功");
    onCancel();
  };
  return (
    <Modal
      visible={open}
      width={"600px"}
      closeOnEsc={false}
      footer={<></>}
      okText={"保存"}
      title=""
      onCancel={onCancel}
      cancelText={"取消"}
    >
      <div className="w-full flex flex-col gap-2 items-center justify-center">
        <Avatar style={{ background: "#1c7ed6" }} size="large" src="">
          Login
        </Avatar>
        <div className="text-3xl">登录</div>
      </div>
      <div className="w-full items-center flex ">
        <Form className="w-full" onSubmit={values => handleSubmit()}>
          {({ formState, values, formApi }) => (
            <Row>
              <Col span={24}>
                <Form.Input
                  size="large"
                  field="phone"
                  label="用户名"
                  placeholder="请输入用户名"
                  rules={[
                    { required: true, message: '请输入用户名' },
                    { type: 'string', message: '请输入用户名' },
                    { validator: (rule, value) => value === 'Herther', message: '用户名错误' }
                ]}
                ></Form.Input>
              </Col>
              <Col span={24}>
                <Form.Input
                  size="large"
                  field="password"
                  label="密码"
                  placeholder="请输入密码"
                  rules={[
                    { required: true, message: '请输入密码' },
                    { type: 'string', message: '请输入密码' },
                    { validator: (rule, value) => value === '123456', message: '密码错误' }
                ]}
                ></Form.Input>
              </Col>

              <Col span={24}>
                <Form.Checkbox field="agree" noLabel>
                  我已阅读并同意服务条款
                </Form.Checkbox>
              </Col>
              <Col span={24}>
                <Button
                  disabled={!values.agree}
                  size="large"
                  theme="solid"
                  htmlType="submit"
                  block
                  type="primary"
                >
                  登录
                </Button>
              </Col>
            </Row>
          )}
        </Form>
      </div>
      <div className="w-full flex gap-2 mt-5">
        <Button size="large" theme="outline" type="tertiary" block>
          注册
        </Button>
        <Button size="large" theme="outline" type="tertiary" block>
          其他方式登录
        </Button>
      </div>
    </Modal>
  );
};
export default LoginModel;
