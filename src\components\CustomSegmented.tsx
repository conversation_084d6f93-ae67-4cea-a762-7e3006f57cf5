import React, { useState } from "react";

// 定义选项的类型
type Option = {
  label: React.ReactNode;
  value: string | number;
};

// 定义 CustomSegmented 组件的属性类型
interface CustomSegmentedProps {
  options: Option[];
  defaultValue: string | number;
  onChange?: (value: string | number) => void;
}

const CustomSegmented: React.FC<CustomSegmentedProps> = ({
  options,
  defaultValue,
  onChange,
}) => {
  const [selectedValue, setSelectedValue] = useState(defaultValue);

  const handleChange = (value: string | number) => {
    setSelectedValue(value);
    onChange?.(value);
  };

  return (
    <div className="flex border-gray-300 py-1">
      {options.map((option) => (
        <div
          key={option.value}
          className={`px-2 py-1  border-semi-color-primary cursor-pointer focus:outline-none ${
            selectedValue === option.value
              ? "border-blue-500 text-blue-500  font-semibold border rounded-md text-semi-color-primary border-solid"
              : "border-transparent  hover:text-blue-600 hover:text-semi-color-primary"
          }`}
          onClick={() => handleChange(option.value)}
        >
          {option.label}
        </div>
      ))}
    </div>
  );
};

export default CustomSegmented;
