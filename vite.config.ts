import { defineConfig, ConfigEnv, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { createHtmlPlugin } from "vite-plugin-html";
import { wrapperEnv } from "./src/utils/envUtils";
import type { UserConfigExport } from "vite";
// import { TanStackRouterVite } from "@tanstack/router-vite-plugin";
import path from "path";
import semi from "./semi";
console.log("path", path.resolve(__dirname, "./src/styles.scss"));
const config: UserConfigExport = async (mode: ConfigEnv) => {
  const env = loadEnv(mode.mode, process.cwd());
  const viteEnv = wrapperEnv(env);
  return {
    plugins: [
      react(),
      // TanStackRouterVite(),
      createHtmlPlugin({
        inject: {
          data: {
            title: viteEnv.VITE_PAGE_TITLE,
          },
        },
      }),
      semi({
        theme: "@semi-bot/semi-theme-herther-1",
      }),
    ],
    resolve: {
      alias: {
        "~@semi": path.join(__dirname, "./node_modules/@semi-bot"),
        "@assets": path.join(__dirname, "./src/assets"),
        "@": path.join(__dirname, "./src"),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler", // or "modern", "legacy"
        },
      },
    },
    clearScreen: false,
    server: {
      host: "0.0.0.0", // 服务器主机名，如果允许外部访问，可设置为"0.0.0.0"
      port: 1420,
      cors: true,
      strictPort: true,
      proxy: {
        "/api": {
          target: viteEnv.VITE_GLOB_API_URL, // easymock
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    envPrefix: ["VITE_", "TAURI_"],
  };
};

export default defineConfig(config);
