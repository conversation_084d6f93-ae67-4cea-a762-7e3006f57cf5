import { http } from "@/utils/axios";

const base_url = "system/dict/data";
const base_type_url = "system/dict/type";

export function getDicts(dictType: string) {
  return http.request<any>({
    url: `${base_url}/type/${dictType}`,
    method: "GET",
  });
}

export function getDictDataList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}
export function getDictTypeList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_type_url}/list`,
    method: "GET",
    params,
  });
}

export function addDictType(params: any) {
  return http.request<any>({
    url: `${base_type_url}`,
    method: "POST",
    data: params,
  });
}
export function editDictType(params: any) {
  return http.request<any>({
    url: `${base_type_url}`,
    method: "PUT",
    data: params,
  });
}
export function addDictData(params: any) {
  return http.request<any>({
    url: `${base_url}`,
    method: "POST",
    data: params,
  });
}
export function editDictData(params: any) {
  return http.request<any>({
    url: `${base_url}`,
    method: "PUT",
    data: params,
  });
}

export function refreshCache() {
  return http.request<any>({
    url: `${base_type_url}/refreshCache`,
    method: "DELETE",
  });
}

export function delDictData(dictCode: string) {
  return http.request<any>({
    url: `${base_url}/${dictCode}`,
    method: "DELETE",
  });
}
export function delDictType(dictType: string) {
  return http.request<any>({
    url: `${base_type_url}/${dictType}`,
    method: "DELETE",
  });
}

export function getDictData(id: any){
  return http.request<any>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}
export function getDictType(id: any){
  return http.request<any>({
    url: `${base_type_url}/${id}`,
    method: "GET",
  });
}