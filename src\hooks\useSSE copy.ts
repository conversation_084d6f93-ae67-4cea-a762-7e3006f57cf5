import React from "react";
import { fetchEventSource } from "@/components/fetch-event-source"; // Import the new library
import useSyncState from "./useSyncState";
import useEvent from "./useEvent";

export type SSEOptions = {
  method?: "GET"; // SSE only supports GET
  params?: Record<string, string>;
  headers?: Record<string, string>;
  reconnectDelay?: number;
  maxRetries?: number;
  withCredentials?: boolean;
};

export type SSEState = {
  isConnected: boolean;
  messages: any[];
  error: Error | null;
  retryCount: number;
  connectionInfo: {
    lastRequestUrl: string;
    headers: Record<string, string>;
  };
};

type SSEController = {
  connect: (params?: Record<string, string>) => void;
  disconnect: () => void;
  reconnect: () => void;
};

const useSSE = (
  url: string,
  options: SSEOptions = {}
): [SSEState, SSEController] => {
  const {
    method = "POST",
    params = {},
    headers = {},
    reconnectDelay = 5000,
    maxRetries = 3,
    withCredentials = false,
  } = options;

  const [state, setState] = React.useState<SSEState>({
    isConnected: false,
    messages: [],
    error: null,
    retryCount: 0,
    connectionInfo: {
      lastRequestUrl: "",
      headers: { ...headers },
    },
  });
  const isUserDisconnecting = React.useRef(false);

  const sourceRef = React.useRef<ReturnType<typeof fetchEventSource> | null>(
    null
  );
  const reconnectTimerRef = React.useRef<NodeJS.Timeout | null>(null);

  // URL builder with memoization
  const buildUrl = React.useCallback(
    (baseUrl: string, params: Record<string, string>) => {
      const searchParams = new URLSearchParams(params);
      return `${baseUrl}${searchParams.toString() ? `?${searchParams}` : ""}`;
    },
    []
  );

  // Cleanup connection resources
  const cleanupConnection = React.useCallback(() => {
    if (sourceRef.current) {
      // Use the correct method to close the connection
      // @ts-expect-error
      if (typeof sourceRef.current?.close === "function") {
        // @ts-expect-error
        sourceRef.current?.close(); // Use `close` if available
      } else {
        console.warn("No valid method found to close the connection.");
      }
      sourceRef.current = null;
    }
    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current);
      reconnectTimerRef.current = null;
    }
  }, []);

  // Authorization header updater
  const updateAuthHeader = React.useCallback(
    (newHeaders?: Record<string, string>) => {
      const combinedHeaders = {
        ...state.connectionInfo.headers,
        ...(newHeaders || {}),
      };
      setState((prev) => ({
        ...prev,
        connectionInfo: {
          ...prev.connectionInfo,
          headers: combinedHeaders,
        },
      }));
      return combinedHeaders;
    },
    [state.connectionInfo.headers, setState]
  );

  // Event handlers
  const handleOpen = useEvent(async () => {
    console.log("SSE connection opened");
    setState((prev) => ({
      ...prev,
      isConnected: true,
      error: null,
      retryCount: 0,
    }));
  });

  const handleMessage = useEvent((data: unknown) => {
    setState((prev) => {
      const newMessages = [...prev.messages, data];
      return {
        ...prev,
        messages: newMessages,
      };
    });
  });

  const handleError = useEvent((error: Error) => {
    console.error("SSE error:", error);
    const currentState = state;
    const shouldRetry = currentState.retryCount < maxRetries;

    setState((prev) => ({
      ...prev,
      error,
      isConnected: false,
      // Set retryCount to maxRetries + 1 to block further retries
      retryCount: shouldRetry ? prev.retryCount + 1 : maxRetries + 1,
    }));

    if (shouldRetry) {
      reconnectTimerRef.current = setTimeout(
        () => connect(),
        reconnectDelay * Math.pow(2, currentState.retryCount)
      );
    }
  });

  // Connection methods
  const connect = React.useCallback(
    (newParams?: Record<string, string>) => {
      console.log("Connecting to SSE with params:", newParams);
      cleanupConnection();
      const combinedParams = { ...params, ...(newParams || {}) };
      const urlWithParams = buildUrl(url, combinedParams);
      const currentHeaders = updateAuthHeader();

      const fetchOptions: Parameters<typeof fetchEventSource>[1] = {
        method: method as any,
        headers: currentHeaders,
        credentials: withCredentials ? "include" : "omit",
        onopen: handleOpen,
        openWhenHidden: true,
        onmessage: (event: any) => {
          try {
            handleMessage(JSON.parse(event.data));
          } catch (err) {
            handleError(new Error(`Invalid message format: ${event.data}`));
          }
        },
        onerror: (err: Error) => {
          handleError(err);
        },
        onclose: () => {
          setState((prev) => ({
            ...prev,
            isConnected: false,
          }));
          if (isUserDisconnecting.current) {
            return; // Skip error handling during user disconnect
          }
          handleError(new Error("Connection closed by server"));
        },
      };

      try {
        const source = fetchEventSource(urlWithParams, fetchOptions);
        sourceRef.current = source;

        setState((prev) => ({
          ...prev,
          connectionInfo: {
            ...prev.connectionInfo,
            lastRequestUrl: urlWithParams,
          },
        }));
      } catch (error) {
        handleError(
          error instanceof Error ? error : new Error("Connection failed")
        );
      }
    },
    [
      buildUrl,
      cleanupConnection,
      params,
      updateAuthHeader,
      method,
      withCredentials,
      setState,
      handleOpen,
      handleMessage,
      handleError,
    ]
  );

  const disconnect = React.useCallback(() => {
    console.log("Disconnecting from SSE");
    isUserDisconnecting.current = true;
    cleanupConnection();
    setState((prev) => ({
      ...prev,
      isConnected: false,
      retryCount: 0,
    }));
    isUserDisconnecting.current = false;
  }, [cleanupConnection, setState]);

  const reconnect = React.useCallback(() => {
    console.log("Reconnecting to SSE");
    disconnect();
    connect();
  }, [connect, disconnect]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      cleanupConnection();
      disconnect();
    };
  }, [cleanupConnection, disconnect]);

  return [state, { connect, disconnect, reconnect }];
};

export default useSSE;
