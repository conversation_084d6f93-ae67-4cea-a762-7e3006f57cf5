import { getSelecteds } from "@/api/system/dept";
import { addNotice, editNotice, getNotice } from "@/api/system/notice";
import {
  FormToolTextEditor,
  ToolTextEditor,
} from "@/components/TextEditor/ToolTextEditor";
import useDictionary from "@/hooks/useDictionary";
import {
  IconArrowLeft,
  IconChevronLeft,
  IconPlus,
  IconSend,
  IconServerStroked,
} from "@douyinfe/semi-icons";
import {
  Form,
  Row,
  SideSheet,
  Col,
  Button,
  Divider,
  TreeSelect,
  Toast,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import { values } from "@flowgram.ai/free-layout-editor";
import { useRouter } from "@tanstack/react-router";
import classNames from "classnames";
import React, { useEffect, useMemo, useState } from "react";
import dayjs from "dayjs";
export default function NoticeForm({ visible, onCancel }: any) {
  const formApiRef = React.useRef(null);
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_notice_type",
    "sys_filter_type",
    "sys_receive_mode",
  ]);
  const titles = ["新增", "修改"];
  const [filterType, setFilterType] = useState("");
  const [deptData, setDeptData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState(titles[0]);

  useEffect(() => {
    loadDictionary();
  }, []);

  const noticeTypes = useMemo(() => {
    return (
      dictionaryData?.sys_notice_type?.map((item: any) => ({
        label: item.dictLabel,
        value: item.dictValue,
      })) || []
    );
  }, [dictionaryData]);

  const filterTypes = useMemo(() => {
    return (
      dictionaryData?.sys_filter_type?.map((item: any) => ({
        label: item.dictLabel,
        value: item.dictValue,
      })) || []
    );
  }, [dictionaryData]);

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  useEffect(() => {
    getDepts();
  }, []);
  const getDepts = () => {
    getSelecteds().then(({ data }) => {
      console.log(data);

      setDeptData(data);
    });
  };
  const searchParams = useRouter();
  useEffect(() => {
    //
    if (searchParams.state.location.search?.id) {
      setTitle(titles[1]);
      getNotice(searchParams.state.location.search?.id).then(({ data }) => {
        setFilterType(data.filterType);
        // @ts-expect-error
        formApiRef?.current?.setValues({
          ...data,
          //转成数字数组转字符串数组
          receiveMode: data.receiveMode.toString().split(","),
          filterType: data.filterType,
        });
        formApiRef?.current?.setValue("filterCondition", data.filterCondition);
        //设置
      });
    }
  }, [searchParams]);

  // 获取查询参数
  console.log();
  const handleGoBack = () => {
    searchParams.history.go(-1);
  };
  const handleSave = async (values: any) => {
    setLoading(true);
    addNotice(values)
      .then(({ msg }) => {
        Toast.success(msg);
        searchParams.history.go(-1);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleEdit = async (values: any) => {
    setLoading(true);
    editNotice(values)
      .then(({ msg }) => {
        Toast.success(msg);
        searchParams.history.go(-1);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handlePublish = async (status: any) => {
    const values = await formApiRef.current.validate();
    searchParams.state.location.search?.id
      ? handleEdit({
          ...values,
          noticeId: searchParams.state.location.search?.id,
          status,
        })
      : handleSave({
          ...values,
          status,
          deadline: dayjs(values.deadline).format("YYYY-MM-DD hh:mm:ss"),
        });
  };

  return (
    <div className="h-full w-full py-4 px-2  relative">
      <div
        className="gap-4 px-4 py-4 rounded-xl overflow-x-hidden  border-2 border-solid
       bg-semi-color-white  border-semi-color-border flex h-full w-full flex-col  
       box-border shadow-sm"
      >
        <div className="flex rounded-md  flex-row items-center justify-between">
          <div className="flex flex-row items-center gap-3">
            <Button
              onClick={handleGoBack}
              icon={<IconChevronLeft />}
              type="tertiary"
            ></Button>
            <div className="text-semi-color-text-1">通知公告</div>
            <Divider layout="vertical" />
            <div className="font-semibold">{title}</div>
          </div>
          <div className="flex flex-row items-center gap-2">
            <Button
              theme="solid"
              type="tertiary"
              loading={loading}
              onClick={() => handlePublish(2)}
              // contentClassName="bg-semi-color-success"
              // style={{ background: "var(--semi-color-success)" }}
              icon={<IconServerStroked />}
            >
              暂存
            </Button>
            <Button
              theme="solid"
              icon={<IconSend />}
              loading={loading}
              onClick={() => handlePublish(1)}
            >
              发布
            </Button>
          </div>
        </div>
        <div className="w-full overflow-hidden ">
          <Form getFormApi={getFormApi}>
            <div className="grid grid-cols-12 gap-4">
              {/* Announcement Title */}
              <div className="col-span-3">
                <Form.Input
                  field="noticeTitle"
                  rules={[{ required: true, message: "公告标题不能为空" }]}
                  label="公告标题"
                  placeholder={"请输入公告标题"}
                  required
                />
              </div>

              {/* Announcement Type */}
              <div className="col-span-8">
                <Form.Select
                  field="noticeType"
                  showClear
                  optionList={noticeTypes}
                  className="w-[30%]"
                  rules={[{ required: true, message: "公告类型不能为空" }]}
                  label="公告类型"
                  placeholder={"请输入公告类型"}
                />
              </div>

              {/* Notice Scope */}
              <div className="col-span-3">
                <Form.Select
                  className="w-full"
                  field="filterType"
                  optionList={filterTypes}
                  showClear
                  onChange={(value: any) => {
                    setFilterType(value);
                  }}
                  onClear={() => {
                    setFilterType("");
                  }}
                  rules={[{ required: true, message: "通知范围不能为空" }]}
                  label="通知范围"
                  placeholder={"请选择通知范围"}
                />
              </div>

              {/* Target Users */}
              {filterType !== "" && filterType !== "1" && filterType == "5" ? (
                <div className="col-span-3">
                  <Form.Select
                    field="filterCondition"
                    className="w-full"
                    showClear
                    rules={[{ required: true, message: "指定用户不能为空" }]}
                    label="指定用户"
                    placeholder={"请选择指定用户"}
                  />
                </div>
              ) : null}

              {filterType !== "" && filterType !== "1" && filterType == "3" && (
                <div className="col-span-3">
                  <Form.TreeSelect
                    label="指定部门"
                    field="filterCondition"
                    showClear
                    className="w-full"
                    treeData={deptData}
                    dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
                    multiple
                    filterTreeNode
                    showFilteredOnly
                    placeholder="请选择部门"
                    // onSearch={this.onSearch}
                  />
                </div>
              )}

              {/* Notification Methods */}
              <div
                className={classNames(
                  filterType !== "" && filterType == "1"
                    ? "col-span-8"
                    : "col-span-6"
                )}
              >
                <Form.CheckboxGroup
                  field="receiveMode"
                  direction="horizontal"
                  label="通知方式"
                  rules={[{ required: true, message: "请选择通知方式" }]}
                >
                  {dictionaryData?.sys_receive_mode?.map((item: any) => (
                    <Form.Checkbox value={item.dictValue} key={item.dictValue}>
                      {item.dictLabel}
                    </Form.Checkbox>
                  ))}
                </Form.CheckboxGroup>
              </div>

              {/* Expiry Date */}
              <div className="col-span-4">
                <Form.DatePicker
                  label="失效时间"
                  className="w-full"
                  type="dateTime"
                  insetInput
                  format={"yyyy-MM-dd HH:mm"}
                  field="deadline"
                  placeholder="失效时间"
                />
              </div>

              {/* Editor */}
              <div className="col-span-12">
                <FormToolTextEditor
                  field="noticeContent"
                  rules={[{ required: true, message: "公告内容不能为空" }]}
                  label="公告内容"
                  placeholder={"请输入公告内容"}
                  className="border min-h-[390px] max-h-[450px] border-solid border-semi-color-border rounded-md"
                />
              </div>
            </div>
          </Form>
        </div>

        {/* <div className="w-full text-center text-3xl font-semibold">
          {data.noticeTitle}
        </div> */}
      </div>
    </div>
  );
}
