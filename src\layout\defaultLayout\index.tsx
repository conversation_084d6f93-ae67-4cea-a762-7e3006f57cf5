import React, { useEffect, useState } from "react";
import { Avatar, Dropdown, Layout, Nav, Toast } from "@douyinfe/semi-ui";
import {
  IconBox,
  IconChevronUpDown,
  IconHome,
  IconSemiLogo,
  IconSetting,
  IconUndo,
  IconUserGroup,
} from "@douyinfe/semi-icons";
import {
  IconBadge,
  IconConfig,
  IconForm,
  IconTree,
} from "@douyinfe/semi-icons-lab";
import LayoutSider from "./components/Sider";
import LayoutHeader from "../components/Header";
import { useNavigate } from "@tanstack/react-router";
import { getDeepestChild, getTitleFromMenu } from "../utils/navigationUtils";
import { findSystemMenu } from "@/constants/systemMenus";

const DefaultLayout: React.FC<any> = (props) => {
  const navigate = useNavigate();
  const [activeKey, setActiveKey] = useState<string>(""); // Active top-level menu
  const [subMenus, setSubMenus] = useState<MenuProps[]>([]); // Sub-menu items
  const [activePath, setActivePath] = useState<string[]>([]); // Current path state

  return (
    <Layout className="full-height relative bg-gray-50">
      <div className="w-full  h-full  flex flex-col">
        <LayoutHeader enabledBack showSystem={false} showUserName  backText="系统管理"/>
        <div className="w-full flex-1 flex flex-row  overflow-x-hidden overflow-y-auto ">
          <LayoutSider />
          <div className="h-full w-full overflow-x-hidden overflow-y-auto ">
            {props.children}
          </div>
        </div>
      </div>
    </Layout>
  );
};
export default DefaultLayout;
