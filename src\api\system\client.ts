import { http } from "@/utils/axios";

const base_url = "/system/client";

export function getClientList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}
export function getClient(id: number) {
  return http.request<ClientVO>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}

export function addClient(data: ClientVO) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}
export function editClient(data: ClientVO) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}

export function delClient(id: number | string) {
  return http.request<API.Result>({
    url: `${base_url}/${id}`,
    method: "DELETE",
  });
}

export function changeStatus(params: ClientChangeStatus) {
  return http.request<API.Result>({
    url: `${base_url}/changeStatus`,
    method: "PUT",
    data: params,
  });
}
