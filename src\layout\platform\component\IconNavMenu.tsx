import { FC } from "react";
import classNames from "classnames";
import { MessageSquare } from "lucide-react";
import { Tooltip } from "@douyinfe/semi-ui";

interface MenuListItemProps {
    menu: MenuProps;
    activeKey: string;
    onClick: (menu: MenuProps) => void;
}
{/* 图标导航区域 */ }
const MenuListItem: FC<MenuListItemProps> = ({ menu, activeKey, onClick }) => {
    return (
        <Tooltip content={menu.label}>
            <div
                onClick={() => onClick(menu)}
                className={classNames(
                    "flex flex-col items-center justify-center cursor-pointer rounded-md ",
                    "w-[40px] h-[40px] gap-1 text-[12px]",
                    activeKey === menu.key
                        ? "bg-semi-color-fill-0 text-semi-color-black"
                        : "hover:bg-semi-color-fill-0 "
                )}
            >
                {menu.icon}
            </div>
        </Tooltip>
    );
};

export default MenuListItem;
