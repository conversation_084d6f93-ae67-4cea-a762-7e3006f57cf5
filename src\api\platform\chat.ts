import { fetchStream } from "@/utils/sse/fetch-stream";
import { getAccessToken } from "@/utils/auth";
export async function* chatStream(
  {
    prompt,
    conversationId,
    botId,
  }: {
    prompt: string;
    conversationId: string;
    botId: string;
  },
  signal?: AbortSignal
) {
  const url = "/api/aigc/v1/chat/bot/debugger";
  const headers = {
    Clientid: import.meta.env.VITE_GLOB_CLIENT_ID || "",
    Authorization:
      import.meta.env.VITE_GLOB_TOKEN_HEADER + getAccessToken() || "",
  };

  const requestParams = {
    prompt,
    conversationId,
    botId,
  };

  try {
    const stream = fetchStream(url, {
      method: "POST",
      headers: {
        ...headers,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestParams),
    });
    for await (const event of stream) {
      // 直接透传事件类型
      yield event;
    }
  } catch (e: any) {
    // 处理不可恢复的网络错误
    yield {
      type: "error",
      row: {
        error: `Network error: ${e.message}`,
        timestamp: new Date().toISOString(),
      },
    };
  }
}
