import { http } from "@/utils/axios";

const base_url = "aigc/v1/agent";
export function getAppList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function addAgent(data: any) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}

export function getAgent(id: number) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}

export function updateAgent(data: any) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}

export function updateAgentModelConfig(data: any) {
  return http.request<API.Result>({
    url: `${base_url}/model/config`,
    method: "PUT",
    data,
  });
}
