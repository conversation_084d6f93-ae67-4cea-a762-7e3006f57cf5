declare namespace Log {
    export interface LogingLogstatsVO {
        deteFields: Array<string>;
        dimensions?: Array<string>;
        logs: Array<LoginLogstats>;
    }

    export interface OperLogstatsVO {
        deteFields: Array<string>;
        logs: Array<OperLogstats>;
    }

    export interface OperLogstats  {
        day: string;
        error: number;
        success: number;
      };

      export interface OnlineVO extends API.BaseEntity {
            browser: string;
            clientKey: string;
            deptName: string;
            deviceType: string;
            ipaddr: string;
            loginLocation: string;
            loginTime: number;
            os: string;
            tokenId: string;
            userName: string;
      };
  
  }
  