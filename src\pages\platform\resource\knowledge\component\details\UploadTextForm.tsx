import { useAuth } from "@/components/Auth";
import NotionIcon from "@/components/icon/NotionIcon";
import RemoteIcon from "@/components/icon/RemoteIcon";
import { TextIcon } from "@/components/icon/TextIcon";
import { ResultEnum } from "@/enums/httpEnum";
import { getAccessToken } from "@/utils/auth";
import {
  Button,
  Card,
  Checkbox,
  Col,
  Descriptions,
  Divider,
  Empty,
  Form,
  Icon,
  List,
  Modal,
  Progress,
  Row,
  Space,
  Spin,
  Step,
  Steps,
  Table,
  Toast,
  Typography,
  Upload,
} from "@douyinfe/semi-ui";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { addKnowledge } from "@/api/platform/knowledge";
import {
  IconArrowRight,
  IconClose,
  IconShoppingBag,
} from "@douyinfe/semi-icons";
import { IconChangelog } from "@douyinfe/semi-icons-lab";
import TextFile from "@/components/icon/TextFile";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { addDataset, chuckDataset } from "@/api/platform/dataset";
import { MarkdownBox } from "@/components/Markdown/MarkdownBox";
import PdfIcon from "@/components/icon/PdfIcon";
import WordIcon from "@/components/icon/WordIcon";
import MarkdownIcon from "@/components/icon/MarkdownIcon";
import ExcelIcon from "@/components/icon/ExcelIcon";
import HtmlIcon from "@/components/icon/HtmlIcon";
import {
  IllustrationNoResult,
  IllustrationNoResultDark,
} from "@douyinfe/semi-illustrations";
import { PageEnum } from "@/enums/pageEnum";

const UploadTextForm: React.FC<KnowledgeFormProps> = ({
  open,
  onCancel,
  knowledgeId,
  onOK,
}) => {
  const [current, setCurrent] = useState<number>(0);
  const selectDataSourceRef = React.useRef(null);
  const settingFormRef = React.useRef(null);
  const [ossId, setOssId] = React.useState(null);
  const [fileName, setFileName] = React.useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [disabled, setDisabled] = useState(false)

  const handleCancel = () => {
    setCurrent(0);
    onCancel();
    // 重置 SelectDataSource 的表单状态
    selectDataSourceRef.current?.resetForm();
    settingFormRef.current?.resetForm();
  };

  const addFromDatasets = async (values: any) => {
    setCurrent(1);
    setOssId(values.files[0].response?.data?.ossId);
    setFileName(values.files[0].response?.data.fileName);
  };
  const handleAddDatasets = (values: any) => {
    addDataset({
      chuckParams: values,
      ossId,
      origin: "upload",
      knowledgeId: knowledgeId,
    }).then(({ msg }) => {
      setCurrent(current + 1);
      Toast.success(msg);
    });
  };

  const handleOnOk = async () => {
    if (current === 0) {
      setIsLoading(true);
      setDisabled(true)
      try {
        // @ts-ignore
        await selectDataSourceRef?.current.submitForm().then(async (values) => {
          if (values.files[0].status === "success") {
            await addFromDatasets(values);
          } else {
            return;
          }
        });
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        setDisabled(false)
      }
    } else if (current === 1) {
      setIsLoading(true);
      try {
        // @ts-ignore
        await settingFormRef?.current.submitForm().then(async (values) => {
          await handleAddDatasets(values);
        });
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        console.error("Step 1 submit failed:", error);
      }
    } else {
      setIsLoading(false);
      setCurrent(2);
    }
  };

  return (
    <>
      <Modal
        title="新增知识库"
        fullScreen
        className="semi-light-scrollbar"
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
          height: "60%",
        }}
        onOk={handleOnOk}
        visible={open}
        onCancel={handleCancel}
        closeOnEsc={false}
        footer={
          <div>
            <div className="flex justify-end gap-1">
              <Button onClick={handleCancel} type="tertiary">
                取消
              </Button>
              <Button
                theme="light"
                type="tertiary"
                onClick={() => {
                  if (current !== 0) {
                    setCurrent(current - 1);
                  }
                }}
                disabled={current === 2} // 最后一步禁用
              >
                上一步
              </Button>
              <Button
                onClick={handleOnOk}
                theme="solid"
                disabled={false}
                type="primary"
                loading={isLoading}
              >
                {current === 2 ? "完成" : "下一步"}
              </Button>
            </div>
          </div>
        }
        cancelText={"取消"}
      >
        <div className="px-2">
          <Steps type="basic" current={current}>
            <Step title="导入数据源" description="请上传数据源" />
            <Step title="文本分段清洗" description="配置文本分段分片参数" />
            <Step title="处理" description="分片向量化" />
          </Steps>
        </div>

        {current === 0 && (
          <div
            className="flex items-center  mt-2 overflow-auto justify-center"
            style={{ maxHeight: "calc(100vh - 44px+32px)" }}
          >
            <SelectDataSource ref={selectDataSourceRef} />
          </div>
        )}
        {current === 1 && (
          <div
            style={{
              maxHeight: "calc(100vh - 44px+32px)",
            }}
            className="flex items-center h-full w-full pt-2 overflow-y-hidden justify-center"
          >
            <DataCleaning oss={ossId} ref={settingFormRef} />
          </div>
        )}
        {current === 2 && (
          <div
            style={{
              maxHeight: "calc(100vh - 44px+32px)",
            }}
            className="flex items-center h-full w-full pt-10   overflow-y-hidden justify-center"
          >
            <Finish />
          </div>
        )}
      </Modal>
    </>
  );
};

const SelectDataSource: React.FC<any> = forwardRef((props, ref) => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const formApiRef = React.useRef(null);

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };

  const submitForm = async () => {
    if (formApiRef?.current) {
      return formApiRef?.current
        ?.validate()
        .then(async (values: any) => {
          return Promise.resolve(values);
        })
        .catch((errors: any) => {
          return Promise.reject(errors);
        });
    }
  };

  const resetForm = () => {
    formApiRef.current?.reset();
  };

  useImperativeHandle(ref, () => ({
    submitForm,
    resetForm,
  }));

  const fileIconMapping = {
    "text/plain": <TextIcon />,
    "application/pdf": <PdfIcon />,
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": (
      <WordIcon />
    ),
    "text/markdown": <MarkdownIcon />,
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": (
      <ExcelIcon />
    ),
    "application/vnd.ms-excel": <ExcelIcon />,
    "text/csv": <ExcelIcon />,
    "text/html": <HtmlIcon />,
    default: <TextFile />,
  };

  return (
    <div className="pl-2 py-4 flex flex-col gap-4 lg:w-[70%] md:w-[100%] xl:w-[50%] w-full">
      <Form getFormApi={getFormApi} style={{ padding: 10, width: "100%" }}>
        <Section text={"数据源"}>
          <Row>
            <Col span={24}>
              <Form.Upload
                label="上传文本文件"
                action={
                  location.hash +
                  import.meta.env.VITE_GLOB_API_URL_PREFIX +
                  "/resource/oss/upload"
                }
                rules={[{ required: true, message: "请上传文件" }]}
                maxSize={15 * 1024 * 1024}
                limit={1}
                draggable={true}
                renderFileItem={(renderFileItemProps) => {
                  const mimeType = renderFileItemProps.fileInstance.type;
                  const IconComponent =
                    fileIconMapping[mimeType] || fileIconMapping.default;
                  return (
                    <div
                      className={
                        renderFileItemProps?.status === "uploadFail"
                          ? "border  border-semi-color-danger w-full hover:bg-semi-color-fill-0 cursor-pointer border-solid px-5 py-2 rounded-md flex items-center justify-between"
                          : "w-full hover:bg-semi-color-fill-0 cursor-pointer border-semi-color-border border border-solid px-5 py-2 rounded-md flex items-center justify-between"
                      }
                    >
                      <div className="flex items-center gap-5">
                        {IconComponent}
                        <div className="w-full flex flex-col gap-1">
                          <div>{renderFileItemProps?.name}</div>
                          <div className="w-full min-w-[200px]">
                            <Progress
                              percent={renderFileItemProps?.percent}
                              size="large"
                              aria-label="disk usage"
                              showInfo={true}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div>{renderFileItemProps?.size}</div>
                        <Button
                          icon={<IconClose />}
                          aria-label="删除"
                          theme="borderless"
                          type="tertiary"
                          onClick={renderFileItemProps?.onRemove}
                        />
                      </div>
                    </div>
                  );
                }}
                fileName="file"
                afterUpload={({ response, file }) => {
                  debugger;
                  if (response.code == ResultEnum.SUCCESS) {
                    const { data } = response;
                    return {
                      autoRemove: false,
                      status: "success",
                      name: data.fileName,
                      url: data.url,
                      ...data,
                    };
                  } else if (response.code === ResultEnum.NOT_AUTH) {
                    Modal.warning({
                      title: "登录过期提示",
                      content: response.msg,
                      okText: "重新登录",
                      onOk: () => {
                        const pre = window.location.origin;
                        window.location.href = `${pre}${PageEnum.BASE_LOGIN}?redirectUrl=${encodeURIComponent(window.location.href)}`;
                      },
                    });
                  } else {

                    Toast.error(response.msg)
                    return {
                      status: "uploadFail",
                      autoRemove: true
                    };
                  }
                }}
                field="files"
                headers={{
                  clientid: import.meta.env.VITE_GLOB_CLIENT_ID,
                  Authorization: `Bearer ${getAccessToken()}`,
                }}
                dragMainText={"点击上传文件或拖拽文件到这里"}
                accept={
                  ".txt, .markdown, .mdx, .pdf, .html, .xlsx, .xls, .docx, .csv, .md, .html"
                }
                dragSubText="已支持 TXT、 MARKDOWN、 MDX、 PDF、 HTML、 XLSX、 XLS、 DOCX、 CSV、 MD、 HTML，每个文件不超过 15MB。"
                style={{ marginTop: 10, marginBottom: 10 }}
              ></Form.Upload>
            </Col>
          </Row>
        </Section>
      </Form>
    </div>
  );
});

const DataCleaning: React.FC<any> = forwardRef((props, ref) => {
  const formApiRef = React.useRef(null);
  const [dataList, setDataList] = useState([]);
  const [loading, setLoading] = useState(false);

  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const resetForm = () => {
    formApiRef.current?.reset();
  };
  const submitForm = async () => {
    if (formApiRef?.current) {
      return formApiRef?.current
        ?.validate()
        .then(async (values: any) => {
          return Promise.resolve(values);
        })
        .catch((errors: any) => {
          return Promise.reject(errors);
        });
    }
  };
  useImperativeHandle(ref, () => ({
    submitForm,
    resetForm,
  }));

  // const submitForm = () => {
  //   if (formApiRef.current) {
  //     return formApiRef.current
  //       ?.validate()
  //       .then(async (values: any) => {
  //         return handleChuck(values);
  //       })
  //       .catch((errors: any) => {
  //         return Promise.reject(errors);
  //       });
  //   }
  // };
  const getPrevieChuck = () => {
    if (formApiRef.current) {
      return formApiRef.current
        ?.validate()
        .then(async (values: any) => {
          return handleChuck(values);
        })
        .catch((errors: any) => {
          return Promise.reject(errors);
        });
    }
  };

  const handleChuck = async (data: any) => {
    setLoading(true);
    return await chuckDataset(data, props.oss)
      .then((res) => {
        setLoading(false);
        setDataList(res.data);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <div className="w-full py-2 h-full ">
      <Row gutter={12} className="w-full h-full" style={{ height: "100%" }}>
        <Col span={11} className="h-full">
          <Card
            title="分段设置"
            footer={
              <div className="flex justify-end">
                <Button theme="solid" type="primary" onClick={getPrevieChuck}>
                  预览分块
                </Button>
              </div>
            }
            bodyStyle={{
              padding: "10px",
              overflowY: "auto",
              width: "100%",
              height: "80%",
            }}
            className="w-full overflow-y-auto py-2 h-full"
          >
            <Form getFormApi={getFormApi}>
              <Row gutter={10}>
                <Col span={12}>
                  <Form.InputNumber
                    className="w-full"
                    name="chunkSize"
                    initValue={800}
                    suffix={"Tokens"}
                    rules={[{ required: true, message: "分片文本数不能为空" }]}
                    field="chunkSize"
                    label="分片大小"
                    trigger="blur"
                  />
                  <Form.InputNumber
                    className="w-full"
                    name="minChunkSizeChars"
                    initValue={350}
                    suffix={"Tokens"}
                    rules={[
                      { required: true, message: "分片最小文本数不能为空" },
                    ]}
                    field="minChunkSizeChars"
                    label="最小分片"
                    trigger="blur"
                  />
                </Col>
                <Col span={12}>
                  <Form.InputNumber
                    className="w-full"
                    name="maxNumChunks"
                    initValue={10000}
                    suffix={"Tokens"}
                    rules={[
                      { required: true, message: "最大分片文本数不能为空" },
                    ]}
                    field="maxNumChunks"
                    label="最大分片"
                    trigger="blur"
                  />
                </Col>
                <Divider margin="12px" align="left">
                  文本处理规则
                </Divider>
                <Col span={24}>
                  <Form.CheckboxGroup
                    field="rule"
                    name="rule"
                    direction="horizontal"
                    type="card"
                    label="文本预处理"
                    trigger="blur"
                  >
                    <Checkbox
                      style={{
                        width: "100%",
                        border: "1px solid var(--semi-color-border)",
                      }}
                      value="keepSeparator"
                      extra="分隔符会被保留在分段后的文本块中"
                    >
                      是否保留分隔符
                    </Checkbox>
                  </Form.CheckboxGroup>
                </Col>
              </Row>
            </Form>
          </Card>
        </Col>
        <Col span={13} className="h-full">
          <Card
            title={"分块预览"}
            className="w-full py-2 h-full overflow-y-auto"
            bodyStyle={{
              padding: "10px",
              overflowY: "auto",
              overflowX: "hidden",
              height: "90%",
              width: "100%",
              wordBreak: "break-all",
            }}
            headerExtraContent={
              <div
                style={{ fontSize: "12px", fontWeight: "400" }}
                className="text-semi-color-text-2"
              >
                仅展示10片分块
              </div>
            }
          >
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <Spin size="large" />
              </div>
            ) : (
              <List
                className="w-full text-wrap"
                dataSource={dataList}
                emptyContent={
                  <Empty
                    image={
                      <IllustrationNoResult
                        style={{ width: 200, height: 200 }}
                      />
                    }
                    darkModeImage={
                      <IllustrationNoResultDark
                        style={{ width: 200, height: 200 }}
                      />
                    }
                    description={"暂无数据"}
                  />
                }
                renderItem={(item: any, index: any) => (
                  <List.Item
                    key={index}
                    className="w-full rounded-sm hover:bg-semi-color-fill-0"
                    style={{}}
                  >
                    <MarkdownBox content={item} />
                  </List.Item>
                )}
              />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
});

const Finish = () => {
  return (
    <div className="w-full h-full flex flex-row justify-center">
      <div className="w-[60%] flex justify-end">
        <div className="flex flex-col gap-3 w-[600px] ">
          <div className="flex flex-col gap-2">
            <Typography.Title heading={5}>🎉 文档已上传</Typography.Title>
            <Typography.Text type="secondary" className="mt-2 mb-2">
              文档已上传至知识库： 2161941a36104a97aa...
              ，你可以在知识库的文档列表中找到它。
            </Typography.Text>
          </div>
          <div>
            <Descriptions align="left">
              <Descriptions.Item itemKey="分段模式">自定义</Descriptions.Item>
              <Descriptions.Item itemKey="最大分段长度">500</Descriptions.Item>
              <Descriptions.Item itemKey="文本预处理规则">
                替换掉连续的空格、换行符和制表符
              </Descriptions.Item>
              <Descriptions.Item itemKey="索引方式">高质量</Descriptions.Item>
            </Descriptions>
          </div>
          <div>
            <Button
              theme="solid"
              iconPosition="right"
              icon={<IconArrowRight />}
              type="primary"
            >
              返回文档
            </Button>
          </div>
        </div>
      </div>
      <div className="w-[40%]">
        <div className="w-[300px] bg-semi-color-fill-0 h-[200px] gap-2 px-4 py-1 rounded-lg flex  justify-center flex-col">
          <div className="shadow-md w-[40px] h-[40px] flex items-center justify-center rounded-md bg-semi-color-white">
            <IconShoppingBag />
          </div>
          <Typography.Title heading={6}>接下来做什么</Typography.Title>
          <div className="w-full text-wrap text-xs text-semi-color-text-2">
            当文档完成索引处理后，知识库即可集成至应用内作为上下文使用，你可以在提示词编排页找到上下文设置。你也可以创建成可独立使用的
            ChatGPT 索引插件发布。
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadTextForm;
