// 全局覆盖 md-editor-rt 样式
.prompt-editor-container {
//   .md-editor {
//     border: 1px solid #e5e7eb !important;
//     border-radius: 6px !important;
//     overflow: hidden !important;
    
//     &:focus-within {
//       border-color: #3b82f6 !important;
//       box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
//     }
//   }
  
//   // 整个标题的样式
//   .markdown-heading {
//     display: inline-block;
//     width: 100%;
//     font-weight: bold;
//     background: linear-gradient(90deg, #ff6b6b, #4dabf7);
//     -webkit-background-clip: text;
//     -webkit-text-fill-color: transparent;
//   }
  
//   // 标题标记样式 (# 符号)
//   .heading-marker,
//   .cm-line span[class*="ͼ2o"] {
//     color: #ff6b6b !important;
//     -webkit-text-fill-color: #ff6b6b !important;
//     font-weight: bold !important;
//   }
  
//   // 标题文本样式
//   .heading-text,
//   .cm-line span[class*="ͼ2m"]:not([class*="ͼ2o"]) {
//     color: #4dabf7 !important;
//     background: linear-gradient(90deg, #4dabf7, #748ffc) !important;
//     -webkit-background-clip: text !important;
//     -webkit-text-fill-color: transparent !important;
//     font-weight: bold !important;
//   }
  
//   // 变量高亮样式
//   .variable-highlight {
//     background-color: rgba(59, 130, 246, 0.1) !important;
//     border-radius: 4px !important;
//     padding: 2px 4px !important;
//     font-weight: bold !important;
//     color: #3b82f6 !important;
//   }
  
//   // 直接匹配编辑器内部的类名 - 基于实际DOM结构
//   .cm-editor {
//     .cm-content {
//       .cm-line {
//         // 已处理的标题行
//         &.processed-heading {
//           padding: 4px 0;
//         }
//       }
//     }
//   }
}

// 斜杠命令下拉菜单样式
.slash-command-dropdown {
  position: absolute;
  z-index: 1000;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 280px;
  max-height: 320px;
  overflow-y: auto;
}

.slash-command-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  
  &:hover, &.selected {
    background-color: #f3f4f6;
  }
  
  .command-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 12px;
    color: #6b7280;
  }
  
  .command-content {
    flex: 1;
    
    .command-title {
      font-size: 14px;
      font-weight: 500;
      color: #111827;
    }
    
    .command-description {
      font-size: 12px;
      color: #6b7280;
    }
  }
  
  .command-shortcut {
    font-size: 12px;
    color: #6b7280;
    background-color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
  }
}

// 自定义状态栏样式
.prompt-editor-statusbar {
  

  // 折叠按钮样式
  button {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border: none;
    background: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease-in-out;

    &:hover {
      background-color: #e5e7eb;
      color: #374151;
    }

    &:active {
      transform: scale(0.95);
    }

    svg {
      transition: transform 0.2s ease-in-out;
    }
  }
}
