import {
  <PERSON>tar,
  <PERSON><PERSON>,
  Card,
  Col,
  <PERSON><PERSON>se,
  DatePicker,
  Divider,
  Dropdown,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Space,
  Spin,
  Table,
  TabPane,
  Tabs,
  Tag,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { createRef, useEffect, useMemo, useState } from "react";
import { LocalForageService as storage } from "../../../utils/storage";
import { OpenAIAttribute } from "../../../interface/llm";
import { initialOpenaiAttribute } from "../../../utils/initial-state";
import { MessageUtil } from "../../../utils/message-util";
import {
  IconDelete,
  IconEdit,
  IconHelpCircle,
  IconMore,
  IconRefresh,
  IconRefresh2,
  IconSearch,
} from "@douyinfe/semi-icons";
import Meta from "@douyinfe/semi-ui/lib/es/card/meta";
import { useBoolean } from "@/hooks";
import ModelForm from "./components/ModelForm";
import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import { getModelList } from "@/api/platform/model";
import useDictionary from "@/hooks/useDictionary";
import DictTag from "@/components/DictTag";
import { dictConvetToOpention } from "@/utils";
import { models } from "@/enums/modelEnum";

function ModelIndex() {
  const [formOpen, { setTrue, setFalse, toggle }] = useBoolean(false);
  const scroll = useMemo(() => ({ y: 600, x: "1200px" }), []);
  const [loading, setLoading] = useState(false);
  const initPageInfo = {
    page: 1,
    pageSize: 10,
  };
  const [page, setPage] = useState(initPageInfo.page);
  const [pageSize, setPageSize] = useState(initPageInfo.pageSize);

  const [total, setTotal] = useState(0);
  const [dataSource, setData] = useState([]);
  const [search, setSearch] = useState("");
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "ai_model_type",
    "sys_normal_disable",
    "ai_model_provider",
  ]);
  useEffect(() => {
    loadDictionary();
  }, []);
  const getFilter = (data: any) => {
    return data?.map((element: any) => {
      return {
        text: element.dictLabel,
        value: element.dictValue,
      };
    });
  };
  const columns = useMemo(() => {
    return [
      {
        title: "模型版本",
        dataIndex: "modelVersion",
        render: (text, record, index) => {
          return (
            <div className="flex items-center gap-2">
              <Avatar
                size="extra-small"
                shape="square"
                src={record.logo}
              ></Avatar>
              <div>{text}</div>
            </div>
          );
        },
      },
      {
        title: "别名",
        dataIndex: "aliasName",
      },
      {
        title: "供应商",
        dataIndex: "provider",
        // filters: getFilter(dictionaryData.ai_model_provider),
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_provider"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_provider || []}
            />
          );
        },
      },
      {
        title: "模型类型",
        dataIndex: "type",
        // filters: getFilter(dictionaryData.ai_model_type),
        render: (text: any) => {
          return (
            <DictTag
              dictType="ai_model_type"
              dictValue={text}
              dictionaryData={dictionaryData.ai_model_type || []}
            />
          );
        },
      },
      // {
      //   title: "上下文长度",
      //   dataIndex: "tokens",
      //   align: "center",
      //   // sorter: (a: number, b: number) => (a.tokens - b.tokens > 0 ? 1 : -1),
      //   render: (text: number) => `${text} K`,
      // },
      {
        title: "描述",
        width: 250,
        ellipsis: { showTitle: true },
        dataIndex: "descrption",
      },
      {
        title: "状态",
        dataIndex: "status",
        render: (text: any) => {
          return (
            <DictTag
              type="badge"
              dictType="sys_normal_disable"
              dictValue={text}
              dictionaryData={dictionaryData.sys_normal_disable || []}
            />
          );
        },
      },
      {
        title: "更新日期",
        dataIndex: "updatedAt",
      },
      {
        title: "",
        dataIndex: "operate",
        render: () => {
          return (
            <Dropdown
              position="bottom"
              className="w-[100px]"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item>编 辑</Dropdown.Item>
                  <Dropdown.Item type="danger">删 除</Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                size="small"
                theme="borderless"
                type="primary"
                icon={<IconMore className="cursor-pointer" />}
              />
            </Dropdown>
          );
        },
      },
    ];
  }, []);

  const fetchData = (
    currentPage = initPageInfo.page,
    pageSize = initPageInfo.pageSize,
    formValues = {}
  ) => {
    setLoading(true);
    setPage(currentPage);
    getModelList({
      pageNum: currentPage,
      pageSize,
      ...formValues,
    })
      .then((res) => {
        setLoading(false);
        setData(res.rows);
        setTotal(res.total);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handlePageChange = (page: number) => {
    fetchData(page);
  };
  const handlePageSizeChane = (pageSize: number) => {
    fetchData(page, pageSize);
  };

  useEffect(() => {
    fetchData();
  }, []);
  const handleSearch = (formValues: any) => {
    fetchData(initPageInfo.page, initPageInfo.pageSize, formValues);
  };
  const handleReset = () => {
    fetchData(initPageInfo.page, initPageInfo.pageSize);
  };

  const handleCancel = () => {
    setFalse();
  };

  return (
    <div className="h-full w-full  relative flex flex-col">
      <div className="text-nowrap font-extrabold pl-4 pt-2 text-lg mb-2 flex  items-center justify-between">
        <div>模型管理</div>
      </div>
      <div className="bg-semi-color-white rounded-xl w-full flex-1 pt-2 px-2">
        <div className="my-3 mx-1 flex gap-1 items-center justify-between flex-wrap">
          <Form
            onReset={() => handleReset()}
            onSubmit={(values) => handleSearch(values)}
          >
            <Space wrap>
              <Form.Input
                noLabel
                style={{ width: 260 }}
                placeholder={"请输入模型名称"}
                field="aliasName"
              />
              <Form.Select
                filter
                noLabel
                optionList={dictConvetToOpention(
                  dictionaryData["ai_model_provider"]
                )}
                showClear
                style={{ width: 260 }}
                placeholder={"请选择模型供应商"}
                field="provider"
              />
              <Space>
                <Button theme="solid" type="primary" htmlType="submit">
                  提交
                </Button>
                <Button theme="outline" type="tertiary" htmlType="reset">
                  重置
                </Button>
              </Space>
            </Space>
          </Form>
          <Button theme="solid" type="primary" onClick={setTrue}>
            新增模型
          </Button>
        </div>

        <Table
          loading={loading}
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          scroll={scroll}
          // bordered
          empty={
            <Empty
              image={
                <IllustrationNoContent style={{ width: 150, height: 150 }} />
              }
              darkModeImage={
                <IllustrationNoContentDark
                  style={{ width: 150, height: 150 }}
                />
              }
              title="暂无数据"
            ></Empty>
          }
          pagination={{
            currentPage: page,
            pageSize: pageSize,
            onPageSizeChange: handlePageSizeChane,
            total: total,
            onPageChange: handlePageChange,
          }}
        />
      </div>
      <ModelForm
        open={formOpen}
        onCancel={handleCancel}
        onRefresh={handleReset}
        providers={dictConvetToOpention(dictionaryData["ai_model_provider"])}
        types={dictConvetToOpention(dictionaryData["ai_model_type"])}
      />
    </div>
  );
}

export default ModelIndex;
