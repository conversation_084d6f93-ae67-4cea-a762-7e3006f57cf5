import { IconConfig, IconIntro, IconToken } from "@douyinfe/semi-icons-lab";
import { Icon, Nav, Popover, Tooltip } from "@douyinfe/semi-ui";
import { useNavigate } from "@tanstack/react-router";
import React, { Children, useEffect, useState } from "react";
import "./index.scss";
import { findMenuByKey, Platform, SystemMenus } from "@/constants/systemMenus";
import Logo from "@/layout/components/logo";
import Search from "@/layout/components/Search";
import AvatarMenu from "@/layout/components/AvatarMenu";
import { Monitor } from "lucide-react";
const LayoutSider: React.FC<SiderProps> = () => {
  const navigate = useNavigate();
  const [activeKey, setActiveKey] = useState<Array<string>>([]);

  useEffect(() => {
    setActiveKey([location.pathname]);
    const menu = findMenuByKey(SystemMenus, location.pathname);
    document.title = (menu?.label as string) || "LynkzHub";
  }, [location.pathname]);

  const jump = (key: string) => {
    setActiveKey([key]);
    navigate({
      to: key,
    });
  };

  return (
    <div className="w-full flex items-center">
      <SiderNav
        jump={jump}
        activeKey={activeKey}
        menus={Platform}
        setActiveKey={setActiveKey}
      />
    </div>
  );
};

const SiderNav: React.FC<{
  activeKey: Array<string>;
  jump: (key: string) => void;
  menus: Array<MenuProps>;
  setActiveKey: (key: Array<string>) => void;
}> = ({ activeKey, jump, menus, setActiveKey }) => {
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [collapsed, setCollapsed] = useState<any>(false);
  const navigate = useNavigate();

  const renderMenuItems = (items: MenuProps[]): JSX.Element[] => {
    return items.map((item) => {
      if (item.children && item.children.length > 0) {
        return (
          <Nav.Sub
            key={item.key}
            itemKey={item.key}
            text={item.label}
            icon={item.icon}
          >
            {renderMenuItems(item.children)}
          </Nav.Sub>
        );
      } else {
        return (
          <Nav.Item
            key={item.key}
            itemKey={item.key}
            text={item.label}
            icon={item.icon}
          />
        );
      }
    });
  };
  return (
    <Nav
      className="h-full w-full mt-2  relative  "
      selectedKeys={activeKey}
      style={{ background: "transparent" }}
      mode="horizontal"
      header={
        <Logo
          onclick={() => {
            jump("/home");
          }}
        ></Logo>
      }
      isCollapsed={collapsed}
      onSelect={(data: any) => {
        jump(data.itemKey as string);
        setActiveKey(data.selectedKeys as string[]);
      }}
      footer={
        <div className="flex items-center gap-2">
          <Tooltip content="系统管理" position="bottom">
            <div
              onClick={(e) => {
                e.stopPropagation();
                jump("/system/org/user");
              }}
              className="flex w-[34px] h-[34px] items-center justify-center hover:bg-semi-color-fill-0 cursor-pointer rounded-md"
            >
              <Icon svg={<Monitor size="1em" />} />
            </div>
          </Tooltip>
          <Search />
          <AvatarMenu showName position="bottom" />
        </div>
      }
      onCollapseChange={(isCollapsed: boolean) => {
        setCollapsed(isCollapsed);
      }}
    >
      {renderMenuItems(menus)}
    </Nav>
  );
};

export default LayoutSider;
