import {
  IconDescend2,
  IconFavoriteList,
  IconHandle,
  IconHash,
  IconMark,
  IconMinusCircle,
} from "@douyinfe/semi-icons";
import {
  Divider,
  Form,
  Typography,
  Button,
  Modal,
  Row,
  Toast,
  Col,
  ArrayField,
} from "@douyinfe/semi-ui";
// import { DndContext, closestCenter } from "@dnd-kit/core";
// import {
//   SortableContext,
//   verticalListSortingStrategy,
//   useSortable,
// } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import classNames from "classnames";
import React, { useEffect, useRef, useState } from "react";

export default function VarModel({
  open,
  onCancel,
  onSubmit,
  variable,
  existingKeys = [],
}: any) {
  const [tab, setTab] = useState("text");
  const textFormRef = useRef<any>(null);
  const numberFormRef = useRef<any>(null);
  const dropdownFormRef = useRef<any>(null);
  const buttons = [
    {
      label: "输入框变量",
      value: "text",
      icon: <IconMark />,
    },
    {
      label: "数字框变量",
      icon: <IconHash />,
      value: "number",
    },
    // {
    //   label: "下拉框变量",
    //   icon: <IconDescend2 />,
    //   value: "options",
    // },
    // {
    //   label: "自定义变量",
    //   icon: <IconFavoriteList />,
    //   value: "custom",
    // },
  ];

  const handleChangeTab = (value: string) => {
    setTab(value);
  };
  useEffect(() => {
    if (variable && open) {
      debugger;
      setTab(variable.type);

      setTimeout(() => {
        if (variable.type === "text" && textFormRef.current) {
          textFormRef.current.formApi.setValues({
            key: variable.key,
            defaultValue: variable.defaultValue,
            required: variable.required,
            maxLength: variable.maxLength,
            description: variable.description,
          });
        } else if (variable.type === "number" && numberFormRef.current) {
          numberFormRef.current.formApi.setValues({
            key: variable.key,
            defaultValue: variable.defaultValue,
            required: variable.required,
            maxLength: variable.maxLength,
            description: variable.description,
          });
        } else if (variable.type === "options" && dropdownFormRef.current) {
          dropdownFormRef.current.formApi.setValues({
            key: variable.key,
            defaultValue: variable.defaultValue,
            required: variable.required,
            description: variable.description,
            options: variable.options,
          });
        }
      }, 100);
    }
  }, [variable, open]);

  useEffect(() => {
    // Reset form values when tab changes
    if (!open) return;

    if (tab === "text" && textFormRef.current) {
      textFormRef.current.formApi.reset();
    } else if (tab === "number" && numberFormRef.current) {
      numberFormRef.current.formApi.reset();
    } else if (tab === "options" && dropdownFormRef.current) {
      dropdownFormRef.current.formApi.reset();
    }
  }, [tab, open]);

  return (
    <Modal
      width={"55%"}
      title="变量配置"
      visible={open}
      centered
      footer={
        <div className="flex gap-1 justify-end mx-1 my-2">
          <Button onClick={() => onCancel()}>取消</Button>
          <Button
            theme="solid"
            type="secondary"
            onClick={async () => {
              let formData: any = {};

              // Validate and get form data based on current tab
              if (tab === "text" && textFormRef?.current) {
                formData = await textFormRef.current.formApi.validate();
                formData.type = "text";
              } else if (tab === "number" && numberFormRef?.current) {
                formData = await numberFormRef.current.formApi.validate();
                formData.type = "number";
              } else if (tab === "options" && dropdownFormRef?.current) {
                formData = await dropdownFormRef.current.formApi.validate();
                formData.type = "options";
              }

              // Submit to parent component
              onSubmit?.(formData, true);

              // Show success message

              // Reset form for continued adding
              if (tab === "text" && textFormRef.current) {
                textFormRef.current.formApi.reset();
              } else if (tab === "number" && numberFormRef.current) {
                numberFormRef.current.formApi.reset();
              } else if (tab === "options" && dropdownFormRef.current) {
                dropdownFormRef.current.formApi.reset();
              }
            }}
          >
            继续添加
          </Button>
          <Button
            type="primary"
            theme="solid"
            onClick={async () => {
              let formData: any = {};
              if (tab === "text" && textFormRef?.current) {
                formData = await textFormRef.current?.formApi.validate();
                formData.type = "text";
              } else if (tab === "number" && numberFormRef?.current) {
                formData = await numberFormRef.current?.formApi.validate();
                formData.type = "number";
              } else if (tab === "options" && dropdownFormRef?.current) {
                formData = await numberFormRef.current?.formApi.validate();
                formData.type = "options";
              }
              onSubmit?.(formData, false);
            }}
          >
            保存
          </Button>
        </div>
      }
      onCancel={onCancel}
      bodyStyle={{
        height: 400,
      }}
    >
      <div className="flex flex-col h-full w-full">
        <div className="flex flex-1 flex-row h-full w-full">
          <div className="flex-1 p-1">
            <div className="flex gap-2 flex-col">
              <div>类型</div>
              <div className="grid grid-flow-col text-semi-color-text-1 grid-rows-2 gap-2 w-full">
                {buttons.map((item, index) => (
                  <div
                    key={index}
                    onClick={() => handleChangeTab(item.value)}
                    className={classNames(
                      "flex row-span-1 hover:text-semi-color-primary border w-full border-solid border-semi-color-border flex-row items-center hover:bg-semi-color-fill-0 cursor-pointer rounded-md py-2 px-2 gap-2",
                      item.value === tab
                        ? "border-semi-color-primary text-semi-color-primary"
                        : ""
                    )}
                  >
                    {item.icon}
                    <div>{item.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <div className="flex-1 p-1 mb-2 overflow-y-auto overflow-x-hidden semi-light-scrollbar">
            {tab === "text" && <TextForm ref={textFormRef} />}
            {/* {tab === "options" && <DropdownForm ref={dropdownFormRef} />} */}
            {tab === "number" && <NumberInputForm ref={numberFormRef} />}
          </div>
        </div>
      </div>
    </Modal>
  );
}

const TextForm = React.forwardRef((props: any, ref: any) => {
  return (
    <Form ref={ref}>
      <Form.Input
        field="key"
        rules={[{ required: true, message: "请输入变量Key" }]}
        label="变量Key"
        placeholder="变量 Key"
        required
      />
      <Form.Input
        field="defaultValue"
        rules={[{ required: true, message: "请输入默认值" }]}
        label="默认值"
        placeholder="请输入默认值"
        required
      />
      <Form.Switch
        rules={[{ required: true, message: "请选择是否必填" }]}
        size="small"
        label="是否必填"
        initValue={true}
        field="required"
      />
      <Form.InputNumber
        className="w-full"
        rules={[{ required: true, message: "请输入最大长度" }]}
        field="maxLength"
        min={1}
        max={255}
        initValue={"40"}
        label="最大长度"
        placeholder="请输入最大长度"
      />
      <Form.TextArea
        field="description"
        rules={[{ required: true, message: "请输入变量描述" }]}
        label="变量描述"
        placeholder="变量描述"
      />
    </Form>
  );
});

// const DropdownForm = React.forwardRef((props: any, ref: any) => {
//   const handleDragEnd = (event: any) => {
//     // console.log(formRef.current.formApi.getValues());

//     const { active, over } = event;
//     if (!over || active.id === over.id) return;

//     const oldIndex = active.id;
//     const newIndex = over.id;

//     //id 对应的是下标，所以要替换他们的下标
//     // setArrayFields((fields) => {
//     // const newFields = [...fields];
//     // const temp = newFields[oldIndex];
//     // newFields[oldIndex] = newFields[newIndex];
//     // newFields[newIndex] = temp;
//     //   return newFields;
//     // });
//     // const newFields = [...formRef?.current.getValue("options")];
//     // const temp = newFields[oldIndex];
//     // newFields[oldIndex] = newFields[newIndex];
//     // newFields[newIndex] = temp;
//     // formRef?.current.setValue("options", newFields);
//     // 使用 Semi Design 内置的 move 方法来移动字段
//     // asd.move(oldIndex, newIndex);
//   };

//   return (
//     <Form ref={ref}>
//       <Form.Input
//         field="key"
//         rules={[{ required: true, message: "请输入变量Key" }]}
//         label="变量 Key"
//         placeholder="变量 Key"
//         required
//       />
//       <Form.Input
//         field="defaultValue"
//         rules={[{ required: true, message: "请输入默认值" }]}
//         label="默认值"
//         placeholder="请输入默认值"
//         required
//       />
//       <Form.Switch
//         rules={[{ required: true, message: "请选择是否必填" }]}
//         size="small"
//         label="是否必填"
//         field="required"
//       />
//       <Form.TextArea
//         field="description"
//         rules={[{ required: true, message: "请输入变量描述" }]}
//         label="变量描述"
//         placeholder="变量描述"
//       />
//       <div className="w-full flex flex-col">
//         <Form.Label>选择项</Form.Label>
//         <ArrayField field="options">
//           {({ add, arrayFields, addWithInitValue }) => (
//             <React.Fragment>
//               {arrayFields.map(({ field, key, remove }, index) => (
//                 <DndContext
//                   collisionDetection={closestCenter}
//                   onDragEnd={(event) => handleDragEnd(event)}
//                 >
//                   <SortableContext
//                     items={arrayFields.map((_, index) => index)}
//                     strategy={verticalListSortingStrategy}
//                   >
//                     <SortableItem
//                       key={index}
//                       field={field}
//                       index={index}
//                       remove={remove}
//                     />
//                   </SortableContext>
//                 </DndContext>
//               ))}
//               <Button
//                 onClick={add}
//                 className="mt-1"
//                 theme="outline"
//                 type="primary"
//               >
//                 添加选项
//               </Button>
//             </React.Fragment>
//           )}
//         </ArrayField>
//       </div>
//     </Form>
//   );
// });

const NumberInputForm = React.forwardRef((props: any, ref: any) => {
  return (
    <Form ref={ref}>
      <Form.Input
        field="key"
        rules={[{ required: true, message: "请输入变量Key" }]}
        label="变量 Key"
        placeholder="变量 Key"
        required
      />
      <Form.Input
        field="label"
        rules={[{ required: true, message: "请输入显示名称" }]}
        label="显示名称"
        placeholder="请输入显示名称"
        required
      />
      <Form.Switch
        rules={[{ required: true, message: "请选择是否必填" }]}
        size="small"
        label="是否必填"
        initValue={true}
        field="required"
      />
      <Form.InputNumber
        className="w-full"
        rules={[{ required: true, message: "请输入最大值" }]}
        field="max"
        label="最大值"
        placeholder="请输入最大值"
      />
      <Form.InputNumber
        className="w-full"
        rules={[{ required: true, message: "请输入最小值" }]}
        field="min"
        label="最小值"
        placeholder="请输入最小值"
      />
      <Form.TextArea
        field="description"
        rules={[{ required: true, message: "请输入变量描述" }]}
        label="变量描述"
        placeholder="变量描述"
      />
    </Form>
  );
});

// const SortableItem = ({ field, index, remove }: any) => {
//   const { attributes, listeners, setNodeRef, transform, transition } =
//     useSortable({
//       id: index,
//     });

//   const style = {
//     transform: CSS.Transform.toString(transform),
//     transition,
//   };

//   return (
//     <div
//       ref={setNodeRef}
//       style={style}
//       {...attributes}
//       className="flex w-full flex-row gap-2 items-center"
//     >
//       <Button
//         {...listeners}
//         className="cursor-move"
//         type="tertiary"
//         theme="borderless"
//       >
//         <IconHandle />
//       </Button>
//       <Form.Input
//         field={`${field}[name]`}
//         noLabel
//         className="flex-1"
//         fieldStyle={{ paddingBottom: 0 }}
//         fieldClassName="flex-1"
//         placeholder={`选项${index + 1}`}
//       />
//       <Button
//         type="danger"
//         theme="borderless"
//         icon={<IconMinusCircle />}
//         onClick={() => {
//           remove(index);
//         }}
//       />
//     </div>
//   );
// };
