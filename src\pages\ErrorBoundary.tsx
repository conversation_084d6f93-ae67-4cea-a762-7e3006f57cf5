import { Button, Empty, Icon } from "@douyinfe/semi-ui";
import { useRouter } from "@tanstack/react-router";
import ErrorIcon from "../components/icon/ErrorIcon";
import { useEffect, useState } from "react";
import { LocalForageService as storage } from "../utils/storage";
import { motion } from "framer-motion";

interface ErrorBoundaryProps {
  error: {
    message: string;
    stack?: string;
  };
}

function ErrorBoundary({ error }: ErrorBoundaryProps) {
  const router = useRouter();
  const [themeMode, setThemeMode] = useState<"light" | "dark">("light");

  useEffect(() => {
    storage.getItem<"light" | "dark">("theme_mode").then((mode) => {
      setThemeMode(mode ?? "light");
    });
  }, []);

  const backToHome = () => {
    router.navigate({ to: "/" });
  };

  const tryAgain = () => {
    //刷新
    window.location.reload();
  };

  return (
    <div className="w-full h-full flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-xl w-full"
      >
        <Empty
          image={
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 260,
                damping: 20,
              }}
            >
              <ErrorIcon
                style={{
                  width: 200,
                  height: 200,
                  filter:
                    themeMode === "dark"
                      ? "invert(0.8) hue-rotate(180deg)"
                      : "none",
                }}
              />
            </motion.div>
          }
          title={
            <motion.span
              className="text-xl font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              页面出错了
            </motion.span>
          }
          style={{ padding: 20, textAlign: "center" }}
        >
          <motion.div
            className="flex flex-col items-center   max-w-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <p className="text-semi-color-danger mb-2 text-sm font-semibold">
              {error.message}
            </p>
            <div
              className={`${themeMode === "dark" ? "bg-gray-800" : "bg-gray-100"} p-3 rounded-md max-h-60 overflow-auto text-xs text-left w-full`}
            >
              <pre className="whitespace-pre-wrap break-words">
                {error.stack || "无堆栈信息"}
              </pre>
            </div>
          </motion.div>
          <motion.div
            className="flex gap-2 justify-center mt-6"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
          >
            <Button theme="solid" onClick={tryAgain}>
              刷新
            </Button>
          </motion.div>
        </Empty>
      </motion.div>
    </div>
  );
}

export default ErrorBoundary;
