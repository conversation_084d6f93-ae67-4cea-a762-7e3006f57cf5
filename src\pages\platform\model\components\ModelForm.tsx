import {
  IconAlertCircle,
  IconCamera,
  IconEdit,
  IconPlus,
  IconUpload,
} from "@douyinfe/semi-icons";
import {
  Button,
  Form,
  Input,
  SideSheet,
  Tooltip,
  Toast,
  Avatar,
  Upload,
} from "@douyinfe/semi-ui";
import React from "react";
import { models } from "@/enums/modelEnum";
import { addChatModel } from "@/api/platform/chatModel";
import useBoolean from "@/hooks/useBoolean";
import { formRules } from "../contants/model"; // 导入规则文件
import { ResultEnum } from "@/enums/httpEnum";
import { getAccessToken } from "@/utils/auth";
import AppIcon from "@/components/icon/AppIcon";

const ModelForm: React.FC<ModelFormProps> = ({
  open,
  onCancel,
  onRefresh,
  providers,
  types,
}) => {
  const [modelVersions, setModelVersions] = React.useState<any>([]);
  const formApiRef = React.useRef<any>(null);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const defaultLogo =
    "http://************:9000/hov-developer/2025/03/13/91e283754d8d452a9d2fbeba492a44d6.png";
  const [logo, setLogo] = React.useState(defaultLogo);
  // 供应商变更时更新版本选项
  const handleChangeProvider = (data: any) => {
    setModelVersions(models[data] ?? []);
    formApiRef.current?.setValue("modelVersion", "");
  };

  // 获取表单实例
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };

  // 重置表单
  const handleReset = () => {
    setLogo(defaultLogo);
    formApiRef.current?.reset();
  };

  // 提交逻辑
  const handleSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addChatModel(values);
      Toast.success(res.msg);
      setLogo(defaultLogo);
      closeLoading();
      onCancel();
      onRefresh();
      return res;
    } catch (err) {
      closeLoading();
      throw err;
    }
  };

  // 表单提交处理
  const submitForm = async () => {
    try {
      const values = await formApiRef.current?.validate();
      await handleSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };

  const style = {
    backgroundColor: "var(--semi-color-overlay-bg)",
    height: "100%",
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    color: "var(--semi-color-white)",
  };
  const hoverMask = (
    <div style={style}>
      <IconEdit />
    </div>
  );
  return (
    <SideSheet
      title="新增模型"
      visible={open}
      width={500}
      onCancel={() => {
        setLogo(defaultLogo);
        onCancel();
      }}
      closeOnEsc={false}
      // headerStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      // bodyStyle={{ borderBottom: "1px solid var(--semi-color-border)" }}
      className="semi-light-scrollbar"
      footer={
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button style={{ marginRight: 8 }} onClick={handleReset}>
            重置
          </Button>
          <Button theme="solid" loading={loading} onClick={submitForm}>
            提交
          </Button>
        </div>
      }
    >
      <Form
        initValues={
          {
            // logo: "http://************:9000/hov-developer/2025/03/13/91e283754d8d452a9d2fbeba492a44d6.png",
          }
        }
        getFormApi={getFormApi}
        style={{ width: "100%" }}
      >
        <Form.Upload
          className="avatar-upload"
          accept={"image/*"}
          field="logo"
          noLabel
          action={
            location.hash +
            import.meta.env.VITE_GLOB_API_URL_PREFIX +
            "/resource/oss/upload"
          }
          fileName="file"
          maxSize={3 * 1024 * 1024}
          limit={1}
          headers={{
            clientid: import.meta.env.VITE_GLOB_CLIENT_ID,
            Authorization: `Bearer ${getAccessToken()}`,
          }}
          showUploadList={false}
          afterUpload={({ response, file }) => {
            if (response.code == ResultEnum.SUCCESS) {
              const { data } = response;
              setLogo(data.url);
              return {
                autoRemove: false,
                status: "success",
                name: data.fileName,
                url: data.url,
                ...data,
              };
            }
            return {};
          }}
        >
          <Avatar
            size="large"
            src={logo}
            shape="square"
            hoverMask={hoverMask}
          />
        </Form.Upload>
        {/* 模型别名 */}
        <Form.Input
          className="w-full"
          field="aliasName"
          rules={formRules.aliasName}
          showClear
          label="模型别名"
          placeholder="请输入模型别名"
        />

        {/* 模型供应商 */}
        <Form.Select
          className="w-full"
          filter
          field="provider"
          rules={formRules.provider}
          showClear
          optionList={providers}
          onChange={handleChangeProvider}
          label="模型供应商"
          placeholder="请选择模型供应商"
        />

        {/* 模型版本 */}
        <Form.Select
          field="modelVersion"
          filter
          outerBottomSlot={
            <div className="flex justify-center items-center mx-1">
              <Button block>未找到对应版本？点击添加</Button>
            </div>
          }
          optionList={modelVersions}
          showClear
          rules={formRules.modelVersion}
          placeholder="请选择模型版本"
          label="模型版本"
          className="w-full"
          trigger="blur"
        />

        {/* 模型类型 */}
        <Form.Select
          className="w-full"
          filter
          rules={formRules.type}
          showClear
          optionList={types}
          field="type"
          label="模型类型"
          placeholder="请选择模型类型"
        />

        {/* 接口地址 */}
        <Form.Input
          field="endpoint"
          rules={formRules.endpoint}
          showClear
          placeholder="请输入接口地址"
          label={{
            text: "接口地址",
            extra: (
              <Tooltip content="兼容OpenAPI 格式">
                <IconAlertCircle
                  style={{ color: "var(--semi-color-text-2)" }}
                />
              </Tooltip>
            ),
          }}
          trigger="blur"
        />

        {/* API KEY */}
        <Form.Input
          className="w-full"
          showClear
          field="apiKey"
          rules={formRules.apiKey}
          label="API KEY"
          placeholder="请输入API KEY"
        />

        {/* 模型描述 */}
        <Form.TextArea
          style={{ height: 120 }}
          field="description"
          label="模型描述"
          placeholder="请输入模型描述内容"
        />
      </Form>
    </SideSheet>
  );
};

export default ModelForm;
