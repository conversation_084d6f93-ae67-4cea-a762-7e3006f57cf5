export interface ThemeConfig {
    primary: string;
    isDark: boolean;
    weakOrGray: string;
  }
  
  export interface ThemeSliceState {
    themeConfig: ThemeConfig;
    setDark: (isDark: boolean) => void;
    setPrimaryColor: (color: string) => void;
    setWeakOrGray: (value: string) => void;
  }
  
export const createThemeSlice = (set: any, get: any): ThemeSliceState => ({
themeConfig: {
    primary: "#1890ff",
    isDark: false,
    weakOrGray: "",
},

setDark: (isDark) =>
    set((state: any) => ({
    themeConfig: {
        ...state.themeConfig,
        isDark,
    },
    })),

setPrimaryColor: (color) =>
    set((state: any) => ({
    themeConfig: {
        ...state.themeConfig,
        primary: color,
    },
    })),

setWeakOrGray: (weakOrGray) =>
    set((state: any) => ({
    themeConfig: {
        ...state.themeConfig,
        weakOrGray,
    },
    })),
});