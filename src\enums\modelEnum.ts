export const models = {
  DashScope: [
    {
      label: "qwen-plus",
      value: "qwen-plus",
      type: "LLM",
    },
    {
      label: "qwen-turbo",
      value: "qwen-turbo",
      type: "LLM",
    },
    {
      label: "qwen-max",
      value: "qwen-max",
      type: "LLM",
    },
    {
      label: "qwen-max-longcontext",
      value: "qwen-max-longcontext",
      type: "LLM",
    },
    {
      label: "deepseek-r1",
      value: "deepseek-r1",
      type: "LLM",
    },
    {
      label: "deepseek-v3",
      value: "deepseek-v3",
      type: "LLM",
    },
    {
      label: "text-embedding-v1",
      value: "text-embedding-v1",
      type: "Text Embedding",
    },
    {
      label: "text-embedding-v2",
      value: "text-embedding-v2",
      type: "Text Embedding",
    },
    {
      label: "text-embedding-v3",
      value: "text-embedding-v3",
      type: "Text Embedding",
    },
  ],
  OpenAI: [
    {
      label: "gpt-3.5-turbo",
      value: "gpt-3.5-turbo",
    },
    {
      label: "gpt-4o",
      value: "gpt-4o",
    },
    {
      label: "gpt-4o-mini",
      value: "gpt-4o-mini",
    },
    {
      label: "gpt-4o",
      value: "gpt-4o",
    },
  ],
  DeepSeek: [
    {
      label: "deepseek-r1",
      value: "deepseek-r1",
    },
    {
      label: "deepseek-v3",
      value: "deepseek-v3",
    },
  ],
};
