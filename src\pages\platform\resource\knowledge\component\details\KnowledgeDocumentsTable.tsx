import EmtpyBox from "@/components/icon/EmtpyBox";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import { Table, Empty, Space } from "@douyinfe/semi-ui";

interface Props {
  columns: any[];
  dataSource: any[];
  loading: boolean;
  clasName?: any;
  onRow?: any;
  pagination: {
    currentPage: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (size: number) => void;
  } | boolean;
  rowSelection?: any;
}

export default function KnowledgeDocumentsTable({
  columns,
  dataSource,
  clasName,
  loading,
  pagination,
  onRow,
  rowSelection,
}: Props) {
  return (
    <div className={clasName}>
      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={dataSource}
        loading={loading}
        size="small"
        onRow={onRow}
        scroll={{ y: 600, x: 100 }}
        pagination={pagination}
        rowKey="id"
        empty={
          <Empty
            image={<EmtpyBox />}
            description="暂无数据"
          />
        }
      />
    </div>
  );
}
