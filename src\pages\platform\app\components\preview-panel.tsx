import React from "react";
import ChatComponent from "./chat";
import { Button, Icon, Toast, Tooltip } from "@douyinfe/semi-ui";
import { MessageCircleOff, Trash, Trash2 } from "lucide-react";
import { clearMessages, useFeatchStream } from "@/store/modules/useFeatchStream";

interface PreviewPanelProps {
  botId: number;
  welcomeText: string;
  enabledWelcome: boolean;
}

const PreviewPanel: React.FC<PreviewPanelProps> = ({
  botId,
  welcomeText,
  enabledWelcome,
}) => {
  // 清空消息的处理函数
  const handleClearMessages = () => {
    clearMessages(botId);
    
    // 如果启用了欢迎消息，清空后重新添加系统消息
    if (enabledWelcome && welcomeText) {
      useFeatchStream.getState().addSystemMessage(welcomeText, botId);
    }
  };

  return (
    <div className="flex flex-col bg-semi-color-white relative w-[50%] gap-2 h-full overflow-y-auto overflow-x-hidden rounded-md p-4">
      <div className="flex justify-between items-center">
        <div className="font-semibold text-lg sticky top-0">调试与预览</div>
        <Tooltip content="清空对话">
          <Button
            theme="borderless"
            type="danger"
            icon={<Icon svg={<Trash size="1em" />}  />}
            aria-label="清空对话"
            onClick={handleClearMessages}
          ></Button>
        </Tooltip>
      </div>
      <div
        className="w-full flex-1 flex flex-col overflow-hidden rounded-lg"
        style={{ height: "calc(100% - 100px)" }}
      >
        <ChatComponent
          botId={botId}
          welcomeText={welcomeText}
          enabledWelcome={enabledWelcome}
        />
      </div>
    </div>
  );
};

export default PreviewPanel;
