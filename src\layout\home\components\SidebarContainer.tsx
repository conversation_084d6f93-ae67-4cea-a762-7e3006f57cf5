// import { FC } from "react";
// import LayoutSider from "./Sider";

// interface SidebarContainerProps {
//   subMenus: MenuProps[];
//   activePath: string[];
//   onMenuSelect: (data: any) => void;
//   onActivePathChange: (newPath: string[]) => void; // Added prop
//   children: React.ReactNode;
// }

// const SidebarContainer: FC<SidebarContainerProps> = ({
//   subMenus,
//   activePath,
//   onMenuSelect,
//   onActivePathChange,
//   children,
// }) => {
//   return (
//     <div className="flex-1 flex flex-row overflow-hidden">
//       {subMenus.length > 0 && (
//         <LayoutSider
//           menus={subMenus}
//           activekeys={activePath}
//           setActivekeys={onActivePathChange} // Use prop callback
//           onClick={onMenuSelect}
//         />
//       )}
//       <div className="flex-1 rounded-lg">{children}</div>
//     </div>
//   );
// };

// export default SidebarContainer;
