import { getNotice } from "@/api/system/notice";
import { MarkdownBox } from "@/components/Markdown/MarkdownBox";
import { IconArrowLeft, IconChevronLeft } from "@douyinfe/semi-icons";
import { Button, Spin } from "@douyinfe/semi-ui";
import { useParams, useRouter } from "@tanstack/react-router";
import React, { useEffect } from "react";

export default function NoticePreview() {
  const { id } = useParams({ strict: false });
  //   const [content, setContent] = React.useState("");
  const [data, setData] = React.useState<any>({});
  const [spinning, setSpinning] = React.useState<any>(true);
  const { history } = useRouter();

  useEffect(() => {
    getNoticeDetails();
  }, []);
  const getNoticeDetails = () => {
    setSpinning(true);
    getNotice(id)
      .then((res) => {
        setData(res?.data);
      })
      .finally(() => {
        setSpinning(false);
      });
  };

  const handleGoBack = () => {
    history.go(-1);
  };

  return (
    <div className="px-2 py-2 w-full h-full relative">
      {spinning && (
        <div className="w-full h-full flex justify-center items-center">
          <Spin
            tip="正在加载..."
            style={{ width: "200px" }}
            spinning={spinning}
          ></Spin>
        </div>
      )}
      {!spinning && (
        <div className="px-4 py-4 bg-semi-color-white gap-4 flex h-full w-full flex-col rounded-md box-border shadow-sm">
          <div>
            <Button
              onClick={handleGoBack}
              icon={<IconChevronLeft />}
              type="tertiary"
            >
              返回
            </Button>
          </div>
          <div className="w-full text-center text-3xl font-semibold">
            {data.noticeTitle}
          </div>
          <div className="flex-1 px-[10%] overflow-y-auto">
            <MarkdownBox content={data.noticeContent} />
            {/* <div dangerouslySetInnerHTML={{ __html: data.noticeContent }}></div> */}
          </div>
        </div>
      )}
    </div>
  );
}
