import React from "react";
import useSyncState from "./useSyncState";
import { fetchEventSource } from "@/components/fetch-event-source";
import useEvent from "./useEvent";

function useSSE(
  url: string,
  options: {
    method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
    onMessage?: (event: MessageEvent) => void;
    onError?: (error: any) => void;
    headers?: Record<string, string>;
    openWhen?: boolean;
  } = {}
) {
  const {
    onMessage,
    onError,
    headers = {},
    openWhen = true,
    method = "POST",
  } = options;
  const [isOpen, setIsOpen, getIsOpen] = useSyncState(false);
  const [params, setParams] = React.useState<Record<string, string | number>>({});
  const finalUrl = React.useRef<string | null>(null);
  const abortControllerRef = React.useRef<AbortController | null>(null);

  const memoizedOnMessage = useEvent(onMessage as any);
  const memoizedOnError = useEvent(onError as any);

  const updateFinalUrl = React.useCallback(
    (newParams: Record<string, string | number>) => {
      try {
        const baseUrl = window.location.origin;
        const fullUrl = new URL(url, baseUrl).toString();
        const urlObj = new URL(fullUrl);
        Object.entries(newParams).forEach(([key, value]) => {
          urlObj.searchParams.append(key, String(value));
        });
        finalUrl.current = urlObj.toString();
      } catch (error) {
        console.error("Error constructing URL:", error);
        setIsOpen(false);
        memoizedOnError?.(error);
      }
    },
    [url, memoizedOnError]
  );

  React.useEffect(() => {
    updateFinalUrl(params);
  }, [params, updateFinalUrl]);

  React.useEffect(() => {
    if (!openWhen || !getIsOpen() || !finalUrl.current) return;

    const source = new AbortController();
    abortControllerRef.current = source;

    const fetchSSE = async () => {
      try {
        await fetchEventSource(finalUrl?.current, {
          method,
          headers,
          openWhenHidden: true,
          onmessage: (event) => {
            memoizedOnMessage?.(event);
          },
          onerror: (error) => {
            console.error("SSE error:", error);
            setIsOpen(false);
            memoizedOnError?.(error);
          },
          onclose: () => {
            console.log("SSE connection closed");
            setIsOpen(false);
          },
        });
      } catch (error) {
        if (error?.name === 'AbortError') {
          console.log('SSE connection aborted');
        } else {
          console.error("Unexpected SSE error:", error);
          setIsOpen(false);
          memoizedOnError?.(error);
        }
      }
    };

    fetchSSE();

    return () => {
      if (source) {
        source.abort();
        setIsOpen(false);
      }
    };
  }, [
    finalUrl,
    headers,
    openWhen,
    getIsOpen,
    memoizedOnMessage,
    memoizedOnError,
  ]);

  return {
    isOpen: getIsOpen(),
    close: () => {
      console.log('Closing SSE connection...');
      setIsOpen(false);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    },
    open: (newParams: Record<string, string | number>) => {
      console.log('Opening SSE connection with params:', newParams);
      // 强制更新 finalUrl
      updateFinalUrl(newParams);
      // 更新 params 状态
      setParams(newParams);
      // 确保先关闭之前的连接
      setIsOpen(false);
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
      // 开启新的连接
      setTimeout(() => {
        setIsOpen(true);
      }, 0);
    },
  };
}

export default useSSE;
    