import { http } from "@/utils/axios";

const base_url = "resource/oss/config";

export function getOssConfigList(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function getOssConfig(id: string) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}

export function addOssConfig(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}

export function editOssConfig(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}

export function delOssConfig(id: string | number) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${id}`,
    method: "DELETE",
  });
}

export function changeOssConfigStatus(data:any) {
  return http.request<API.Result<any>>({
    url: `${base_url}/changeStatus`,
    method: "PUT",
    data
  });
}