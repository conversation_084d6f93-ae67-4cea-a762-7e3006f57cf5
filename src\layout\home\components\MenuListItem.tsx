// import { FC } from "react";
// import classNames from "classnames";

// interface MenuListItemProps {
//   menu: MenuProps;
//   activeKey: string;
//   onClick: (menu: MenuProps) => void;
// }

// const MenuListItem: FC<MenuListItemProps> = ({ menu, activeKey, onClick }) => {
//   return (
//     <div
//       onClick={() => onClick(menu)}
//       className={classNames(
//         "flex flex-col items-center justify-center cursor-pointer rounded-md",
//         "w-[40px] h-[40px] gap-1 text-[12px]",
//         activeKey === menu.key
//           ? "bg-semi-color-white text-semi-color-text-0"
//           : "text-semi-color-text-2 hover:bg-semi-color-fill-0"
//       )}
//     >
//       <div>{menu.icon}</div>
//       {/* <div className="text-nowrap">{menu.label}</div> */}
//     </div>
//   );
// };

// export default MenuListItem;
