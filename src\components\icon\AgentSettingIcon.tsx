import React from 'react'

export default function AgentSettingIcon(props:any) {
  return (
  <svg {...props} t="1744362680939" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12257" width="1em" height="1em"><path d="M615.204 805.592c-7.726 0-15.022-2.204-21.684-6.544l-26.914-17.542C550.322 770.89 531.44 765.28 512 765.28s-38.322 5.612-54.608 16.226l-26.912 17.542c-6.662 4.342-13.958 6.542-21.688 6.544-15.696 0.002-34.688-9.952-39.294-31.804l-6.626-31.434c-8.166-38.748-38.48-69.06-77.226-77.226l-31.432-6.626c-13.114-2.764-23.578-11.452-28.706-23.834-5.128-12.38-3.874-25.922 3.446-37.148l17.542-26.912c21.624-33.174 21.624-76.042 0-109.214l-17.542-26.912c-7.32-11.228-8.576-24.768-3.446-37.15s15.592-21.068 28.706-23.834l31.434-6.626c38.746-8.166 69.058-38.48 77.226-77.226l6.626-31.434c4.606-21.852 23.6-31.806 39.298-31.806 7.726 0 15.022 2.202 21.684 6.546l26.912 17.542c16.286 10.616 35.168 16.226 54.608 16.226s38.322-5.61 54.608-16.226l26.912-17.542c6.662-4.344 13.958-6.546 21.684-6.546 15.7 0 34.692 9.954 39.298 31.806l6.626 31.434c8.166 38.746 38.478 69.058 77.224 77.226l31.434 6.626c13.116 2.764 23.578 11.452 28.708 23.834s3.874 25.922-3.446 37.15l-17.542 26.912c-21.624 33.172-21.624 76.042 0 109.214l17.542 26.912c7.318 11.226 8.574 24.768 3.446 37.148-5.128 12.382-15.592 21.07-28.708 23.834l-31.434 6.626c-38.746 8.166-69.058 38.48-77.224 77.226l-6.626 31.434c-4.61 21.85-23.602 31.804-39.3 31.804z" fill="#4988FD" p-id="12258"></path><path d="M615.204 256.408c-1.862 0-3.546 0.534-5.302 1.678l-26.912 17.542a129.684 129.684 0 0 1-70.988 21.094 129.69 129.69 0 0 1-70.99-21.094L414.1 258.086c-1.756-1.144-3.44-1.678-5.302-1.678-2.856 0-8.612 1.67-9.944 7.994l-6.626 31.432c-10.618 50.37-50.024 89.778-100.394 100.394l-31.434 6.626c-3.422 0.722-5.836 2.726-7.176 5.958-1.338 3.232-1.048 6.356 0.862 9.288l17.542 26.912c28.11 43.124 28.11 98.854 0 141.978l-17.542 26.912c-1.91 2.932-2.2 6.056-0.862 9.288s3.752 5.238 7.176 5.958l31.434 6.626c50.372 10.618 89.778 50.024 100.394 100.394l6.626 31.434c1.332 6.32 7.086 7.992 9.944 7.992 1.862 0 3.546-0.534 5.302-1.678l26.912-17.542a129.686 129.686 0 0 1 70.99-21.096 129.684 129.684 0 0 1 70.988 21.094l26.912 17.542c1.756 1.144 3.442 1.678 5.302 1.678 2.856 0 8.612-1.67 9.944-7.992l6.626-31.434c10.618-50.372 50.024-89.778 100.394-100.394l31.434-6.626c3.424-0.72 5.838-2.726 7.176-5.958 1.338-3.23 1.048-6.356-0.862-9.288l-17.542-26.912c-28.11-43.124-28.11-98.854 0-141.978l17.542-26.912c1.91-2.932 2.2-6.056 0.862-9.288s-3.752-5.236-7.178-5.958l-31.432-6.626c-50.37-10.618-89.778-50.024-100.394-100.394l-6.626-31.432c-1.334-6.32-7.09-7.992-9.944-7.992m-206.408-60c12.912 0 26.084 3.604 38.066 11.414l26.912 17.542a69.93 69.93 0 0 0 38.224 11.358 69.926 69.926 0 0 0 38.224-11.358l26.912-17.542c11.978-7.808 25.158-11.414 38.066-11.414 31.514 0 61.454 21.46 68.652 55.618l6.626 31.434a69.998 69.998 0 0 0 54.056 54.058l31.434 6.626c48.15 10.148 71.076 65.498 44.208 106.72l-17.542 26.912a70 70 0 0 0 0 76.45l17.542 26.912c26.87 41.222 3.942 96.572-44.208 106.72l-31.434 6.626a69.992 69.992 0 0 0-54.056 54.058l-6.626 31.432c-7.198 34.156-37.14 55.62-68.652 55.62-12.91 0-26.086-3.604-38.066-11.414l-26.912-17.542c-11.616-7.572-24.92-11.36-38.224-11.36s-26.608 3.788-38.224 11.36l-26.912 17.542c-11.978 7.808-25.158 11.414-38.066 11.414-31.514 0-61.454-21.46-68.654-55.62l-6.626-31.432a69.992 69.992 0 0 0-54.058-54.058l-31.434-6.626c-48.15-10.15-71.076-65.498-44.206-106.72l17.542-26.912a70 70 0 0 0 0-76.45l-17.542-26.912c-26.87-41.222-3.944-96.572 44.206-106.72l31.434-6.626a69.998 69.998 0 0 0 54.058-54.058l6.626-31.434c7.2-34.154 37.144-55.616 68.654-55.618z" fill="#4988FD" p-id="12259"></path><path d="M400 516a112 112 0 1 0 224 0 112 112 0 1 0-224 0z" fill="#DFECFD" p-id="12260"></path><path d="M512 628c-61.756 0-112-50.242-112-112 0-61.756 50.244-112 112-112 61.758 0 112 50.244 112 112 0 61.758-50.242 112-112 112z m0-164c-28.672 0-52 23.328-52 52 0 28.674 23.328 52 52 52 28.674 0 52-23.326 52-52 0-28.672-23.326-52-52-52z" fill="#DFECFD" p-id="12261"></path></svg>
  )
}
