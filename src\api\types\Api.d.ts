declare namespace API {
  export interface Result<T = any> {
    code: number;
    msg: string;
    data: T;
  }

  export interface TalbeDataInfo<T = any> {
    total: number
    rows: T[]
    code:number
    msg:string
  }
  export interface Stats {
    error: number;
    success: number;
  }
  export interface BaseEntity{
    createdBy?: any;
    createdDept?: any;
    createdAt?: string;
    updatedBy?: any;
    updatedAt?: any;
  }

  export interface CaptchaConfig {
    'sys.captcha.type': string;
    'sys.captcha.enable': string;
    'sys.captcha.graph.length': string;
    'sys.captcha.graph.type': string;
  }
}
