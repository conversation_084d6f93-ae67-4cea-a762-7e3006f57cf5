interface SiderProps {
  // jumpPage?: (key: string) => void;
  // enableBack?: boolean;
  // activekeys: Array<string>;
  // menus: Array<MenuProps>;
  // onClick: (data: any) => void;
  // setActivekeys: (keys: Array<string>) => any;
}
declare const strings: {
  readonly POSITION_SET: readonly [
    "top",
    "topLeft",
    "topRight",
    "left",
    "leftTop",
    "leftBottom",
    "right",
    "rightTop",
    "rightBottom",
    "bottom",
    "bottomLeft",
    "bottomRight",
    "leftTopOver",
    "rightTopOver",
    "leftBottomOver",
    "rightBottomOver",
  ];
};
interface MenuProps {
  key: string;
  label: string;
  icon: React.ReactNode;
  children?: MenuProps[];
}

interface SiderAvatarProps {
  className?: string;
  position?: ArrayElement<typeof strings.POSITION_SET>;
  collapsed?: boolean;
}
