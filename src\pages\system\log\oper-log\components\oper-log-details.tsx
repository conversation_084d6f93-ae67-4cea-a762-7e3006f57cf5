import DictTag from "@/components/DictTag";
import {
  But<PERSON>,
  Col,
  Descriptions,
  Divider,
  JsonViewer,
  Modal,
  Row,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState } from "react";

export default function OperationLogDetails({
  open,
  onCancel,
  data,
  status,
}: any) {
  const [operateData, setOperateData] = useState<any>({});
  const resultRef = React.createRef<any>();
  const requestRef = React.createRef<any>();
  // const [oneData, setOneData] = useState<any>([]);
  // const [twoData, setTwoData] = useState<any>({});

  // useEffect(() => {
  //   console.log(operateData);
  // }, [operateData]);

  useEffect(() => {
    if (data && open) {
      setOperateData(data);
    }
  }, [data]);

  return (
    <>
      <Modal
        title="日志详情"
        width={"50%"}
        className="semi-light-scrollbar"
        onCancel={onCancel}
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          minHeight: "500px",
          overflow: "auto",
        }}
        visible={open}
        footer={
          <Button type="tertiary" onClick={onCancel}>
            关闭
          </Button>
        }
        centered
      >
        <Row className="w-full" gutter={2}>
          <Col span={20}>
            <Descriptions align="plain" layout="horizontal" column={2}>
              <Descriptions.Item itemKey="操作人">
                {operateData?.operName}
              </Descriptions.Item>
              <Descriptions.Item itemKey="操作部门">
                {operateData?.deptName}
              </Descriptions.Item>
              <Descriptions.Item itemKey="操作类别">
                {operateData?.operatorType}
              </Descriptions.Item>
              <Descriptions.Item itemKey="业务名称">
                {operateData?.title}
              </Descriptions.Item>
              <Descriptions.Item itemKey="操作时间">
                {operateData?.operTime}
              </Descriptions.Item>
              <Descriptions.Item itemKey="请求IP">
                {operateData?.operIp}
              </Descriptions.Item>
              <Descriptions.Item itemKey="操作位置" span={2}>
                {operateData?.operLocation}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={4}>
            <div className="flex flex-col items-end justify-center">
              <div>请求状态</div>
              <div className="text-semi-color-primary text-[20px] font-bold">
                <div className="">
                  <DictTag
                    type="tag"
                    dictType="sys_oper_status"
                    dictValue={operateData?.status}
                    dictionaryData={status}
                  />
                </div>
              </div>
            </div>
          </Col>
        </Row>
        <Divider margin="12px" />
        <Row className="w-full" gutter={2}>
          <Col span={20}>
            <Descriptions align="plain" layout="horizontal" column={1}>
              <Descriptions.Item itemKey="请求地址">
                {operateData?.operUrl}
              </Descriptions.Item>
              <Descriptions.Item itemKey="执行时间">
                {operateData?.costTime} ms
              </Descriptions.Item>
              <Descriptions.Item itemKey="执行方法">
                {operateData?.method}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col span={4}>
            <div className="flex flex-col items-end justify-center">
              <div>请求方法</div>
              <div className="text-semi-color-danger-active text-[20px] font-bold">
                {operateData?.requestMethod}
              </div>
            </div>
          </Col>
        </Row>
        <Divider margin="12px" />
        <div className="flex gap-1 flex-col">
          <div className="text-[14px] font-bold">请求参数</div>
          <div>
            <JsonViewer
              height={100}
              ref={requestRef}
              value={
                operateData?.operParam
                  ? JSON.stringify(JSON.parse(operateData?.operParam), null, 2)
                  : `{}`
              }
              width={"100%"}
              showSearch={false}
              options={{
                // readOnly: true,
                autoWrap: true,
                formatOptions: { tabSize: 4, insertSpaces: true, eol: "\n" },
              }}
            />
          </div>
        </div>
        <Divider margin="12px" />
        <div className="flex gap-1 flex-col">
          <div className="text-[14px] font-bold">响应参数</div>
          <div>
            <JsonViewer
              ref={resultRef}
              height={100}
              value={
                operateData?.jsonResult
                  ? JSON.stringify(JSON.parse(operateData?.jsonResult), null, 2)
                  : `{}`
              }
              width={"100%"}
              showSearch={false}
              options={{
                // readOnly: true,
                autoWrap: true,
                formatOptions: { tabSize: 4, insertSpaces: true, eol: "\n" },
              }}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}
