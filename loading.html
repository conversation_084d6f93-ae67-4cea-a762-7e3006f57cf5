<style data-app-loading="inject-css">
  #__app-loading__.hidden {
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.33, 1, 0.68, 1);
  }
  
  .loading {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #ffffff;
  }
  
  .loading.hidden {
    pointer-events: none;
    visibility: hidden;
    opacity: 0;
    transition: all 0.8s cubic-bezier(0.33, 1, 0.68, 1);
  }
  
  .dark .loading {
    background: #000000;
  }
  
  .loading-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 40px;
  }
  
  /* 苹果风格的加载动画 */
  .apple-loader {
    position: relative;
    width: 50px;
    height: 50px;
  }
  
  .apple-loader svg {
    animation: rotate 2s linear infinite;
    transform-origin: center center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  
  .apple-loader circle {
    stroke: #0066CC;
    stroke-width: 4;
    stroke-dasharray: 150, 200;
    stroke-dashoffset: -10;
    stroke-linecap: round;
    fill: none;
    animation: dash 1.5s ease-in-out infinite;
  }
  
  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes dash {
    0% {
      stroke-dasharray: 1, 200;
      stroke-dashoffset: 0;
    }
    50% {
      stroke-dasharray: 89, 200;
      stroke-dashoffset: -35;
    }
    100% {
      stroke-dasharray: 89, 200;
      stroke-dashoffset: -124;
    }
  }
  
  /* 标题样式 */
  .title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
    margin: 0;
    letter-spacing: -0.5px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  }
  
  .subtitle {
    font-size: 16px;
    color: #86868b;
    margin-top: 8px;
    font-weight: 400;
    letter-spacing: -0.1px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  }
  
  .dark .title {
    color: #f5f5f7;
  }
  
  .dark .subtitle {
    color: #a1a1a6;
  }
  
  /* 底部装饰 */
  .bottom-decoration {
    position: absolute;
    bottom: 32px;
    font-size: 12px;
    color: #86868b;
    font-weight: 400;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  }
  
  .dark .bottom-decoration {
    color: #a1a1a6;
  }
  
  /* 背景装饰 */
  .bg-decoration {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: -1;
    opacity: 0.5;
  }
  
  .bg-blur {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
  }
  
  .bg-blur-1 {
    top: 20%;
    right: 15%;
    width: 300px;
    height: 300px;
    background: rgba(0, 102, 204, 0.1);
  }
  
  .bg-blur-2 {
    bottom: 10%;
    left: 15%;
    width: 250px;
    height: 250px;
    background: rgba(0, 102, 204, 0.08);
  }
  
  .dark .bg-blur-1 {
    background: rgba(10, 132, 255, 0.15);
  }
  
  .dark .bg-blur-2 {
    background: rgba(10, 132, 255, 0.1);
  }
</style>

<div id="__app-loading__" class="loading">
  <div class="bg-decoration">
    <div class="bg-blur bg-blur-1"></div>
    <div class="bg-blur bg-blur-2"></div>
  </div>
  
  <div class="loading-wrap">
    <div class="apple-loader">
      <svg viewBox="0 0 50 50">
        <circle cx="25" cy="25" r="20"></circle>
      </svg>
    </div>
  </div>
  
  <div class="title-container">
    <h1 class="title"><%= VITE_APP_TITLE %></h1>
    <p class="subtitle">正在准备应用...</p>
  </div>
  
  <div class="bottom-decoration">
    © <%= new Date().getFullYear() %> LynkzHub
  </div>
  
