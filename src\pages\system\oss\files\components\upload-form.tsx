import React, { useState } from "react";
import { Modal, Upload, Button, Toast } from "@douyinfe/semi-ui";
import { IconUpload } from "@douyinfe/semi-icons";
import { ResultEnum } from "@/enums/httpEnum";
import { getAccessToken } from "@/utils/auth";
import { useMediaQuery } from 'react-responsive';
interface UploadFormProps {
  visible: boolean;
  onClose: () => void;
  onUploadSuccess: (url: string, fileName: string, ossId: string) => void;
}

const UploadForm: React.FC<UploadFormProps> = ({
  visible,
  onClose,
  onUploadSuccess,
}) => {
  const [fileList, setFileList] = useState<any[]>([]);
  // 使用 useMediaQuery 来根据页面宽度设置不同的弹窗宽度
  const isMobile = useMediaQuery({ maxWidth: 767 });
  const isSmallScreen = useMediaQuery({ minWidth: 768, maxWidth: 1023 });
  const modalWidth = isMobile || isSmallScreen ? '90%' : '50%';
  return (
    <Modal
      title="上传文件"
      visible={visible}
      width={modalWidth}
      centered
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <Upload
        defaultFileList={fileList}
        draggable={true}
        limit={5}
        action={
          location.hash +
          import.meta.env.VITE_GLOB_API_URL_PREFIX +
          "/resource/oss/upload"
        }
        headers={{
          clientid: import.meta.env.VITE_GLOB_CLIENT_ID,
          Authorization: `Bearer ${getAccessToken()}`,
        }}
        fileName="file"
        afterUpload={({ response, file }) => {
          console.log("上传响应:", response); // 调试日志
          if (response.code == ResultEnum.SUCCESS) {
            const { data } = response;
            Toast.success("上传成功");
            return {
              autoRemove: false,
              status: "success",
              name: data.fileName,
              url: data.url,
              ...data,
            };
          } else {
            Toast.error(response.data.msg)
          }
          return {};
        }}
        multiple
        maxSize={1024 * 1024 * 10} // 10MB
        dragMainText={"点击上传文件或拖拽文件到这里"}
        dragSubText="支持任意类型文件"
      />
    </Modal>
  );
};

export default UploadForm;
