import { TOKEN_DATA_KEY } from "@/enums/key";
import { storage } from "./customStorage";
import { useAuthStore } from "@/store";
// token 的一些操作

export const getTokenData = () => {
   const { tokenData } = useAuthStore.getState();
    return tokenData;
};

export function getAccessToken() {
 const tokenData =  getTokenData();
  return tokenData?.access_token || null;
}

// export const getExpireIn = () => {
//   const tokenData = storage.get(TOKEN_DATA_KEY);
//   return tokenData.expire_in;
// };

// // export const getRefreshToken = () => {
// //   const tokenData = storage.get(TOKEN_DATA_KEY);
// //   return tokenData?.refresh_token;
// // };

// export const removeTokenData = () => {
//   storage.remove(TOKEN_DATA_KEY);
// };


export const getClientId = () => {
 const tokenData =  getTokenData();
  // const tokenData = storage.get(TOKEN_DATA_KEY);
  return tokenData?.client_id;
};