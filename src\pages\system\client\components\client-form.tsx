import { addClient, editClient, getClient } from "@/api/system/client";
import { useBoolean } from "@/hooks";
import { generateClientKey, generateClientSecurity } from "@/utils";
import { IconIssueStroked } from "@douyinfe/semi-icons";
import {
  Button,
  Col,
  Form,
  Modal,
  Toast,
  Row,
  Spin,
  Tooltip,
} from "@douyinfe/semi-ui";
import React, { useEffect } from "react";

export default function ClientForm({
  onCancel,
  open,
  onRefresh,
  title,
  statuss,
  grantTypes,
  deviceTypes,
  id,
}: any) {
  const formApiRef = React.useRef(null);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const [spinning, { setTrue: openSpinning, setFalse: closeSpinning }] =
    useBoolean(false);
  const handleReset = () => {
    // @ts-expect-error
    formApiRef.current?.reset();
    onCancel();
  };
  const handleCancel = () => {
    onCancel();
    onRefresh();
  };
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const handleAddSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addClient({
        ...values,
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const handleEditSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await editClient({
        ...values,
        id,
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const submitForm = async () => {
    try {
      // @ts-expect-error
      const values = await formApiRef.current?.validate();
      id ? await handleEditSubmit(values) : await handleAddSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };
  const getData = async () => {
    try {
      openSpinning();
      const { data } = await getClient(id);
      // @ts-expect-error
      formApiRef.current?.setValues(data);
      closeSpinning();
    } catch (err) {
      closeSpinning();
      console.error(err);
    }
  };
  useEffect(() => {
    if (id) {
      getData();
    }
  }, [open, id]);
  const genKey = async () => {
    const clientKey = await generateClientKey();
    const clientSecurity = await generateClientSecurity();
    // @ts-expect-error
    formApiRef.current?.setValue("clientKey", clientKey);
    // @ts-expect-error
    formApiRef.current?.setValue("clientSecret", clientSecurity);
    // model.clientKey = clientKey;
    // model.clientSecret = clientSecurity;
  };
  return (
    <Modal
      title={title}
      width={750}
      centered
      visible={open}
      onCancel={handleReset}
      closeOnEsc={false}
      footer={
        <div>
          <Button onClick={handleReset}>取消</Button>
          <Button type="secondary" theme="solid" onClick={genKey}>
            秘钥生成
          </Button>
          <Button theme="solid" loading={loading} onClick={submitForm}>
            提交
          </Button>
        </div>
      }
    >
      <Spin tip="正在加载..." spinning={spinning}>
        <Form
          className="w-full"
          getFormApi={getFormApi}
          wrapperCol={{ span: 24 }}
        >
          <Row gutter={[10, 10]}>
            <Col span={12}>
              <Form.Input
                field="clientKey"
                rules={[{ required: true, message: "客户端key不能为空" }]}
                label="客户端KEY"
                placeholder={"请输入客户端KEY"}
                required
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="clientSecret"
                rules={[{ required: true, message: "客户端秘钥不能为空" }]}
                label="客户端秘钥"
                placeholder={"请输入客户端秘钥"}
                required
              />
            </Col>
            <Col span={12}>
              <Form.InputNumber
                field="activeTimeout"
                className="w-full"
                initValue={"1800"}
                min={0}
                label={{
                  text: "Token活跃超时时间",
                  extra: (
                    <Tooltip content="指定时间无操作则过期（单位：秒），默认30分钟（1800秒)">
                      <IconIssueStroked className="text-semi-color-text-2" />
                    </Tooltip>
                  ),
                }}
                max={99999999}
                rules={[{ required: true, message: "请输入Token活跃超时时间" }]}
                placeholder={"请输入Token活跃超时时间"}
                required
              />
            </Col>
            <Col span={12}>
              <Form.InputNumber
                field="timeout"
                className="w-full"
                initValue={"604800"}
                label={{
                  text: "Token固定超时",
                  extra: (
                    <Tooltip content="指定时间必定过期（单位：秒），默认七天（604800秒）">
                      <IconIssueStroked className="text-semi-color-text-2" />
                    </Tooltip>
                  ),
                }}
                min={0}
                max={99999999}
                extraText=""
                rules={[{ required: true, message: "请输入token固定超时" }]}
                placeholder={"请输入token固定超时"}
                required
              />
            </Col>
            <Col span={12}>
              <Form.Select
                field="deviceType"
                className="w-full"
                showClear={true}
                rules={[{ required: true, message: "请选择设备类型" }]}
                placeholder={"请选择设备类型"}
                label="设备类型"
              >
                {deviceTypes?.map((item: any, index: number) => {
                  return (
                    <Form.Select.Option key={index} value={item.dictValue}>
                      {item.dictLabel}
                    </Form.Select.Option>
                  );
                })}
              </Form.Select>
            </Col>
            <Col span={12}>
              <Form.Select
                field="grantTypeList"
                className="w-full"
                showClear={true}
                multiple
                rules={[{ required: true, message: "请选择授权类型" }]}
                placeholder={"请选择授权类型"}
                label="授权类型"
              >
                {grantTypes?.map((item: any, index: number) => {
                  return (
                    <Form.Select.Option key={index} value={item.dictValue}>
                      {item.dictLabel}
                    </Form.Select.Option>
                  );
                })}
              </Form.Select>
            </Col>

            <Col span={24}>
              <Form.RadioGroup
                field="status"
                label="状态"
                initValue={"0"}
                rules={[{ required: true, message: "必须选择是否独占 " }]}
              >
                {statuss?.map((item: any, index: number) => {
                  return (
                    <Form.Radio value={item.dictValue} key={index}>
                      {item.dictLabel}
                    </Form.Radio>
                  );
                })}
              </Form.RadioGroup>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
}
