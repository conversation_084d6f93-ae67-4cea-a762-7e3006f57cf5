import React from "react";

export default function ErrorIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="10em"
      height="10em"
      viewBox="0 0 200 200"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      {/* 背景元素 */}
      <rect x="30" y="40" width="140" height="120" rx="8" fill="#F9FAFB">
        <animate
          attributeName="opacity"
          values="0.8;1;0.8"
          dur="3s"
          repeatCount="indefinite"
        />
      </rect>
      <rect x="40" y="50" width="120" height="20" rx="4" fill="#E5E7EB" />
      <rect x="40" y="80" width="120" height="70" rx="4" fill="#EEF2FF" />
      
      {/* 错误状态指示 - 闪烁效果 */}
      <circle cx="150" cy="60" r="6" fill="#EF4444">
        <animate
          attributeName="opacity"
          values="0.3;1;0.3"
          dur="1.5s"
          repeatCount="indefinite"
        />
        <animate
          attributeName="r"
          values="5;6;5"
          dur="1.5s"
          repeatCount="indefinite"
        />
      </circle>
      
      {/* 断开的连接/错误线 - 移动效果 */}
      <path
        d="M70 115 L90 95 M110 115 L130 95"
        stroke="#6366F1"
        strokeWidth="6"
        strokeLinecap="round"
      >
        <animate
          attributeName="stroke-width"
          values="5;6;5"
          dur="2s"
          repeatCount="indefinite"
        />
      </path>
      <path
        d="M90 115 L110 95"
        stroke="#EF4444"
        strokeWidth="6"
        strokeLinecap="round"
        strokeDasharray="2 4"
      >
        <animate
          attributeName="stroke-dasharray"
          values="2 4;3 3;2 4"
          dur="2s"
          repeatCount="indefinite"
        />
        <animate
          attributeName="stroke-width"
          values="5;6;5"
          dur="2s"
          repeatCount="indefinite"
        />
      </path>
      
      {/* 错误符号 - 旋转和缩放效果 */}
      <g>
        <animateTransform
          attributeName="transform"
          type="rotate"
          from="0 100 140"
          to="360 100 140"
          dur="20s"
          repeatCount="indefinite"
        />
        <circle cx="100" cy="140" r="20" fill="#FEE2E2" />
        <path
          d="M92 132 L108 148 M108 132 L92 148"
          stroke="#EF4444"
          strokeWidth="4"
          strokeLinecap="round"
        >
          <animate
            attributeName="stroke-width"
            values="3;4;3"
            dur="2s"
            repeatCount="indefinite"
          />
        </path>
      </g>
      
      {/* 装饰元素 - 移动效果 */}
      <circle cx="30" cy="30" r="10" fill="#DBEAFE">
        <animate
          attributeName="cy"
          values="30;25;30"
          dur="3s"
          repeatCount="indefinite"
        />
      </circle>
      <circle cx="170" cy="170" r="15" fill="#DBEAFE">
        <animate
          attributeName="cy"
          values="170;175;170"
          dur="4s"
          repeatCount="indefinite"
        />
      </circle>
      
      {/* 箭头动画 */}
      <path
        d="M160 20 L170 30 L160 40"
        stroke="#6366F1"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <animate
          attributeName="d"
          values="M160 20 L170 30 L160 40;M158 20 L172 30 L158 40;M160 20 L170 30 L160 40"
          dur="2s"
          repeatCount="indefinite"
        />
      </path>
      <path
        d="M20 160 L30 170 L40 160"
        stroke="#6366F1"
        strokeWidth="3"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <animate
          attributeName="d"
          values="M20 160 L30 170 L40 160;M18 160 L32 170 L42 160;M20 160 L30 170 L40 160"
          dur="2s"
          repeatCount="indefinite"
        />
      </path>
      
      {/* 添加数据流动效果 */}
      <path
        d="M50 60 L70 60 L80 60 L100 60"
        stroke="#9CA3AF"
        strokeWidth="2"
        strokeDasharray="3 3"
      >
        <animate
          attributeName="stroke-dashoffset"
          values="0;12"
          dur="1s"
          repeatCount="indefinite"
        />
      </path>
      <path
        d="M50 65 L120 65"
        stroke="#9CA3AF"
        strokeWidth="2"
        strokeDasharray="3 3"
      >
        <animate
          attributeName="stroke-dashoffset"
          values="0;12"
          dur="1s"
          repeatCount="indefinite"
          begin="0.3s"
        />
      </path>
    </svg>
  );
}
