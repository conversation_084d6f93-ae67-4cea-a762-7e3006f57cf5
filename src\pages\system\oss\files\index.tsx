import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import {
  IconApps,
  IconDelete,
  IconEdit,
  IconListView,
  IconMore,
  IconSync,
} from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Select,
  Table,
  Image,
  Toast,
  Tooltip,
  Typography,
  Card,
  ButtonGroup,
  Row,
  Col,
  Pagination,
  Spin,
  Radio,
  RadioGroup,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState, useCallback } from "react";
import { useBoolean } from "@/hooks";
import { debounce } from "lodash-es";

import classNames from "classnames";
import { useTable } from "@/hooks/useTables";
import { getOssConfigList } from "@/api/system/oss-config";
import { getFileList } from "@/api/system/files";
import UploadForm from './components/upload-form';

export default function FileList() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_oss_config_enable",
    "sys_oss_config_access",
  ]);
  const [ossEnableOptions, setOssEnableOptions] = useState([]);
  const [viewMode, setViewMode] = useState<"list" | "card">("list"); // 新增视图模式状态

  useEffect(() => {
    setTimeout(() => {
      loadDictionary();
    }, 0);
  }, []);

  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getFileList,
    params: {},
    columns: [
      {
        title: "文件名",
        dataIndex: "fileName",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "原名",
        dataIndex: "originalName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "后缀",
        dataIndex: "fileSuffix",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "上传人",
        dataIndex: "createdByName",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "服务商",
        dataIndex: "service",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "上传时间",
        dataIndex: "createdAt",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button theme="borderless">预览</Button>
              <Dropdown
                className="min-w-[120px]"
                zIndex={1000}
                position="bottom"
                trigger={"click"}
                render={
                  <Dropdown.Menu>
                    <Dropdown.Item
                      type="danger"
                      icon={<IconDelete />}
                    // onClick={() => handleRemoveDictData(row.dictCode)}
                    >
                      删除
                    </Dropdown.Item>
                  </Dropdown.Menu>
                }
              >
                <Button
                  type="tertiary"
                  theme="borderless"
                  icon={<IconMore className="cursor-pointer" />}
                ></Button>
              </Dropdown>
            </div>
          );
        },
      },
    ],
  });

  // 防抖提交（300ms）
  const debouncedSubmit = useCallback(
    debounce((values: any) => {
      setSearchParams(values);
      refresh();
    }, 300),
    []
  );

  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);

  const handleUploadSuccess = (url: string, fileName: string, ossId: string) => {
    // 处理上传成功后的逻辑，例如刷新列表
    refresh();
  };

  return (
    <div className="w-full h-full px-2 py-4">
      <div className="bg-semi-color-white flex flex-col py-2 px-2 w-full h-full rounded-xl shadow-md">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between gap-2 px-2 mb-2 items-center">
          <div className="font-semibold text-xl">文件管理</div>
          {/* Search & Actions */}
          <div className="flex flex-wrap gap-2 items-center">
            <Form<typeof searchParams>
              initValues={searchParams}
              autoScrollToError={{ behavior: "smooth", block: "center" }}
              showValidateIcon
              onValueChange={(values) => {
                debouncedSubmit(values);
              }}
              onReset={() => {
                resetSearchParams();
                refresh();
              }}
              onSubmit={(values) => {
                setSearchParams(values);
                refresh();
              }}
            >
              <div className="flex gap-2">
                <Form.Input
                  className="min-w-[250px]"
                  showClear
                  trigger={["change", "blur"]}
                  noLabel
                  field="fileName"
                  placeholder={"请输入文件名"}
                />
              </div>
            </Form>

            {/* View Toggle */}
            <div className="flex flex-row gap-2 rounded-md bg-semi-color-fill-1 py-[3px] px-2">
              <Tooltip position="topLeft" content="列表视图">
                <div
                  className={classNames(
                    "py-1 px-2 rounded-md  flex items-center justify-center cursor-pointer",
                    viewMode === "list" ? "bg-semi-color-white" : ""
                  )}
                  onClick={() => setViewMode("list")}
                >
                  <IconListView
                    className="text-semi-color-text-1"
                    size="large"
                  ></IconListView>
                </div>
              </Tooltip>
              <Tooltip position="topLeft" content="卡片试图">
                <div
                  className={classNames(
                    "py-1 px-2 rounded-md  flex items-center justify-center cursor-pointer",
                    viewMode === "card" ? "bg-semi-color-white" : ""
                  )}
                  onClick={() => setViewMode("card")}
                >
                  <IconApps className="text-semi-color-text-1" size="large" />
                </div>
              </Tooltip>
            </div>

            <Button
              type="primary"
              theme="solid"
              onClick={() => setIsUploadModalVisible(true)}
            >
              上传文件
            </Button>
            <Button theme="solid" type="danger">
              批量删除
            </Button>
            <Tooltip content="刷新列表">
              <Button
                type="tertiary"
                icon={<IconSync />}
                onClick={() => refresh()}
              />
            </Tooltip>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1  overflow-hidden py-2 px-2">
          {/* List View */}
          {viewMode === "list" && (
            <Table
              rowSelection={rowSelection as any}
              columns={columns}
              className="h-full"
              dataSource={dataSource}
              loading={loading}
              size="small"
              scroll={{ x: 962, y: "calc(100vh - 210px)" }}
              pagination={pagination}
              rowKey="ossId"
              empty={
                <Empty
                  description="暂无数据"
                  image={<EmptyDataIcon style={{ width: 100 }} />}
                />
              }
            />
          )}

          {/* Card View */}
          {viewMode === "card" && (
            <>{
              loading ? (
                <div className="flex-1 h-full flex items-center justify-center">
                  <Spin
                    size="large"
                    style={{ width: "200px" }}
                    tip="正在加载..."
                  />
                </div>
              ) : (
                <div className="flex-1 flex flex-col h-full w-full">
                  <div className="flex-1 overflow-y-auto overflow-x-hidden">
                    <Row gutter={[10, 10]}>
                      {dataSource.length > 0 ? (
                        dataSource.map((item) => (
                          <Col key={item.fileId} xs={1} sm={2} md={3} lg={4}>
                            <div className="rounded-md gap-2 flex-col cursor-pointer flex items-center hover:bg-semi-color-fill-0 px-2 py-2 justify-center transition-shadow duration-200">
                              <Image
                                src={item.url}
                                imgCls=" object-contain"
                                width={"100px"}
                                height={"100px"}
                                fallback={
                                  "http://************:19000/hov-developer/2025/05/03/9dc8f8625ba446d9afcb4526e17863ac.svg"
                                }
                                alt={item.fileName}
                              />
                              <div className="text-sm overflow-hidden w-[100px] text-semi-color-text-1 truncate">
                                {item.fileName}
                              </div>
                            </div>
                          </Col>
                        ))
                      ) : (
                        <div className="flex justify-center py-10 w-full">
                          <Empty
                            description="暂无数据"
                            image={<EmptyDataIcon style={{ width: 100 }} />}
                          />
                        </div>
                      )}
                    </Row>
                  </div>

                  {/* Pagination */}
                  <div className="pt-2 flex justify-end">
                    <Pagination {...pagination} showSizeChanger />
                  </div>
                </div>
              )}
            </>)
          }
        </div>
        <UploadForm
          visible={isUploadModalVisible}
          onClose={() => setIsUploadModalVisible(false)}
          onUploadSuccess={handleUploadSuccess}
        />
      </div>
    </div>
  );
};

