import React, { useState } from "react";
import { ChatSession } from "../interface/message";
import {
  Avatar,
  Button,
  Collapse,
  Collapsible,
  Dropdown,
  Layout,
  List,
  Modal,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import {
  IconChevronDown,
  IconChevronUp,
  IconClose,
  IconComment,
  IconDelete,
  IconHash,
  IconMoon,
  IconMore,
  IconPlus,
  IconSettingStroked,
  IconSidebar,
  IconSun,
} from "@douyinfe/semi-icons";
import { SessionSetting } from "../interface/setting";
import { LocalForageService as storage } from "../utils/storage";
import { IconOverflow } from "@douyinfe/semi-icons-lab";

interface SessionBoxProps {
  sessionSidebar: boolean;
  currentCommentSessionId: string;
  currentCommentSessionTitle: string;
  themeMode: string;
  commentSessionList: ChatSession[];
  sessionSetting: SessionSetting;
  createSession: (roleId: string) => void;
  selectSession: (sessionId: string) => void;
  removeSession: (
    event: React.MouseEvent<HTMLButtonElement>,
    sessionId: string
  ) => void;
  triggerSessionSidebar: () => void;
  jumpPage: (path: string) => void;
  switchThemeMode: (mode: "light" | "dark") => void;
}

const sessionEqual = (
  prevProps: SessionBoxProps,
  currentProps: SessionBoxProps
) => {
  return (
    prevProps.sessionSidebar === currentProps.sessionSidebar &&
    prevProps.themeMode === currentProps.themeMode &&
    prevProps.currentCommentSessionId ===
      currentProps.currentCommentSessionId &&
    prevProps.currentCommentSessionTitle ===
      currentProps.currentCommentSessionTitle
  );
};

export const SessionBox: React.FC<SessionBoxProps> = React.memo(
  ({
    sessionSidebar,
    currentCommentSessionId,
    currentCommentSessionTitle,
    themeMode,
    commentSessionList,
    sessionSetting,
    createSession,
    selectSession,
    removeSession,
    triggerSessionSidebar,
    jumpPage,
    switchThemeMode,
  }) => {
    const { Sider, Header, Content, Footer } = Layout;
    const { Text } = Typography;

    const [sessionRemoveModal, sessionRemoveContextHolder] = Modal.useModal();
    const [showHistorySession, setShowHistorySession] =
      useState<boolean>(false);

    const changeHistorySession = () => {
      setShowHistorySession((prevState) => !prevState);
    };

    const toSettingPage = async () => {
      const settingLastPath = await storage.getItem<string>(
        "setting_last_path"
      );
      if (settingLastPath) {
        jumpPage(settingLastPath);
      } else {
        jumpPage("/plaform/model");
      }
    };

    return (
      <div className={`comment-sider full-height flex flex-col`}>
        <Header data-tauri-drag-region className="comment-session-header">
          <div className="w-full px-3 pt-3 flex justify-between">
            <div className="flex items-center gap-2">
              <Avatar size="small" className="bg-semi-color-primary">
              LynkzHub
              </Avatar>
              <div className="text-lg font-bold">LynkzHub</div>
            </div>
            <div>
              <Tooltip content={"会话栏显隐"}>
                <Button
                  theme="borderless"
                  type="tertiary"
                  icon={<IconSidebar />}
                  aria-label="会话栏显隐"
                  onClick={triggerSessionSidebar}
                />
              </Tooltip>
            </div>
          </div>
          <div className="w-full px-2">
            <Button
              className="comment-session-plus"
              theme="light"
              type="tertiary"
              size="large"
              icon={<IconPlus />}
              onClick={() => createSession("")}
              children="创建新的聊天"
            />
          </div>
        </Header>
        <Content data-tauri-drag-region className="comment-session-main">
          <Collapse defaultActiveKey="1">
            <Collapse.Panel header="最近对话" itemKey="1">
              <List
                className="comment-session-list "
                dataSource={commentSessionList.slice(
                  0,
                  sessionSetting.chatSessionMaxNumber
                )}
                split={false}
                size="small"
                renderItem={(item) => (
                  <List.Item
                    key={item.id}
                    className={
                      "comment-session-item" +
                      (item.id == currentCommentSessionId ? " checked" : "")
                    }
                    onClick={() => selectSession(item.id)}
                  >
                    <Text icon={<IconComment />}>{item.content}</Text>
                    <div className="comment-session-more">
                      <Button
                        theme="borderless"
                        type="tertiary"
                        size="small"
                        icon={<IconDelete />}
                        onClick={(event) =>
                          sessionRemoveModal.confirm({
                            title: "要删除此对话吗？",
                            content:
                              "删除后将不在此处显示，并且该对话所有内容无法找回。",
                            okButtonProps: {
                              type: "danger",
                              autoFocus: true,
                            },
                            onOk: () => removeSession(event, item.id),
                          })
                        }
                      />
                    </div>
                  </List.Item>
                )}
              />
              <List
                style={{
                  display:
                    commentSessionList.length >
                    sessionSetting.chatSessionMaxNumber
                      ? "block"
                      : "none",
                }}
                className="comment-session-list"
                split={false}
                size="small"
              >
                <List.Item
                  className="comment-session-item"
                  onClick={changeHistorySession}
                >
                  {showHistorySession ? (
                    <Text icon={<IconChevronUp />}>收起</Text>
                  ) : (
                    <Text icon={<IconChevronDown />}>展开</Text>
                  )}
                </List.Item>
              </List>
              <Collapsible
                isOpen={
                  showHistorySession &&
                  commentSessionList.length >
                    sessionSetting.chatSessionMaxNumber
                }
              >
                <List
                  className="comment-session-list"
                  dataSource={commentSessionList.slice(
                    sessionSetting.chatSessionMaxNumber
                  )}
                  split={false}
                  size="small"
                  renderItem={(item) => (
                    <List.Item
                      key={item.id}
                      className={
                        "comment-session-item" +
                        (item.id == currentCommentSessionId ? " checked" : "")
                      }
                      onClick={() => selectSession(item.id)}
                    >
                      <Text icon={<IconHash />}>{item.content}</Text>
                      <div className="comment-session-more">
                        <Button
                          theme="borderless"
                          type="tertiary"
                          size="small"
                          icon={<IconMore />}
                          onClick={(event) =>
                            sessionRemoveModal.confirm({
                              title: "要删除此对话吗？",
                              content:
                                "删除后将不在此处显示，并且该对话所有内容无法找回。",
                              okButtonProps: {
                                type: "danger",
                                autoFocus: true,
                              },
                              onOk: () => removeSession(event, item.id),
                            })
                          }
                        />
                      </div>
                    </List.Item>
                  )}
                />
              </Collapsible>
            </Collapse.Panel>
          </Collapse>
        </Content>
        {sessionRemoveContextHolder}
        <Footer
          className="comment-session-footer"
          style={{
            display: "flex",
            flexDirection: "row",
            gap: "5px",
            justifyContent: "flex-end",
          }}
        >
        </Footer>
      </div>
    );
  },
  sessionEqual
);
