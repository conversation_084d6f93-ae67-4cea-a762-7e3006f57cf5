// src/hooks/useDictionary.ts
import { useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { shallowEqual } from "react-redux";
import { getDicts } from "@/api/system/dict";
import { useGlobalStore } from "@/store";

const useDictionary = (keys: string[]) => {
  const {
    dictionary,
    setData,
  } = useGlobalStore.getState();

  // 使用 shallowEqual 避免不必要的重新渲染
  // const dictionaryData = useSelector(
  //   (state: any) => state.dictionary.data,
  //   shallowEqual
  // );

  // 初始化 data 时，确保从 dictionaryData 中获取最新的数据
  const [data, setDataState] = useState<{ [key: string]: any }>(() => {
    return keys.reduce(
      (acc, key) => {
        if (dictionary[key]) {
          acc[key] = dictionary[key];
        }
        return acc;
      },
      {} as { [key: string]: any }
    );
  });


  // 使用 useMemo 确保 keys 数组的引用稳定
  const memoizedKeys = useMemo(() => keys, [keys.join(",")]);

  /**
   * 加载字典数据
   */
  const loadDictionary = async () => {
    const newData: { [key: string]: any } = {};

    try {
      // 获取未加载的字典数据
      const unloadedKeys = memoizedKeys.filter(
        (key) => !dictionary[key] && !data[key]
      );

      if (unloadedKeys.length > 0) {
        // 并行加载字典数据
        await Promise.all(
          unloadedKeys.map(async (key) => {
            try {
              const response = await getDicts(key); // 单个字典键加载
              newData[key] = response.data;
            } catch (error) {
              console.error(
                `Failed to fetch dictionary data for key ${key}:`,
                error
              );
            }
          })
        );
      }

      // 只在有新数据时更新状态
      if (Object.keys(newData).length > 0) {
        // 更新状态
        setDataState((prevData) => ({ ...prevData, ...newData }));
        setData({ ...dictionary, ...newData });
      }
    } catch (error) {
      console.error("Error fetching dictionary data:", error);
    }
  };

  return { data, loadDictionary };
};

export default useDictionary;
