import { delDictData, getDictDataList, refreshCache } from "@/api/system/dict";
import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import {
  IconCaretdown,
  IconDelete,
  IconEdit,
  IconMore,
  IconSmallTriangleDown,
  IconSync,
} from "@douyinfe/semi-icons";
import { IllustrationNoContent } from "@douyinfe/semi-illustrations";
import {
  Button,
  Card,
  Col,
  DatePicker,
  Dropdown,
  Empty,
  Form,
  Input,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Select,
  Table,
  Toast,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { VChart } from "@visactor/react-vchart";
import classNames from "classnames";
import { useTable } from "@/hooks/useTables";
import { getOssConfigList } from "@/api/system/oss-config";
import {
  clean,
  countLoginStatus,
  getLoginLogList,
  statsLoginLogs,
} from "@/api/log/login-log";
import { commonSpec, pieSpec } from "./constats";

export default function LoginLog() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_device_type",
    "sys_login_status",
  ]);
  const [filterOption, setFilterOption] = useState<any>([]);
  useEffect(() => {
    loadDictionary();
    if (dictionaryData?.sys_login_status) {
      setFilterOption(() => {
        return dictionaryData?.sys_login_status.map((item: any) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          };
        });
      });
    }
  }, []);

  const {
    dataSource,
    loading,
    columns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getLoginLogList,
    defaultPageSize: 15,
    params: {},
    columns: [
      {
        title: "用户名称",
        dataIndex: "userName",
        ellipsis: true,
        toolTip: true,
        render: (text: string) => (
          <Typography.Text ellipsis={{ showTooltip: true }}>
            {text}
          </Typography.Text>
        ),
      },
      {
        title: "设备类型",
        dataIndex: "deviceType",
        render: (text: any) => {
          return (
            <DictTag
              dictType="sys_device_type"
              dictValue={text}
              dictionaryData={dictionaryData.sys_device_type || []}
            />
          );
        },
      },
      {
        title: "浏览器",
        dataIndex: "browser",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "登录IP",
        dataIndex: "ipaddr",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "登录地点",
        dataIndex: "loginLocation",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作系统",
        dataIndex: "os",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "描述",
        dataIndex: "msg",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        dataIndex: "loginTime",
        title: "登录时间",
        render: (text: any) => {
          return (
            <Typography.Text ellipsis={{ showTooltip: true }}>
              {text ? text : "-"}
            </Typography.Text>
          );
        },
      },
      {
        title: "操作",
        dataIndex: "operate",
        render: (text: any, row: any) => {
          return (
            <div className="flex flex-row gap-1 items-center">
              <Button theme="borderless">解锁</Button>
              {/* <Dropdown
                className="min-w-[120px]"
                zIndex={1000}
                position="bottom"
                trigger={"click"}
                render={
                  <Dropdown.Menu>
                    <Dropdown.Item
                      icon={<IconEdit />}
                      // onClick={() => handleEditForm(row)}
                    >
                      修改
                    </Dropdown.Item>
                    <Dropdown.Item
                      type="danger"
                      icon={<IconDelete />}
                      // onClick={() => handleRemoveDictData(row.dictCode)}
                    >
                      删除
                    </Dropdown.Item>
                  </Dropdown.Menu>
                }
              >
                <Button
                  type="tertiary"
                  theme="borderless"
                  icon={<IconMore className="cursor-pointer" />}
                ></Button>
              </Dropdown> */}
            </div>
          );
        },
      },
    ],
  });
  const [barChartData, setBarChartData] = useState<any>([]);
  const [barChartFileter, setBarChartFilter] = useState<any>([]);
  const getLoginStats = () => {
    let params = [] as any;
    if (barChartFileter !== null || barChartFileter.length == 2) {
      params = {
        startDate: barChartFileter[0],
        endDate: barChartFileter[1],
      };
    }
    statsLoginLogs(params).then(({ data }) => {
      // console.log(data)
      const tempData: { type: string; date: any; value: any }[] = [];
      data.logs.forEach((item) => {
        tempData.push({
          type: "登录成功",
          date: item.day,
          value: item.success,
        });
        tempData.push({
          type: "登录失败",
          date: item.day,
          value: item.error,
        });
      });
      setBarChartData(tempData);
    });
  };
  useEffect(() => {
    getLoginStats();
  }, [barChartFileter]);

  const barChartConfig = useMemo(() => {
    return {
      ...commonSpec,
      xField: ["date", "type"],
      yField: "value",
      direction: "vertical",
      legends: {
        visible: true,
        orient: "top",
        position: "end",
      },
      data: [
        {
          id: "id0",
          values: barChartData,
        },
      ],
    };
  }, [barChartData]);

  const [pieChartData, setPieChartData] = useState<any>([]);
  const getLoginCount = () => {
    countLoginStatus().then(({ data }) => {
      setPieChartData([
        { type: "登录成功", value: data.success },
        { type: "登录失败", value: data.error },
      ]);
    });
  };
  useEffect(() => {
    getLoginCount();
  }, []);

  const pieConfig = useMemo(() => {
    return {
      ...pieSpec,
      data: [
        {
          id: "id0",
          values: pieChartData,
        },
      ],
    };
  }, [pieChartData]);

  const handleClear = (type: string) => {
    const modal = Modal.warning({
      title: "确认删除数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await clean(type as string)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  return (
    <div className="w-full flex gap-2 flex-col h-full py-2 px-2 sm:overflow-auto  rounded-md">
      <Row gutter={12} className="w-full">
        <Col span={16}>
          <Card
            bordered={false}
            header={
              <div className="flex flex-row items-center justify-between">
                <div className="text-lg font-semibold">登录统计</div>
                <div>
                  <DatePicker
                    type="dateRange"
                    insetInput
                    needConfirm={true}
                    onChange={(date, dateString) => {
                      setBarChartFilter(dateString);
                    }}
                    style={{ width: "100%" }}
                  />
                </div>
              </div>
            }
            bodyStyle={{ padding: "0 10px 10px 10px" }}
            headerLine={false}
          >
            <VChart
              spec={barChartConfig}
              options={{ mode: "desktop-browser" }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            bordered={false}
            header={
              <div className="flex flex-row items-center justify-between">
                <div className="text-lg font-semibold">登录统计</div>
              </div>
            }
            bodyStyle={{ padding: "0 10px 10px 10px" }}
            headerLine={false}
          >
            <VChart spec={pieConfig} options={{ mode: "desktop-browser" }} />
          </Card>
        </Col>
      </Row>
      <div className="sm:overflow-auto rounded-lg overflow-hidden h-full flex gap-2 flex-col bg-semi-color-white w-full px-2 py-2">
        <div className="flex  flex-row items-center justify-between gap-2 py-2">
          <div className="text-lg font-semibold">登录日志</div>
          <div className="flex flex-row items-center gap-2">
            <Select
              className="w-[200px]"
              placeholder="操作状态"
              showClear
              onChange={(value) => {
                setSearchParams({
                  ...searchParams,
                  status: value,
                });
                refresh();
              }}
              value={searchParams.status}
              optionList={filterOption}
            />
            <Dropdown
              position="bottomLeft"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleClear("0")}>
                    保留一个月
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("1")}>
                    保留三个月
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("2")}>
                    保留一年
                  </Dropdown.Item>
                  <Dropdown.Item onClick={() => handleClear("3")}>
                    保留一万条
                  </Dropdown.Item>
                  <Dropdown.Item type="danger" onClick={() => handleClear("4")}>
                    清空所有
                  </Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button
                theme="solid"
                type="warning"
                iconPosition="right"
                icon={<IconSmallTriangleDown />}
              >
                批量处理
              </Button>
            </Dropdown>
            <Tooltip content="刷新列表">
              <Button
                type="tertiary"
                icon={<IconSync />}
                onClick={() => refresh()}
              />
            </Tooltip>
          </div>
        </div>
        <div className="flex-1 sm:overflow-hidden h-full">
          <Table
            rowSelection={rowSelection as any}
            columns={columns}
            className="h-full"
            dataSource={dataSource}
            loading={loading}
            size="small"
            scroll={{ x: 962, y: "calc(100vh - 520px)" }}
            pagination={pagination}
            rowKey="infoId"
            empty={
              <Empty
                description="暂无数据"
                image={<EmptyDataIcon style={{ width: 100 }} />}
              />
            }
          />
        </div>
      </div>
    </div>
  );
}
