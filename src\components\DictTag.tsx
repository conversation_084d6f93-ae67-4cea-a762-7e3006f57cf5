// src/components/DictTag.tsx
import React, { useMemo } from "react";
import { Tag, Badge } from "@douyinfe/semi-ui";
import { IconClear, IconSpin, IconTickCircle } from "@douyinfe/semi-icons";
import classNames from "classnames";

interface DictItem {
  dictCode: string;
  dictSort: number;
  dictLabel: string;
  dictValue: string;
  dictType: string;
  cssClass: string;
  listClass: string;
  isDefault: string;
  remark: string;
  createdAt: string;
}

interface DictTagProps {
  type?: "tag" | "badge"; // 渲染类型，默认为 tag
  dictType: string; // 字典类型
  className?:any;
  dictValue: string | number | any; // 字典值，支持字符串或数字
  dictionaryData: DictItem[]; // 字典数据
  showIcon?: boolean;
}

// 定义一个枚举来表示状态
enum Status {
  Primary = "primary",
  Processing="processing",
  Success = "success",
  Info = "info",
  Warning = "Warning",
  Error = "error",
  Default = "default",
}

enum TagStatus {
  processing = "processing",
  info = "info",
  success= "success",
  warning = "warning",
  error = "error",
}

// 定义一个函数来根据状态返回颜色
function getColorByStatus(color: string): string {
  switch (color) {
    case TagStatus.processing :
      return "blue";
      case TagStatus.info :
        return "grey";
      case TagStatus.success :
        return "green";
      case TagStatus.warning :
        return "yellow";
      case TagStatus.error :
        return "red";
      default:
        // console.error("Unknown status:");
        return "blue";
  }
}

function getIcon(color: string): any {
  switch (color) {
    case Status.Primary:
      return <IconSpin spin />;
    case Status.Success:
      return <IconTickCircle />;
    case Status.Info:
      return <IconTickCircle />;
    case Status.Warning:
      return <IconTickCircle />;
    case Status.Error:
      return <IconClear />;
    case Status.Default:
      return <IconTickCircle />;
    default:
      console.error("Unknown status:");
      return null;
  }
}

function getBadgeColorByStatus(color: string): string {
  switch (color) {
    case Status.Primary:
      return "primary";
    case Status.Processing:
      return "processing"
    case Status.Success:
      return "success";
    case Status.Info:
      return "tertiary";
    case Status.Warning:
      return "warning";
    case Status.Error:
      return "error";
    case Status.Default:
      return "tertiary";
    default:
      console.error("Unknown status:");
      return "primary";
  }
}

const DictTag: React.FC<DictTagProps> = ({
  type = "tag",
  dictType,
  className,
  showIcon = false,
  dictValue,
  dictionaryData,
}) => {
  // 将 dictValue 转换为字符串
  const normalizedDictValue = String(dictValue);

  // 查找对应的字典项
  const dictItem = useMemo(() => {
    return (
      dictionaryData.find(
        (item) =>
          item.dictType === dictType &&
          String(item.dictValue) === String(normalizedDictValue) // 统一转换为字符串
      ) || null
    );
  }, [dictType, normalizedDictValue, dictionaryData]);

  if (!dictItem) {
    return null; // 如果未找到对应的字典项，返回空
  }

  const { dictLabel, listClass } = dictItem;

  // 根据 type 渲染不同的组件
  if (type === "badge") {
    return (
      <span className={classNames("flex gap-1 items-center",className)} >
        <Badge dot type={listClass as any}></Badge>
        <span> {dictLabel}</span>
      </span>
    );
  }

  return (
    <Tag
      className={className}
      color={getColorByStatus(listClass) as any}
      prefixIcon={showIcon ? getIcon(listClass) : null}
    >
      {dictLabel}
    </Tag>
  );
};

export default DictTag;
