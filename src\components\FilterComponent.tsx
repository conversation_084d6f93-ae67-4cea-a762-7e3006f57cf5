import React, { useRef, useState } from "react";
import {
  Form,
  Select,
  Input,
  Button,
  Radio,
  Popover,
  Divider,
  ArrayField,
  useFormState,
  useFormApi,
  Toast,
} from "@douyinfe/semi-ui";
import {
  IconCrossStroked,
  IconFilter,
  IconMinusCircle,
  IconPlusStroked,
  IconSearchStroked,
} from "@douyinfe/semi-icons";
import { log } from "console";

const { Option } = Select;

interface FilterCondition {
  field: string;
  operator: string;
  value: string;
}
type FieldType = "string" | "number" | "enum" | "date" | "boolean";

interface FieldConfig {
  fileType: FieldType;
  type: string;
  label: string;
  options?: Array<{ value: string; label: string }>;
}

interface FilterProps {
  schema: Record<string, FieldConfig>;
  onFilter: (params: any) => void;
}
const rules = [
  { key: "gt", label: "大于" },
  { key: "ge", label: "大于等于" },
  { key: "lt", label: "小于" },
  { key: "le", label: "小于等于" },
  { key: "eq", label: "等于" },
  { key: "ne", label: "不等于" },
  { key: "in", label: "包含" },
  { key: "not_in", label: "不包含" },
  { key: "between", label: "在之间" },
  { key: "not_between", label: "不在之间" },
  { key: "like", label: "全模糊匹配" },
  { key: "not_like", label: "全模糊不匹配" },
  { key: "like_left", label: "左模糊" },
  { key: "like_right", label: "右模糊" },
  { key: "is_null", label: "为空" },
  { key: "not_null", label: "不为空" },
];
const FilterComponent: React.FC<FilterProps> = ({ onFilter, schema }) => {
  const formRef = useRef();
  const [paramsLength, setParamLength] = useState(0)

  const handleSubmit = async () => {
    // @ts-expect-error
    const values = await formRef.current?.validate();
    //校验params field 字段是否为空，如果为空提示
    if (values.params?.length > 0) {
      for (let i = 0; i < values.params.length; i++) {
        const item = values.params[i];
        if (item.field === "") {
          Toast.error("请选择字段");
          return;
        }
        if (item.rule === "") {
          Toast.error("请选择过滤条件");
          return;
        }
      }
    }
    debugger;
    setParamLength(values.params?.length ? values.params.length : 0)
    onFilter(values)
  };

  const handleClear = () => {
    if (formRef.current) {
      // @ts-expect-error
      formRef.current?.reset();
    }
  };

  return (
    <Popover
      trigger="click"
      className="w-[600px]"
      content={
        <div className="w-full p-4">
          <div className="mb-2 w-full">
            <div className="flex items-center justify-between">
              <div className="text-semi-color-text-0">过滤器</div>
              <div
                className="text-sm text-semi-color-text-1 cursor-pointer"
                onClick={handleClear}
              >
                清除筛选条件
              </div>
            </div>
          </div>
          <Form
            allowEmpty={true}
            className="w-full"
            getFormApi={(formApi) => {
              // @ts-expect-error
              formRef.current = formApi;
            }}
          >
            <div className="w-full">
              <Form.RadioGroup
                noLabel
                initValue={"and"}
                field="condition"
                defaultValue="and"
                className="flex space-x-4"
              >
                <Form.Radio value="and">且</Form.Radio>
                <Form.Radio value="or">或</Form.Radio>
              </Form.RadioGroup>
            </div>
            <ArrayField field={"params"}>
              {({ add, arrayFields, addWithInitValue }) => (
                <React.Fragment>
                  {arrayFields.map(({ field, key, remove }, index) => {
                    const fieldPath = `params[${index}]`;
                    const selectedName = formRef.current?.getValue(
                      `${fieldPath}.name`
                    );
                    const fieldConfig = schema[selectedName];
                    // formRef.current?.setValue("type", fieldConfig?.type);
                    return (
                      <div key={key} className="flex items-center w-full gap-1">
                        <div className="w-[40%]">
                          <Form.Select
                            field={`${fieldPath}.field`}
                            noLabel
                            // rules={[{ required: true, message: "请选择字段" }]}
                            placeholder="选择字段"
                            onChange={(val: any) => {
                              console.log(schema[val]);
                              // @ts-expect-error
                              formRef.current?.setValue(
                                `${fieldPath}.type`,
                                schema[val]?.type
                              );
                            }}
                            className="w-full"
                          >
                            {Object.entries(schema).map(([key, config]) => (
                              <Form.Select.Option key={key} value={key}>
                                {config.label}
                              </Form.Select.Option>
                            ))}
                          </Form.Select>
                        </div>
                        <div className="w-[30%]">
                          <Form.Select
                            noLabel
                            placeholder="选择过滤条件"
                            field={`${fieldPath}.rule`}
                            className="w-full"
                          >
                            {rules.map((item) => (
                              <Form.Select.Option
                                key={item.key}
                                value={item.key}
                              >
                                {item.label}
                              </Form.Select.Option>
                            ))}
                          </Form.Select>
                        </div>
                        <div className=" flex  w-[60%] gap-2">
                          <DynamicFormItem
                            fieldConfig={fieldConfig}
                            fieldPath={fieldPath}
                          />
                          <Button
                            className="mt-1"
                            icon={<IconCrossStroked />}
                            theme="borderless"
                            onClick={() => remove()}
                          ></Button>
                        </div>
                      </div>
                    );
                  })}
                  <div className="text-semi-color-primary mt-2  ">
                    <div
                      className="cursor-pointer flex gap-2 items-center w-[200px]"
                      onClick={(e: any) => {
                        e.stopPropagation();
                        addWithInitValue({
                          field: "",
                          rule: "",
                          val: "",
                          type: "string",
                          useLine: true,
                        });
                      }}
                    >
                      <IconPlusStroked />
                      <div>添加筛选条件</div>
                    </div>
                  </div>
                </React.Fragment>
              )}
            </ArrayField>
            <Divider margin="12px" />
            <div className="flex justify-end items-center">
              <Button type="primary" theme="light" onClick={handleSubmit}>
                筛选
              </Button>
            </div>
          </Form>
        </div>
      }
    >
      <Button theme='outline' color="primary" icon={<IconSearchStroked />}>
        高级查询
        {paramsLength > 0 && (
          <div
            className="flex items-center justify-center 
          w-4 h-4 rounded-[50%] bg-semi-color-primary text-semi-color-white text-brand-9  ml-2 text-[12px]"
          >
            {paramsLength}
          </div>
        )}
      </Button>
    </Popover>
  );
};

const DynamicFormItem = ({ fieldConfig, fieldPath }: any) => {
  return (
    <div className="flex-1 w-full">
      {fieldConfig?.filetType === "enum" ? (
        <Form.Select
          placeholder="请选择过滤值"
          noLabel
          className="w-full"
          showClear={true}
          field={`${fieldPath}.val`}
        >
          {fieldConfig.options?.map((opt: any) => (
            <Form.Select.Option key={opt.value} value={opt.value}>
              {opt.label}
            </Form.Select.Option>
          ))}
        </Form.Select>
      ) : fieldConfig?.filetType === "number" ? (
        <Form.InputNumber
          noLabel
          field={`${fieldPath}.val`}
          placeholder="请输入过滤值"
          fieldClassName="w-full"
          className="w-full"
        />
      ) : (
        <Form.Input
          noLabel
          field={`${fieldPath}.val`}
          fieldClassName="w-full"
          placeholder="请输入过滤值"
        />
      )}
    </div>
  );
};

export default FilterComponent;
