import Icon, {
  IconArrowLeft,
  IconEdit,
  IconForward,
  IconHistory,
  IconTickCircle,
  IconAlertCircle,
  IconLock,
} from "@douyinfe/semi-icons";
import { IconAccessibility } from "@douyinfe/semi-icons-lab";
import { Avatar, Button, Divider, Skeleton, Tooltip, Spin } from "@douyinfe/semi-ui";
import classNames from "classnames";
import { Loader, LoaderCircle } from "lucide-react";
import React from "react";

export default function Header({
  detailLoading,
  detailData,
  currentTab,
  tabs,
  handleGoback,
  handleSave,
  autoSaveStatus,
  editLock,
  currentUser,
}: any) {
  return (
    <div className="h-[60px] flex border-b border-solid border-semi-color-border justify-between items-center px-[12px] bg-semi-color-white">
      <div className="flex gap-2 items-center">
        <Button theme="borderless" onClick={handleGoback}>
          <IconArrowLeft />
        </Button>
        <Divider layout="vertical" />
        <div className="flex items-center gap-2">
          {detailLoading ? (
            <Skeleton.Avatar shape="square" style={{ marginRight: 12 }} />
          ) : (
            <Avatar
              className="rounded-lg"
              shape="square"
              size="default"
              src={detailData?.basics?.logo}
            />
          )}

          <div>
            {detailLoading ? (
              <Skeleton.Title
                style={{ width: 120, marginBottom: 12, marginTop: 12 }}
              />
            ) : (
              <>
                <div className="font-semibold text-xs">
                  {detailData?.basics?.name}
                </div>
                <Tooltip content={detailData?.basics?.description}>
                  <div className="text-[12px] text-semi-color-text-2 text-ellipsis truncate max-w-[300px]">
                    {detailData?.basics?.description}
                  </div>
                </Tooltip>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2 text-lg cursor-pointer">
        {tabs.map((item) => {
          return (
            <div
              key={item.value}
              className={classNames(
                "rounded-full py-1 px-4 hover:bg-semi-color-fill-0 ",
                currentTab === item.value
                  ? "font-semibold text-semi-color-primary"
                  : ""
              )}
            >
              {item.label}
            </div>
          );
        })}

        {/* 显示编辑状态 */}
        {editLock && (
          <div className={classNames(
            "flex items-center gap-1 text-sm",
            editLock.lockedBy === currentUser?.userId
              ? "text-semi-color-success"
              : "text-semi-color-warning"
          )}>
            {editLock.lockedBy === currentUser?.userId ? (
              <>
                <IconEdit />
                <span>您正在编辑</span>
              </>
            ) : (
              <>
                <IconLock />
                <span>被 {editLock.username} 编辑中</span>
                {editLock.browserInfo && (
                  <Tooltip content={`在${editLock.browserInfo.deviceType || '未知设备'}的${editLock.browserInfo.browserName || '未知浏览器'}上`}>
                    <IconAccessibility />
                  </Tooltip>
                )}
              </>
            )}
          </div>
        )}

        {/* 根据自动保存状态显示不同的内容 */}
        {editLock?.lockedBy === currentUser?.userId && (
          <>
            {autoSaveStatus === 'saving' && (
              <div className="flex items-center gap-1 text-semi-color-text-2">
                <Icon svg={<LoaderCircle size="1em" />} spin />
                <span className="text-sm">正在保存...</span>
              </div>
            )}
            {autoSaveStatus === 'saved' && (
              <div className="flex items-center gap-1 text-semi-color-success">
                <IconTickCircle />
                <span className="text-sm">已保存</span>
              </div>
            )}
            {autoSaveStatus === 'error' && (
              <div className="flex items-center gap-1 text-semi-color-danger">
                <IconAlertCircle />
                <span className="text-sm">保存失败</span>
              </div>
            )}
          </>
        )}

        <Button
          theme="solid"
          type="primary"
          icon={<IconForward />}
          disabled={!editLock || editLock.lockedBy !== currentUser?.userId}
          onClick={handleSave}
        >
          保存
        </Button>
      </div>
    </div>
  );
}
