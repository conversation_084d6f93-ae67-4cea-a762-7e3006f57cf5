// import {
//   IconBell,
//   IconBox,
//   IconChevronLeft,
//   IconChevronRight,
//   IconChevronUpDown,
//   IconCustomerSupport,
//   IconExit,
//   IconGridView,
//   IconHome,
//   IconInfoCircle,
//   IconSetting,
//   IconUndo,
//   IconUser,
//   IconUserGroup,
// } from "@douyinfe/semi-icons";
// import { IconConfig, IconIntro, IconToken } from "@douyinfe/semi-icons-lab";
// import {
//   Avatar,
//   Button,
//   Divider,
//   Dropdown,
//   DropdownItem,
//   Layout,
//   Nav,
//   Popover,
// } from "@douyinfe/semi-ui";
// import { useNavigate } from "@tanstack/react-router";
// import React, { Children, useEffect, useState } from "react";
// import "./index.scss";
// import { Logo } from "@/components/icon/Logo";
// import SiderAvatar from "@/layout/components/SiderAvatar";
// import AppList from "@/components/icon/AppList";
// import classNames from "classnames";
// import { findMenuByKey, SystemMenus } from "@/constants/systemMenus";
// const LayoutSider: React.FC<SiderProps> = ({
//   jumpPage,
//   menus,
//   activekeys,
//   onClick,
//   setActivekeys,
// }) => {
//   const navigate = useNavigate();

//   const jump = (key: string) => {
//     navigate({
//       to: key,
//     });
//   };

//   return (
//     <div className="h-full">
//       <SiderNav
//         jump={jump}
//         onMenuClick={onClick}
//         activeKey={activekeys}
//         menus={menus}
//         setActiveKey={setActivekeys}
//       />
//     </div>
//   );
// };

// const SiderNav: React.FC<{
//   activeKey: Array<string>;
//   jump: (key: string) => void;
//   menus: Array<MenuProps>;
//   onMenuClick: (data: any) => void;
//   setActiveKey: (key: Array<string>) => void;
// }> = ({ activeKey, jump, menus, setActiveKey, onMenuClick }) => {
//   const [openKeys, setOpenKeys] = useState<string[]>([]);
//   const [collapsed, setCollapsed] = useState<any>(false);
//   const navigate = useNavigate();

//   const renderMenuItems = (items: MenuProps[]): JSX.Element[] => {
//     return items?.map((item) => {
//       if (item?.children && item?.children.length > 0) {
//         return (
//           <Nav.Sub
//             key={item.key}
//             itemKey={item.key}
//             text={item.label}
//             icon={item.icon}
//           >
//             {renderMenuItems(item.children)}
//           </Nav.Sub>
//         );
//       } else {
//         return (
//           <Nav.Item
//             key={item.key}
//             itemKey={item.key}
//             text={item.label}
//             icon={item.icon}
//           />
//         );
//       }
//     });
//   };
//   return (
//     <Nav
//       className="h-full mt-2 pt-2 relative "
//       style={{ width: !collapsed ? "210px" : "60px", backgroundColor: "transparent" }}
//       selectedKeys={activeKey}
//       isCollapsed={collapsed}
//       onSelect={(data: any) => {
//         jump(data.itemKey as string);
//         setActiveKey(data.selectedKeys as string[]);
//         onMenuClick(data);
//         // document.title = data.selectedItems[0].text;
//       }}
//       footer={{
//         style: {
//           width: "100%",
//         },
//         collapseButton: true,
//       }}
//       onCollapseChange={(isCollapsed: boolean) => {
//         setCollapsed(isCollapsed);
//       }}
//     >
//       {renderMenuItems(menus)}
//     </Nav>
//   );
// };

// export default LayoutSider;
