import { Button, Divider, Input, Popover, Typography } from "@douyinfe/semi-ui";

import { IconAppCenter, IconSearch } from "@douyinfe/semi-icons";
const SiderTeamProject = () => {
  return (
    <Popover
      content={
        <div className="w-[300px] flex flex-col justify-between  min-h-[300px] p-2">
          <div>
            <div>
              <Input suffix={<IconSearch />} showClear></Input>
            </div>
            <div className="pt-2 mb-2">
              <div className="py-2 px-2 rounded-md hover:bg-semi-color-fill-0">
                个人团队
              </div>
            </div>
          </div>
          <div className="w-full">
            <Divider margin="5px" />
            <Button block theme="borderless" type="primary">
              创建团队
            </Button>
          </div>
        </div>
      }
      trigger="click"
    >
      {/* <div className="flex items-center justify-between gap-2  hover:bg-semi-color-fill-0 rounded-lg px-2 py-2 cursor-pointer">
        <IconAppCenter />
        <Typography.Text
          ellipsis={{
            showTooltip: {
              opts: { content: "开发团队" },
            },
          }}
        >
          开发团队
        </Typography.Text>
      </div> */}

      <Button theme="borderless" icon={<IconAppCenter />} type="tertiary">
        开发团队
      </Button>
    </Popover>
  );
};

export default SiderTeamProject;
