import SearchEmpty from "@/components/icon/SearchEmpty";
import Icon, { IconSearch, IconSearchStroked } from "@douyinfe/semi-icons";
import { Input, Modal, Empty, Tooltip } from "@douyinfe/semi-ui";
import { motion } from "framer-motion";
import React from "react";

export default function Search() {
  const [visible, setVisible] = React.useState(false);
  const handleCancel = () => {
    setVisible(false);
  };
  const handleOpen = () => {
    setVisible(true);
  };

  return (
    <>
      <Tooltip content="搜索" position="right">
        <div className="flex w-[33px] h-[33px] items-center justify-center hover:bg-semi-color-fill-0 cursor-pointer rounded-md" onClick={(e) => {
          handleOpen();
        }}>
          <IconSearchStroked

            size="large"
            className="text-semi-color-text-2"
          />
        </div>
      </Tooltip>
      <Modal
        width={"60%"}
        title={
          <div className="flex items-center gap-2">
            <IconSearchStroked />
            <div>全局搜索</div>
          </div>
        }
        centered
        onCancel={handleCancel}
        footer={null}
        bodyStyle={{ height: 450 }}
        visible={visible}
      >
        <div className="h-full w-full flex flex-col gap-5">
          <div className="w-full">
            <Input prefix={<IconSearch />} showClear></Input>
          </div>
          <div className=" flex-1 flex-col gap-2 items-center flex justify-center ">
            <motion.div
              className="w-full"
              initial={{ opacity: 0, x: -200 }} // 起始位置在左侧之外
              animate={{ opacity: 1, x: 0 }}    // 向右滑动到正常位置
              transition={{ duration: 0.5, ease: "backOut" }}
            >
              <Empty
                image={<SearchEmpty />}
              />
            </motion.div>
            <motion.div
              className="w-full"
              initial={{ opacity: 0, y: -200 }} // 起始位置在左侧之外
              animate={{ opacity: 1, y: 0 }}    // 向右滑动到正常位置
              transition={{ duration: 0.5, ease: "backOut" }}
            >
              <div className="text-md text-center text-semi-color-text-2">
                输入关键词可快速找到相关搜索结果。点击分类项可过滤文件类型，包括维格表、文件夹、神奇表单、镜像和仪表盘
              </div>
            </motion.div>
          </div>
        </div>
      </Modal>
    </>
  );
}
