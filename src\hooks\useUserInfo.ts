import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store"; // Import AppDispatch explicitly
import { fetchUserInfo } from "@/store/modules/global";

/**
 * Custom hook to ensure user info is loaded
 */
export const useUserInfo = () => {
  const dispatch = useDispatch<AppDispatch>(); // Specify dispatch type
  const userInfo = useSelector((state: RootState) => state.global.userInfo);

  useEffect(() => {
    if (!userInfo) {
      dispatch(fetchUserInfo()); // Now correctly typed
    }
  }, [dispatch, userInfo]);

  return { userInfo };
};