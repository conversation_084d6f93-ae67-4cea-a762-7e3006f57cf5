import React, { useC<PERSON>back, memo, useState, PropsWithChildren } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextArea,
  Upload as SemUpload,
  Typography,
} from "@douyinfe/semi-ui";
import Icon from "@douyinfe/semi-icons";
import {
  sendMessage,
  stopReponding,
  useMessages,
  useResponding,
  useFeatchStream,
  clearMessages,
  useConversationId
} from "@/store/modules/useFeatchStream";
import IconCopy from "@/components/icon/icon-copy";
import {
  ArrowUp,
  LoaderCircle,
  Paperclip,
  RefreshCw,
  Trash,
  X,
} from "lucide-react";
import { MessageUtil } from "@/utils/message-util";
import { MarkdownBox } from "@/components/Markdown/MarkdownBox";
import '../style/chat.scss';
const roleInfo = {
  user: {
    name: "User",
    avatar:
      "https://p6-passport.byteacctimg.com/img/user-avatar/assets/e7b19241fb224cea967dfaea35448102_1080_1080.png~300x300.image",
  },
  assistant: {
    name: "Assistant",
    avatar:
      "http://************:19000/hov-developer/2025/04/26/861a7fef31304cc492a91d13c80d8ed4.svg",
  },
  system: {
    name: "System",
    avatar:
      "http://************:19000/hov-developer/2025/04/26/861a7fef31304cc492a91d13c80d8ed4.svg",
  },
};

const commonOuterStyle = {
  maxWidth: "100%",
  height: "calc(100vh - 150px)",
};

const CustomActions = React.memo(
  ({ message, onDeleteMessage, onMessageSend }: any) => {
    return (
      <>
        {message.role === "assistant" && (
          <span className="w-full items-center flex justify-between ">
            <div className="flex gap-1 items-center">
              <Button
                size="small"
                theme="borderless"
                type="tertiary"
                icon={
                  <Icon className="text-semi-color-fill-4" svg={<IconCopy />} />
                }
                onClick={() => navigator.clipboard.writeText(message?.content)}
              />
              <Button
                size="small"
                type="tertiary"
                icon={<Icon svg={<RefreshCw size={"1em"} />} />}
                theme="borderless"
                onClick={() => onMessageSend(message?.content)}
              />
              <Button
                size="small"
                theme="borderless"
                type="danger"
                icon={<Icon svg={<Trash size={"1em"} />} />}
                onClick={() => onDeleteMessage && onDeleteMessage(message.id)}
              />
            </div>
            {message?.promptTokens && message.promptTokens != 0 && (
              <div className="text-xs text-semi-color-text-2 pl-2">
                {message?.promptTokens} Tokens
              </div>
            )}
          </span>
        )}
      </>
    );
  }
);

function ChatComponent({ welcomeText, enabledWelcome, botId }: any) {
  // Set active botId when component mounts
  React.useEffect(() => {
    useFeatchStream.getState().setActiveBotId(botId);
  }, [botId]);

  // 获取当前会话ID
  const conversationId = useConversationId(botId);
  
  // 清空消息处理函数
  const handleClearMessages = useCallback(() => {
    clearMessages(botId);
    
    // 如果启用了欢迎消息，清空后重新添加系统消息
    if (enabledWelcome && welcomeText) {
      useFeatchStream.getState().addSystemMessage(welcomeText, botId);
    }
  }, [botId, enabledWelcome, welcomeText]);
  
  const handleSendMessage = useCallback(
    (content: string) => {
      sendMessage(content, botId);
    },
    [botId]
  );

  const customRenderAction = useCallback((props: any, onMessageSend: any) => {
    return (
      <CustomActions
        onMessageSend={onMessageSend}
        {...props}
      />
    );
  }, []);

  // Get messages for this specific bot
  const messages = useMessages(botId);
  const responding = useResponding(botId);
  
  const systemMessages = messages.filter((msg) => msg.role === "system");
  
  React.useEffect(() => {
    if (enabledWelcome && welcomeText) {
      if (systemMessages.length === 0) {
        useFeatchStream.getState().addSystemMessage(welcomeText, botId);
      } else {
        systemMessages.forEach((msg) => {
          useFeatchStream.getState().updateSystemMessage(msg.id, welcomeText, botId);
        });
      }
    } else {
      useFeatchStream.getState().clearSystemMessages(botId);
    }
  }, [welcomeText, enabledWelcome, botId]);

  const [value, setValue] = useState("");
  const [fileList, setFileList] = useState([]);
  const CustomInput = (props: any) => {
    const { detailProps, defaultNode, onSend } = props;
    const chatEnterPress = (e?: React.KeyboardEvent) => {
      if (value.trim() !== "") {
        e?.preventDefault();
        onSend(value);
        setValue("");
      }
    };
    return (
      <div className="flex items-center gap-2 mt-2">
        <div className="w-full flex flex-col ">
          <div className="w-full flex justify-center  items-center">
            <div className="comment-send-content shadow-sm  relative p-1 pb-2 overflow-hidden w-full flex flex-col justify-between  px-2 border border-solid h-full rounded-2xl">
              <div className="flex items-center gap-2 flex-wrap">
                {fileList.map((item, key) => {
                  return (
                    <div
                      key={key}
                      className="w-[200px] px-2 py-2 bg-semi-color-fill-0 rounded-lg flex items-center gap-2"
                    >
                      <Icon
                        svg={<Paperclip size="1em" />}
                        className="text-semi-color-text-0"
                      />
                      <Typography.Text
                        ellipsis={{
                          showTooltip: {
                            opts: { content: "这是自定义要展示的内容" },
                          },
                        }}
                        style={{ width: 150 }}
                      >
                        可以自定义浮层里的展示内容试试看吧
                      </Typography.Text>
                      <Icon
                        svg={<X size={"1em"} />}
                        className="cursor-pointer"
                      />
                    </div>
                  );
                })}
              </div>
              <TextArea
                value={value}
                className="comment-send-input border-none rounded-none max-h-[86px] overflow-y-auto"
                placeholder="Enter发送，Shift + Enter换行。"
                onChange={(v) => setValue(v)}
                autoComplete="off"
                autosize={{ minRows: 1 }}
                onEnterPress={chatEnterPress}
              />
              <div className="flex flex-row items-center gap-2  justify-between">
                <Tooltip content={"附加消息"}>
                  <SemUpload
                    fileList={fileList}
                    showUploadList={false}
                    onFileChange={(files: any) => {
                      // setFileList([...fileList, ...files]);
                    }}
                    multiple
                  >
                    <Button
                      theme="borderless"
                      type="tertiary"
                      icon={
                        <Icon
                          svg={<Paperclip size="1em" />}
                          className="text-semi-color-text-0"
                        />
                      }
                      aria-label="附加消息"
                    />
                  </SemUpload>
                </Tooltip>
                <Divider layout="vertical" />
                <div>
                  <Tooltip content={"发送"}>
                    <button
                      disabled={
                        value.trim().length === 0 ||
                        useFeatchStream.getState().responding
                      }
                      onClick={() => chatEnterPress()}
                      className="w-[32px] text-semi-color-white disabled:text-semi-color-disabled-text h-[32px] rounded-full focus:outline-none cursor-pointer
             bg-semi-color-primary items-center flex border-none justify-center
             disabled:bg-semi-color-disabled-bg disabled:cursor-not-allowed 
             hover:bg-semi-color-primary-hover active:bg-semi-color-primary-hover"
                    >
                      <Icon svg={<ArrowUp size="1.5em" />} />
                    </button>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Chat
      chatBoxRenderConfig={{
        renderChatBoxTitle: () => null,
        renderChatBoxAvatar: () => null,
        renderChatBoxAction: (props) =>
          customRenderAction(props, handleSendMessage),
        renderChatBoxContent: renderContent,
      }}
      showClearContext
      renderInputArea={(props: any) => CustomInput(props)}
      className="custom-chat"
      mode={"noBubble"}
      showStopGenerate={responding}
      style={commonOuterStyle}
      chats={messages}
      onStopGenerator={() => stopReponding()}
      onMessageSend={handleSendMessage}
      onClearContext={handleClearMessages} // 绑定清空消息处理函数
      roleConfig={roleInfo}
    />
  );
}
const renderContent = (props: any) => {
  const { role, message, defaultNode, className } = props;
  const render = () => {
    switch (role.name) {
      case "User":
        return (
          <div className=" w-full bg-semi-color-fill-1 px-3  py-2 rounded-lg">
            {message.content}
          </div>
        );
      case "Assistant":
        return (
          <div className="w-full ai-chat-box-wrapper">
            {message.status === "error" && (
              <div className="w-full text-semi-color-text-0 ">
                {message.content}
              </div>
            )}
            {message.status === "loading" && (
              <div className="loading-container">
                <Icon svg={<LoaderCircle size="1em" />} spin />
              </div>
            )}
            {(message.status === "complete" ||
              message.status === "incomplete") && (
              <MarkdownBox
                content={
                  message.status === "incomplete"
                    ? message.content
                    : MessageUtil.messageContentConvert(message.content)
                }
              />
            )}
          </div>
        );
      case "System":
        return <div className=" text-semi-color-text-0">{message.content}</div>;
    }
  };
  return <div className={className}>{render()}</div>;
}

export default ChatComponent;
