import React from "react";
import {
  Avatar,
  Divider,
  Dropdown,
  DropdownItem,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";

import Icon, {
  IconBell,
  IconChevronRight,
  IconCustomerSupport,
  IconExit,
  IconInfoCircle,
  IconLanguage,
  IconUser,
} from "@douyinfe/semi-icons";
import classNames from "classnames";
import AboutModel from "./AboutModel";
import { useState } from "react";
import { useAuthStore, useGlobalStore } from "@/store";
import { logout } from "@/api";
import { useNavigate } from "@tanstack/react-router";
import { useDictionaryStore } from "@/store/modules/useDictionaryStore";
import { MonitorDot } from "lucide-react";
export default function AvatarMenu({ showName = false, showSystemMenu = false, position = "right" }) {
  const [open, setOpen] = useState(false);
  const [profileOpen, setProfileOpen] = useState(false);
  // const { userInfo } = useUserInfo();
  // const { setTokenDataCorrected } = useAuth();
  const { clearTokenData } = useAuthStore();
  const navigate = useNavigate();
  const { userInfo, clearUserInfo } = useGlobalStore.getState();
  const { cleanData } = useDictionaryStore.getState();
  const handleOpenAbout = () => {
    setOpen(true);
  };
  const handleOpenProfile = () => {
    // setProfileOpen(true);
    navigate({
      to: "/profile",
    });
  };

  const handleLogout = () => {
    logout()
      .then(({ msg }) => {
        Toast.success(msg);
        // 清除字典包括个人信息
      })
      .catch((error) => {
        console.error("Logout error:", error);
      })
      .finally(() => {
        clearTokenData();
        clearUserInfo();
        cleanData();
      });
    navigate({
      to: "/login",
    });
  };
  return (
    <>
      <Dropdown
        zIndex={1000}
        trigger={"click"}
        className="w-[250px] min-h-[220px]"
        position={position}
        render={
          <Dropdown.Menu className="w-full" style={{ padding: "0" }}>
            <div className="px-3 flex items-center min-h-[70px]   gap-2  cursor-pointer">
              <Avatar
                // style={{ backgroundColor: "#87d068" }}
                className="rounded-lg"
                size="default"
                shape="square"
                src={userInfo?.user?.avatar}
              ></Avatar>
              <div className="flex flex-col items-start">
                <div className="font-semibold">{userInfo?.user?.nickName}</div>
                <Tooltip content={userInfo?.user?.signature}>
                  <div className="text-semi-color-text-2 truncate">
                    {userInfo?.user?.signature}
                  </div>
                </Tooltip>
              </div>
            </div>
            <Divider />
            <DropdownItem icon={<IconUser />} onClick={handleOpenProfile}>
              个人设置
            </DropdownItem>
            {
              showSystemMenu && <DropdownItem icon={<Icon svg={<MonitorDot size="1em" />} />} onClick={() => {
                navigate({
                  to: '/system/dictionary'
                })
              }}>
                后台管理
              </DropdownItem>
            }
            <Divider />
            <Dropdown
              zIndex={1000}
              trigger={"click"}
              className="w-[160px]"
              position={"right"}
              render={
                <Dropdown.Menu className="w-full">
                  <DropdownItem>
                    <div>简体中文</div>
                  </DropdownItem>
                  <DropdownItem>
                    <div>English</div>
                  </DropdownItem>
                </Dropdown.Menu>
              }
            >
              <DropdownItem icon={<IconLanguage />}>
                <div className="flex w-full justify-between flex-row items-center">
                  <div>语言切换</div>
                  <IconChevronRight />
                </div>
              </DropdownItem>
            </Dropdown>
            <DropdownItem icon={<IconInfoCircle />} onClick={handleOpenAbout}>
              关于
            </DropdownItem>
            <Divider />
            <DropdownItem
              icon={<IconExit className="text-semi-color-danger" />}
              onClick={handleLogout}
            >
              退出登录
            </DropdownItem>
          </Dropdown.Menu>
        }
      >
        {/* w-[44px] */}
        <div
          className={classNames(
            "flex text-wrap   h-[44px] gap-1 items-center text-semi-color-text-2 justify-center hover:bg-semi-color-fill-0 box-border cursor-pointer rounded-md",
            showName ? "px-2" : " w-[44px]"
          )}
        >
          <Avatar size="small" src={userInfo?.user?.avatar} />
          {showName && (
            <div className="text-semi-color-text-0">
              {userInfo?.user?.nickName}
            </div>
          )}
        </div>
      </Dropdown>
      <AboutModel open={open} onCancel={() => setOpen(false)} />
      {/* <UserProfile open={profileOpen} onCancel={() => setProfileOpen(false)} /> */}
    </>
  );
}
