import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { withField } from '@douyinfe/semi-ui';
import { CommonFieldProps } from '@douyinfe/semi-ui/lib/es/form';

interface CustomSelectorProps {
    options: { value: string; label: string }[];
    value?: string;
    className?: string;
    onChange: (value: string) => void;
}

const CustomSelector: React.FC<CustomSelectorProps> = ({ options, value, onChange, className }) => {
    const [selectedValue, setSelectedValue] = useState<string | undefined>(value);

    const handleClick = (optionValue: string) => {
        setSelectedValue(optionValue);
        onChange(optionValue);
    };

    useEffect(() => {
        // 当 value 被外部改变时同步内部状态
        setSelectedValue(value);
    }, [value]);

    useEffect(() => {
        // 如果没有指定 value 且 options 已加载，尝试自动选择第一个选项
        if (!value && options.length > 0 && !selectedValue) {
            const firstOptionValue = options[0]?.value;
            setSelectedValue(firstOptionValue);
            onChange(firstOptionValue);
        }
    }, [options, selectedValue, value, onChange]);

    return (
        <div className={classNames("flex gap-1 flex-1 items-center flex-wrap", ...className)}>
            {options.map((option) => (
                <div
                    key={option.value}
                    className={classNames('px-2 py-1 text-sm rounded cursor-pointer', {
                        'bg-semi-color-primary text-semi-color-white': option.value === selectedValue,
                        'hover:bg-semi-color-fill-0': option.value !== selectedValue,
                    })}
                    onClick={() => handleClick(option.value)}
                >
                    {option.label}
                </div>
            ))}
        </div>
    );
};

interface WrappedCustomSelectorProps {
    value?: string;
    className?: string,
    onChange?: (value: string) => void;
    options: { value: string; label: string }[];
}

const WrappedCustomSelector: React.FC<WrappedCustomSelectorProps> = ({ value, onChange, options, className }) => {
    return (
        <CustomSelector
            className={className}
            options={options}
            value={value}
            onChange={(selectedValue) => {
                if (onChange) {
                    onChange(selectedValue);
                }
            }}
        />
    );
};

// Use withField with correct types
const SemiFormFieldCustomSelector = withField(WrappedCustomSelector, {
    valueKey: 'value',
    onKeyChangeFnName: 'onChange',
    // valuePath: 'selectedValue',
});

export {
    SemiFormFieldCustomSelector,
    CustomSelector,
};