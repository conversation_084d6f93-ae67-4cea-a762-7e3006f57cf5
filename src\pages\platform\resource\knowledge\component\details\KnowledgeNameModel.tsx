import { Form, Input, Modal } from '@douyinfe/semi-ui'

export default function KnowledgeNameModel({
    visible,
    handleOk,
    handleCancel,
}: any) {
    return (
        <Modal
            title="修改"
            visible={visible}
            onOk={handleOk}
            onCancel={handleCancel}
            closeOnEsc={true}
        >
            <Form>
                <Form.Input field='name' required rules={[
                    { required: true, message: '请输入名称' }
                ]} label="名称" placeholder="请输入名称" />
                <Form.TextArea field='description' rules={[
                    { required: true, message: '请输入描述' }
                ]} label="描述" placeholder="请输入描述" />
            </Form>
        </Modal>
    )
}
