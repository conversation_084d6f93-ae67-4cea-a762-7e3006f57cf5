import React, { useState, useEffect, useRef, useCallback } from 'react';
import { config, MdEditor } from 'md-editor-rt';
import { Hash, List, Quote, Code, Bold, Italic, Link, Image, Table, ChevronUp, ChevronDown } from 'lucide-react';
import { withField } from '@douyinfe/semi-ui';
import 'md-editor-rt/lib/style.css'; // 确保导入基础样式
import './prompt-editor.scss';


interface SlashCommand {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  shortcut?: string;
  action: (editor: any) => void;
}

interface EnhancedMdEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  maxHeight?: number;
}



// 基础 PromptEditor 组件
const PromptEditor: React.FC<EnhancedMdEditorProps> = ({
  value = '',
  onChange,
  placeholder = '输入您的提示词...',
  className = '',
  minHeight = 300,
  maxHeight = 500,
}) => {
  const [content, setContent] = useState(value);
  const [showSlashMenu, setShowSlashMenu] = useState(false);
  const [slashMenuPosition, setSlashMenuPosition] = useState({ x: 0, y: 0 });
  const [selectedCommandIndex, setSelectedCommandIndex] = useState(0);
  const [slashStartPos, setSlashStartPos] = useState(0);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const editorRef = useRef<any>(null);

  // 计算当前高度
  const currentHeight = isCollapsed ? Math.min(minHeight, 300) : maxHeight;
  // // 添加自定义样式处理函数
  useEffect(() => {
    setContent(value);
  }, [value]);


  // 斜杠命令配置
  const slashCommands: SlashCommand[] = [
    {
      id: 'heading1',
      title: '一级标题',
      description: '创建一级标题',
      icon: <Hash size={16} />,
      shortcut: 'H1',
      action: (editor) => insertText(editor, '# ')
    },
    {
      id: 'heading2',
      title: '二级标题',
      description: '创建二级标题',
      icon: <Hash size={16} />,
      shortcut: 'H2',
      action: (editor) => insertText(editor, '## ')
    },
    {
      id: 'heading3',
      title: '三级标题',
      description: '创建三级标题',
      icon: <Hash size={16} />,
      shortcut: 'H3',
      action: (editor) => insertText(editor, '### ')
    },
    {
      id: 'bold',
      title: '粗体',
      description: '加粗文本',
      icon: <Bold size={16} />,
      shortcut: '⌘B',
      action: (editor) => insertText(editor, '**粗体文本**')
    },
    {
      id: 'italic',
      title: '斜体',
      description: '倾斜文本',
      icon: <Italic size={16} />,
      shortcut: '⌘I',
      action: (editor) => insertText(editor, '*斜体文本*')
    },
    {
      id: 'code',
      title: '代码',
      description: '行内代码',
      icon: <Code size={16} />,
      shortcut: '⌘E',
      action: (editor) => insertText(editor, '`代码`')
    },
    {
      id: 'codeblock',
      title: '代码块',
      description: '多行代码块',
      icon: <Code size={16} />,
      shortcut: '```',
      action: (editor) => insertText(editor, '```\n代码块\n```')
    },
    {
      id: 'quote',
      title: '引用',
      description: '创建引用块',
      icon: <Quote size={16} />,
      shortcut: '>',
      action: (editor) => insertText(editor, '> ')
    },
    {
      id: 'list',
      title: '无序列表',
      description: '创建无序列表',
      icon: <List size={16} />,
      shortcut: '-',
      action: (editor) => insertText(editor, '- ')
    },
    {
      id: 'orderedlist',
      title: '有序列表',
      description: '创建有序列表',
      icon: <List size={16} />,
      shortcut: '1.',
      action: (editor) => insertText(editor, '1. ')
    },
    {
      id: 'link',
      title: '链接',
      description: '插入链接',
      icon: <Link size={16} />,
      shortcut: '⌘K',
      action: (editor) => insertText(editor, '[链接文本](URL)')
    },
    {
      id: 'image',
      title: '图片',
      description: '插入图片',
      icon: <Image size={16} />,
      shortcut: '⌘⇧I',
      action: (editor) => insertText(editor, '![图片描述](图片URL)')
    },
    {
      id: 'table',
      title: '表格',
      description: '插入表格',
      icon: <Table size={16} />,
      shortcut: '⌘T',
      action: (editor) => insertText(editor, '| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容 | 内容 | 内容 |')
    },
    {
      id: 'variable',
      title: '变量',
      description: '插入变量占位符',
      icon: <span style={{ fontSize: '14px', fontWeight: 'bold' }}>123</span>,
      shortcut: '{{}}',
      action: (editor) => insertText(editor, '{{变量名}}')
    }
  ];

  // 插入文本的辅助函数
  const insertText = (_editor: any, text: string) => {
    // 这里需要根据实际的编辑器API来实现
    const newContent = content.slice(0, slashStartPos) + text + content.slice(slashStartPos + 1);
    setContent(newContent);
    onChange?.(newContent);
    setShowSlashMenu(false);
  };

  // 处理内容变化
  const handleChange = (newValue: string) => {
    setContent(newValue);
    onChange?.(newValue);

    // 检查是否输入了斜杠
    const lastChar = newValue[newValue.length - 1];
    if (lastChar === '/') {
      setSlashStartPos(newValue.length - 1);
      setShowSlashMenu(true);
      setSelectedCommandIndex(0);
      // 这里需要获取光标位置来设置菜单位置
      // 暂时使用固定位置
      setSlashMenuPosition({ x: 100, y: 100 });
    } else if (showSlashMenu && newValue.length < content.length) {
      // 如果删除了字符，隐藏菜单
      setShowSlashMenu(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!showSlashMenu) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        setSelectedCommandIndex(prev =>
          prev < slashCommands.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        event.preventDefault();
        setSelectedCommandIndex(prev =>
          prev > 0 ? prev - 1 : slashCommands.length - 1
        );
        break;
      case 'Enter':
        event.preventDefault();
        const selectedCommand = slashCommands[selectedCommandIndex];
        selectedCommand.action(editorRef.current);
        break;
      case 'Escape':
        event.preventDefault();
        setShowSlashMenu(false);
        break;
    }
  }, [showSlashMenu, selectedCommandIndex, slashCommands]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // 处理命令点击
  const handleCommandClick = (command: SlashCommand) => {
    command.action(editorRef.current);
  };


  return (
    <div className={`prompt-editor-container border  border-semi-color-border  rounded-md border-solid overflow-hidden shadow-sm w-full ${className}`} style={{ position: 'relative' }}>
      <MdEditor
        ref={editorRef}
        className="prompt-editor"
        value={content}
        onChange={handleChange}
        placeholder={placeholder}
        preview={false}
        id="prompt-editor"
        toolbars={[]}
        footers={[]}
        // markdownItPlugins={markdownItPlugins}
        showCodeRowNumber={false}
        style={{
          height: `${currentHeight}px`,
          border: "none",
          transition: 'height 0.3s ease-in-out'
        }}
        theme="light"
        previewTheme="default"
        codeTheme="atom"
      />

      {/* 斜杠命令下拉菜单 */}
      {showSlashMenu && (
        <div
          className="slash-command-dropdown"
          style={{
            left: slashMenuPosition.x,
            top: slashMenuPosition.y,
          }}
        >
          {slashCommands.map((command, index) => (
            <div
              key={command.id}
              className={`slash-command-item ${index === selectedCommandIndex ? 'selected' : ''}`}
              onClick={() => handleCommandClick(command)}
            >
              <div className="command-icon">
                {command.icon}
              </div>
              <div className="command-content">
                <div className="command-title">{command.title}</div>
                <div className="command-description">{command.description}</div>
              </div>
              {command.shortcut && (
                <div className="command-shortcut">{command.shortcut}</div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* 自定义状态栏 */}
      <div className="prompt-editor-statusbar flex items-center justify-between  px-2 border-t border-gray-200 bg-gray-50 text-xs text-gray-500">
        <div>
          {content.length} 字符 · {content.split('\n').length} 行
        </div>
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center gap-1  cursor-pointer text-sm rounded hover:bg-gray-200 transition-colors duration-200 text-gray-600 hover:text-gray-800"
          title={isCollapsed ? "展开编辑器" : "折叠编辑器"}
        >
          {isCollapsed ? (
            <>
              <ChevronUp size={14} />
              <span>展开</span>
            </>
          ) : (
            <>
              <ChevronDown size={14} />
              <span>折叠</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
};

// 使用 withField HOC 创建 Semi Design Form 字段组件
const PromptEditorField = withField(PromptEditor, {
  valueKey: 'value',
  onKeyChangeFnName: 'onChange',
});

export default PromptEditor;
export { PromptEditorField };






