import { createRoute, Outlet, parseSearchWith } from "@tanstack/react-router";
import Dictionary from "@/pages/system/dictionary";
import { authRoute, rootRoute } from "./base";
import Workbench from "@/pages/system/workbench";
import Oss from "@/pages/system/oss/config";
import OperationLog from "@/pages/system/log/oper-log";
import LoginLog from "@/pages/system/log/login-log";
import FileList from "@/pages/system/oss/files";
import Clients from "@/pages/system/client";
import OnlineUser from "@/pages/system/log/online";
import HomeLayout from "@/layout/home";
import Notice from "@/pages/system/notice";
import NoticePreview from "@/pages/system/notice/notice-preview";
import Role from "@/pages/system/permission/role";
import Menu from "@/pages/system/permission/menu";
import Model from "@/pages/system/model";
import NoticeForm from "@/pages/system/notice/notice-form";
import User from "@/pages/system/org/user";
import Post from "@/pages/system/org/post";
import Profile from "@/pages/profile";

// 组织架构模块
const orgRoutes = [
  {
    path: "/system/org/user",
    getParentRoute: () => authRoute,
    component: () => <User />,
  },
  {
    path: "/system/org/post",
    getParentRoute: () => authRoute,
    component: () => <Post />,
  }
]

//通知路由
const notices = [
  {
    path: "/system/notice",
    getParentRoute: () => authRoute,
    component: () => <Notice />,
  },
  {
    path: "/system/notice/preview/$id",
    getParentRoute: () => authRoute,
    component: () => <NoticePreview />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/notice/form",
    component: () => <NoticeForm />,
  }
]
//日志
const logs = [
  {
    getParentRoute: () => authRoute,
    path: "/system/log/online",
    component: () => <OnlineUser />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/log/oper-log",
    component: () => <OperationLog />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/log/login-log",
    component: () => <LoginLog />,
  }
]

const systems = [
  {
    getParentRoute: () => authRoute,
    path: "/system/model",
    component: () => <Model />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/permission/menu",
    component: () => <Menu />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/client",
    component: () => <Clients />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/permission/role",
    component: () => <Role />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/files",
    component: () => <FileList />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/oss",
    component: () => <Oss />,
  },
  {
    getParentRoute: () => authRoute,
    path: "/system/dictionary",
    component: () => <Dictionary />,
  },
]


const systemRoutes = [
  ...systems,
  ...orgRoutes,
  ...logs,
  ...notices
] as any



export default systemRoutes;
