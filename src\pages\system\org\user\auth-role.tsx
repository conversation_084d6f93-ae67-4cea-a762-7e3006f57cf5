import { authRole, getUser } from '@/api/system/user';
import { Modal, Spin, Toast, Transfer } from '@douyinfe/semi-ui'
import { motion } from 'framer-motion';
import React, { useEffect } from 'react'

export default function AuthRole({ open, id, onCancel, onRefresh }: any) {
    const [roles, setRoles] = React.useState<any>([]);
    const [defaultRoles, setDefaultRoles] = React.useState<any>([]);
    const [loading, setLoading] = React.useState(false);
    const [subLoading, setSubLoading] = React.useState(false)
    const [values, setValues] = React.useState<any>([]); // 用于存储选中的值 [1,2,3,4] [dat]
    const featchUserData = () => {
        setLoading(true)
        getUser(id).then(({ data }) => {
            console.log(data)
            setRoles(data.roles.map((item: any) => {
                return {
                    value: item.roleId,
                    key: item.roleId,
                    label: item.roleName,
                }
            }))
            // console.log(data.roleIds)
            setDefaultRoles(data.roleIds.map((item: any) => item))
        }).finally(() => { setLoading(false) })
    }
    useEffect(() => {
        if (open) {
            featchUserData()
        }
    }, [open])


    const handleSubmit = async () => {
        setSubLoading(true)
        authRole({
            userId: id,
            roleIds: values,
        }).then(({ msg }) => {
            Toast.success(msg)
            onRefresh();
            onCancel();
        }).finally(() => { setSubLoading(false) })
    }
    return (
        <Modal
            width={700}
            visible={open}
            title={"分配角色"}
            onCancel={onCancel}
            centered
            okButtonProps={{
                loading: subLoading,
                disabled: loading
            }}
            onOk={handleSubmit}
            bodyStyle={{
                minHeight: 300,
            }}>
            {
                loading ?
                    <div className='flex flex-col items-center w-full h-[300px] justify-center'>
                        <Spin spinning size="large" />
                        <span className="mt-2 text-gray-500 text-sm">加载中...</span>
                    </div>
                    : <Transfer
                        className='w-full h-full semi-light-scrollbar'
                        style={{ height: 416 }}
                        dataSource={roles}
                        defaultValue={[...defaultRoles]}
                        onChange={(values, items) => setValues(values)}
                    />
            }

        </Modal>
    )
}
