import { Suspense } from "react";
import AppIndex from "@/pages/platform/app/index";
import Knowledge from "@/pages/platform/resource/knowledge";
import KnowledgeDetails from "@/pages/platform/resource/knowledge/component/details/index";
import Segment from "@/pages/platform/resource/knowledge/component/segment";
import { authRoute, platformRoute } from "./base";
import AppDetails from "@/pages/platform/app/app-details";
import Mcp from "@/pages/platform/resource/mcp";
import Tools from "@/pages/platform/resource/tools";
import Prompt from "@/pages/platform/resource/prompt";
import PromptForm from "@/pages/platform/resource/prompt/prompt-form";

const platformRoutes = [
  {
    getParentRoute: () => platformRoute,
    path: "/platform/app/index",
    component: () => <AppIndex />,
  },
  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/tools",
    component: Tools,
  },
  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/knowledge",
    component: Knowledge,
  },
  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/knowledge/details/$id",
    component: KnowledgeDetails,
  },

  {
    // id:"knowledgeSegment",
    getParentRoute: () => platformRoute,
    path: "/platform/resource/knowledge/details/$detailId/segment/$id",
    component: () => (
      <Suspense fallback={<div>Loading...</div>}>
        <Segment />
      </Suspense>
    ),
  },
  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/mcp",
    component: Mcp,
  },
  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/prompt",
    component: Prompt,
  },

  {
    getParentRoute: () => platformRoute,
    path: "/platform/resource/prompt/form",
    component: PromptForm,
  }
]

export default platformRoutes;
