import { addDictData, editDictData, getDictData } from "@/api/system/dict";
import { useBoolean } from "@/hooks";
import { Button, Col, Form, Modal, Row, Spin, Toast } from "@douyinfe/semi-ui";
import React, { useEffect } from "react";
import { dictCssTypeOptions, getBgClass } from "../constants/dictonary";
import classNames from "classnames";

function DictDataForm({ onCancel, open, dictType, onRefresh, title, id }: any) {
  const formApiRef = React.useRef(null);
  const [loading, { setTrue: setLoading, setFalse: closeLoading }] =
    useBoolean(false);
  const [spinning, { setTrue: openSpinning, setFalse: closeSpinning }] =
    useBoolean(false);
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const handleCancel = () => {
    onCancel();
    onRefresh();
  };
  const handleReset = () => {
    // @ts-expect-error
    formApiRef.current?.reset();
    onCancel();
  };
  const handleAddSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await addDictData({
        ...values,
        dictType,
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const handleEditSubmit = async (values: any) => {
    try {
      setLoading();
      const res = await editDictData({
        ...values,
        dictCode: id,
        dictType,
      });
      Toast.success(res.msg);
      closeLoading();
      handleCancel();
    } catch (err) {
      closeLoading();
      throw err;
    }
  };
  const submitForm = async () => {
    try {
      // @ts-expect-error
      const values = await formApiRef.current?.validate();
      id ? await handleEditSubmit(values) : await handleAddSubmit(values);
    } catch (errors) {
      console.error(errors);
    }
  };
  const getDictDataDetail = async () => {
    try {
      openSpinning();
      // setLoading();
      const { data } = await getDictData(id);
      // @ts-expect-error
      formApiRef.current?.setValues(data);
      closeSpinning();
    } catch (err) {
      closeSpinning();
      console.error(err);
    }
  };
  useEffect(() => {
    if (id) {
      getDictDataDetail();
    }
  }, [open, id]);

  const readSelectOptionItem = (optionNode: any) => {
    {
      return (
        <Form.Select.Option
          key={optionNode.value}
          value={optionNode.value}
          className="flex gap-2 items-center flex-row"
        >
          <div
            className={classNames(
              "w-[10px] h-[10px] rounded-sm",
              getBgClass(optionNode.value)
            )}
          ></div>
          <span>{optionNode.label}</span>
        </Form.Select.Option>
      );
    }
  };
  const renderSelectedItem = (optionNode: any) => {
    return (
      <div className="flex gap-2 items-center">
        <div
          className={classNames(
            "w-[10px] h-[10px] rounded-sm",
            getBgClass(optionNode.value)
          )}
        >
        </div>
        <span>{optionNode.label[1]}</span>
      </div>
    );
  };
  return (
    <div>
      <Modal
        title={title}
        centered
        width={750}
        footer={
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Button style={{ marginRight: 8 }} onClick={handleReset}>
              取消
            </Button>
            <Button theme="solid" loading={loading} onClick={submitForm}>
              提交
            </Button>
          </div>
        }
        visible={open}
        onCancel={handleReset}
        closeOnEsc={false}
      >
        <Spin tip="正在加载..." spinning={spinning}>
          <Form
            className="w-full"
            getFormApi={getFormApi}
            wrapperCol={{ span: 24 }}
          >
            <Row gutter={[10, 10]}>
              <Col span={12}>
                <Form.Input
                  field="dictLabel"
                  rules={[{ required: true, message: "请填写字典标签" }]}
                  label="字典标签"
                  placeholder={"请输入字典标签"}
                  required
                />
              </Col>
              <Col span={12}>
                <Form.Input
                  field="dictValue"
                  rules={[{ required: true, message: "请填写字典键值" }]}
                  placeholder={"请输入字典键值"}
                  label="字典键值"
                  required
                />
              </Col>
              <Col span={24}>
                <Form.Select
                  // optionList={dictCssTypeOptions}
                  field="listClass"
                  className="w-full"
                  showClear={true}
                  rules={[{ required: true, message: "请选择样式属性" }]}
                  placeholder={"请选择样式属性"}
                  renderSelectedItem={renderSelectedItem}
                  label="样式属性"
                >
                  {dictCssTypeOptions.map((item) => readSelectOptionItem(item))}
                </Form.Select>
              </Col>
              <Col span={12}>
                <Form.InputNumber
                  field="dictSort"
                  className="w-full"
                  initValue={"0"}
                  rules={[{ required: true, message: "请填写字典排序" }]}
                  placeholder={"请输入字典排序"}
                  label="字典排序"
                  required
                />
              </Col>
              <Col span={24}>
                <Form.TextArea field="remark" label="备注" />
              </Col>
            </Row>
          </Form>
        </Spin>
      </Modal>
    </div>
  );
}
export default DictDataForm;
