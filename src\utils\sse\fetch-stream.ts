// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { Toast } from "@douyinfe/semi-ui";
import { ReturnEvent, type RawStreamEvent } from "./StreamEvent";

/**
 * Fetches a stream from the given URL and yields parsed events.
 */
export async function* fetchStream(
  url: string,
  init: RequestInit
): AsyncIterable<ReturnEvent> {
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
    },
    ...init,
  });
  if (response.status !== 200) {
    yield {
      type: "error",
      row: {
        error: `请求服务端出现异常。`,
      },
    };
    return;
  }
  const reader = response.body
    ?.pipeThrough(new TextDecoderStream())
    .getReader();
  if (!reader) {
    yield { type: "error", row: { error: "无法读取返回数据" } };
    return;
  }
  let buffer = "";
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    buffer += value;
    try {
      const json = JSON.parse(buffer);
      if (json.code) {
        const errorMessage =
          json.msg || `Server returned error code: ${json.code}`;
        yield { type: "error", row: { error: errorMessage } };
        continue;
      }
    } catch (e) {}
    // 处理事件流
    while (true) {
      const index = buffer.indexOf("\n\n");
      if (index === -1) break;

      const chunk = buffer.slice(0, index);
      buffer = buffer.slice(index + 2);
      const event = parseEvent(chunk);
      if (event) {
        yield {
          type: "event",
          row: event,
        };
      }
    }
  }
}

/**
 * Parses a single SSE event chunk into a structured object.
 */
export function parseEvent(chunk: string): RawStreamEvent | undefined {
  const lines = chunk.split("\n").filter((line) => line.trim() !== "");

  if (lines.length === 0) return undefined;

  const result = {
    event: "message",
    data: "",
    id: "",
  };

  for (const line of lines) {
    const pos = line.indexOf(":");
    if (pos === -1) continue;

    const field = line.slice(0, pos).trim();
    let value = line.slice(pos + 1).trim();

    // 处理前导空格（SSE 允许冒号后跟一个空格）
    if (value.startsWith(" ")) {
      value = value.slice(1);
    }

    switch (field) {
      case "event":
        result.event = value;
        break;
      case "data":
        result.data = JSON.parse(value);
        break;
      case "id":
        result.id = value;
        break;
      default:
        // 忽略不支持的字段
        break;
    }
  }

  return result;
}
