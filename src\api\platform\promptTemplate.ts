import { http } from "@/utils/axios";

const base_url = "aigc/v1/prompt/template";
export function getPromptTemplate(params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}


export function addPromptTemplate(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}

export function getPromptTemplateById(id: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}

export function updatePromptTemplate(data: any) {
  return http.request<API.Result<any>>({
    url: `${base_url}`,
    method: "PUT",
    data,
  });
}

export function deletePromptTemplate(ids:any) {
  return http.request<API.Result<any>>({
    url: `${base_url}/${ids}`,
    method: "DELETE",
  });
}
