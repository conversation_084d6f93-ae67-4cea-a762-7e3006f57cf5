import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Popover, Divider, Toolt<PERSON> } from "@douyinfe/semi-ui";
import {
  IconFillStroked,
  IconFilter,
  IconHourglassStroked,
} from "@douyinfe/semi-icons";

interface TableColumn {
  dataIndex: string;
  title: string;
}

interface TableColumnFilterProps {
  columns: TableColumn[];
  onColumnsChange?: (selectedColumns: TableColumn[]) => void; // 添加回调函数类型
}

const TableColumnFilter: React.FC<TableColumnFilterProps> = ({
  columns,
  onColumnsChange,
}) => {
  const [selectedColumns, setSelectedColumns] =
    useState<TableColumn[]>(columns);
  const handleCheckboxChange = (checked: boolean, column: TableColumn) => {
    if (column.dataIndex === "operate") {
      return;
    }

    const newSelectedColumns = checked
      ? [...selectedColumns, column]
      : selectedColumns.filter((c) => c.dataIndex !== column.dataIndex);

    setSelectedColumns(newSelectedColumns);
    onColumnsChange?.(newSelectedColumns); // ✅ Now using the updated value
  };

  const resetToDefault = () => {
    setSelectedColumns(columns);
    onColumnsChange?.(columns); // 重置时也通知父组件
  };

  return (
    <div>
      <Popover
        trigger="click"
        content={
          <div className="min-w-[200px] h-[300px] flex flex-col semi-light-scrollbar">
            {/* 可滚动区域 */}
            <div className="overflow-y-auto flex-grow p-2">
              {columns.map((column) => (
                <div className="p-2" key={column.dataIndex}>
                  <Checkbox
                    checked={selectedColumns.some(
                      (c) => c.dataIndex === column.dataIndex
                    )}
                    onChange={(e) => {
                      // @ts-expect-error
                      handleCheckboxChange(e?.target?.checked, column);
                    }}
                    disabled={column.dataIndex === "operate"} // 添加禁用逻辑
                  >
                    {column.title}
                  </Checkbox>
                </div>
              ))}
            </div>
            {/* 固定底部按钮 */}
            <Divider />
            <div
              className="p-2 text-center text-semi-color-primary cursor-pointer select-none"
              onClick={resetToDefault}
            >
              重置为默认
            </div>
          </div>
        }
      >
        {/* <Tooltip content="筛选"> */}
        <Button type="tertiary" icon={<IconFillStroked />}></Button>
        {/* </Tooltip> */}
      </Popover>
    </div>
  );
};

export default TableColumnFilter;
