{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "1.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@douyinfe/semi-icons": "^2.79.0", "@douyinfe/semi-icons-lab": "^2.79.0", "@douyinfe/semi-illustrations": "^2.79.0", "@douyinfe/semi-ui": "^2.79.0", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@flowgram.ai/form-materials": "^0.1.28", "@flowgram.ai/free-container-plugin": "^0.1.28", "@flowgram.ai/free-group-plugin": "^0.1.28", "@flowgram.ai/free-layout-editor": "^0.1.28", "@flowgram.ai/free-lines-plugin": "^0.1.28", "@flowgram.ai/free-node-panel-plugin": "^0.1.28", "@flowgram.ai/free-snap-plugin": "^0.1.28", "@flowgram.ai/group-plugin": "0.1.26", "@flowgram.ai/minimap-plugin": "^0.1.28", "@mdxeditor/editor": "^3.32.3", "@semi-bot/semi-theme-freelynet": "^1.0.0", "@semi-bot/semi-theme-herther-1": "^1.3.8", "@semi-bot/semi-theme-zinc": "^1.0.0", "@tanstack/react-router": "^1.120.3", "@uiw/react-codemirror": "^4.23.13", "@visactor/react-vchart": "^1.13.9", "@visactor/vchart-semi-theme": "^1.12.2", "axios": "^1.9.0", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "emoji-mart": "^5.6.0", "framer-motion": "^12.15.0", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "katex": "^0.16.22", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "lucide-react": "^0.514.0", "markdown-it-task-lists": "^2.1.1", "md-editor-rt": "^5.6.0", "mermaid": "^10.9.3", "nanoid": "^5.1.5", "prismjs": "^1.30.0", "qs": "^6.14.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-infinite-scroller": "^1.2.6", "react-markdown": "^9.1.0", "react-responsive": "^10.0.1", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "styled-components": "^6.1.18", "unist": "^0.0.1", "unist-util-visit": "^5.0.0", "zod": "^3.24.4", "zustand": "^5.0.5"}, "devDependencies": {"@tanstack/router-devtools": "^1.120.3", "@tanstack/router-vite-plugin": "^1.120.3", "@tauri-apps/cli": "^1.6.3", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "sass": "^1.87.0", "tailwindcss": "~3.4.17", "typescript": "^5.8.3", "vite": "^5.4.19", "vite-plugin-html": "^3.2.2"}, "pnpm": {"overrides": {"@langchain/core": "^0.2.0"}}}