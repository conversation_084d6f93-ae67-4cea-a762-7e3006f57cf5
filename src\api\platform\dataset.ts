import { http } from "@/utils/axios";

const base_url = "aigc/v1/dataset";
export function getDatasetList( params: any) {
  return http.request<API.TalbeDataInfo>({
    url: `${base_url}/list`,
    method: "GET",
    params,
  });
}

export function chuckDataset(data: any, ossId: any) {
  return http.request<API.Result>({
    url: `${base_url}/chunk/${ossId}`,
    method: "POST",
    data,
  });
}
export function getDataset(id: any) {
  return http.request<API.Result>({
    url: `${base_url}/${id}`,
    method: "GET",
  });
}
export function addDataset(data: any) {
  return http.request<API.Result>({
    url: `${base_url}`,
    method: "POST",
    data,
  });
}

export function updateStatus(id:any){
  return http.request<API.Result>({
    url: `${base_url}/change_status/${id}`,
    method: "PUT",
  });
}

export function deleteDataset(ids:any){
  return http.request<API.Result>({
    url: `${base_url}/${ids}`,
    method: "DELETE",
  })
}