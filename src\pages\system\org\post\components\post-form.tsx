import { Col, Form, Modal, Row } from "@douyinfe/semi-ui";
import React from "react";

export default function PostForm({
  title,
  open,
  onRefresh,
  id,
  onCancel,
}: any) {
  const formApiRef = React.useRef(null);
  const [loading, setLoading] = React.useState(false);
  const getFormApi = (formApi: any) => {
    formApiRef.current = formApi;
  };
  const handleReset = () => {
    // @ts-expect-error
    formApiRef.current?.reset();
    onCancel();
  };
  const handleAddSubmit = async (values: any) => {
    //TODO
  };
  const handleEditSubmit = async (values: any) => {
    //  TODO
  };
  const submitForm = async () => {
    try {
      setLoading(true);
      // @ts-expect-error
      const values = await formApiRef.current?.validate();
      id ? await handleEditSubmit(values) : await handleAddSubmit(values);
    } catch (errors) {
      console.error(errors);
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <Modal
        width={750}
        visible={open}
        title={title}
        centered
        onOk={submitForm}
        onCancel={handleReset}
        confirmLoading={loading}
        okText={"提交"}
      >
        <Form
          allowEmpty={true}
          wrapperCol={{ span: 24, offset: 4 }}
          className="w-full"
          getFormApi={getFormApi}
        >
          <Row gutter={[10, 10]}>
            <Col span={12}>
              <Form.Input
                field="postName"
                rules={[
                  { required: true, message: "岗位名称不能为空" },
                  {
                    min: 0,
                    max: 64,
                    message: "岗位名称长度必须在0-50之间",
                  },
                ]}
                label="岗位名称"
                placeholder={"请输入岗位名称"}
                required
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="postCode"
                rules={[
                  { required: true, message: "岗位编码不能为空" },
                  {
                    pattern: /^[a-zA-Z0-9]+$/,
                    message: "岗位编码只能包含字母和数字",
                  },
                  {
                    min: 0,
                    max: 64,
                    message: "岗位编码长度必须在0-64之间",
                  },
                ]}
                label="岗位编码"
                placeholder={"请输入岗位编码"}
                required
              />
            </Col>

            <Col span={12}>
              <Form.TreeSelect
                field="deptId"
                rules={[
                  { required: true, message: "关联部门不能为空" },
                ]}
                label="关联部门"
                filterTreeNode
                fieldClassName="w-full"
                className="w-full"
                placeholder={"请输入岗位编码"}
              />
            </Col>
            {/* 排序 */}
            <Col span={12}>
              <Form.InputNumber
                field="postSort"
                min={0}
                max={9999}
                className="w-full"
                fieldClassName="w-full"
                rules={[{ required: true, message: "排序不能为空" }]}
                label="排序"
                placeholder={"请输入排序"}
              />
            </Col>
            {/* 状态 */}
            <Col span={24}>
              <Form.RadioGroup
                field="status"
                label="状态"
                initValue={"0"}
                rules={[{ required: true, message: "必须选择是否独占 " }]}
              >
                <Form.Radio value="0">正常</Form.Radio>
                <Form.Radio value="1">停用</Form.Radio>
              </Form.RadioGroup>
            </Col>
            {/* 备注 */}
            <Col span={24}>
              <Form.TextArea
                field="remark"
                rules={[
                  { required: true, message: "备注不能为空" },
                  { min: 0, max: 200, message: "备注长度必须在0-200之间" },
                ]}
                label="备注"
                placeholder={"请输入备注"}
                required
              />
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
}
