import React, { useState } from 'react';
import { IconChevronDown, IconChevronUp } from '@douyinfe/semi-icons';
import { Button, Collapsible } from '@douyinfe/semi-ui';
import { MarkdownBox } from './Markdown/MarkdownBox';

interface ThinkingBlockProps {
  content: string;
}

const ThinkingBlock: React.FC<ThinkingBlockProps> = ({ content }) => {
  const [isOpen, setIsOpen] = useState(true);

  // 确保不会递归调用MarkdownBox
  const renderContent = () => {
    // 简单文本渲染，避免递归
    return (
      <div className="p-3 border-t border-solid border-semi-color-border italic text-semi-color-text-2 text-xs">
        {content}
      </div>
    );
  };

  return (
    <div className="thinking-block mb-4 bg-semi-color-fill-0 rounded-md border border-solid border-semi-color-border">
      <div
        className="px-2 py-1 flex justify-between items-center cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center">
          <span className="mr-2">💭</span>
          <span className="text-md">AI 思考过程</span>
        </div>
        <Button
          size="small"
          theme="borderless"
          type="tertiary"
          icon={isOpen ? <IconChevronUp /> : <IconChevronDown />}
          onClick={(e) => {
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
        />
      </div>
      <Collapsible isOpen={isOpen}>
        {renderContent()}
      </Collapsible>
    </div>
  );
};

export default ThinkingBlock;

