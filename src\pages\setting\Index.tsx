import React, { useEffect, useState } from "react";
import { Avatar, Dropdown, Layout, Nav, Toast } from "@douyinfe/semi-ui";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import {
  IconBox,
  IconChevronUpDown,
  IconHome,
  IconSemiLogo,
  IconSetting,
  IconUndo,
  IconUserGroup,
} from "@douyinfe/semi-icons";
import {
  IconBadge,
  IconConfig,
  IconForm,
  IconTree,
} from "@douyinfe/semi-icons-lab";

const SettingIndex = () => {
  const { Header, Footer, Sider, Content } = Layout;
  const [activeKey, setActiveKey] = useState<Array<string>>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const pathToTitle: { [key: string]: string } = {
    "/platform/normal": "通用设置",
    "/platform/role": "角色预设",
    "/platform/model": "模型管理",
    "/platform/plugin": "插件管理",
  };
  useEffect(() => {
    setActiveKey([location.pathname]);
    document.title = pathToTitle[location.pathname] || "LynkzHub";
  }, [location.pathname]);

  const jumpPage = (key: string) => {
    setActiveKey([key]);
    if (key === "/platform/normal") {
      navigate("/platform/normal");
    } else if (key === "/platform/role") {
      navigate("/platform/role");
    } else if (key === "/platform/app") {
      navigate("/platform/app");
    } else if (key === "/platform/plugin") {
      navigate("/platform/plugin");
    } else if (key === "/system/workbench") {
      navigate("/system/workbench");
    }
  };
  const backChat = () => {
    navigate("/comment");
  };
  return (
    <Layout className="full-height setting-index">
      <Sider className="w-[230px] p-2 h-full">
        <div className="setting-index-sider relative h-full rounded-lg w-[220px] ">
          <Dropdown
            trigger={"click"}
            className="w-[280px]"
            position="bottomRight"
            render={
              <Dropdown.Menu className="w-full">
                <Dropdown.Item className="w-full" icon={<IconBox />}>
                  Menu Item 1
                </Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <div className="flex items-center justify-between  mx-2 my-3 hover:bg-semi-color-fill-0 rounded-lg px-2 py-2 cursor-pointer">
              <div className="flex items-center gap-2">
                <Avatar
                  shape="square"
                  style={{ backgroundColor: "#dc6b82" }}
                  size="small"
                  alt="Alice Swift"
                >
                  <IconUserGroup />
                </Avatar>
                <div>
                  <div className="text-sm font-semibold">工作空间</div>
                  <div className="text-xs ">12312</div>
                </div>
              </div>
              <IconChevronUpDown />
            </div>
          </Dropdown>

          <div
            className="px-4 hover:bg-semi-color-fill-0 py-2 text-sm"
            onClick={backChat}
          >
            <div className="flex gap-2 items-center cursor-pointer">
              <IconUndo />
              <div>返回对话</div>
            </div>
          </div>
          <Nav
            style={{ width: "220px", padding: "0px" }}
            defaultOpenKeys={["platform"]}
            selectedKeys={activeKey}
            onSelect={(data) => {
              console.log(data);
              jumpPage(data.itemKey as string);
            }}
          >
            <Nav.Item
              itemKey={"/system/workbench"}
              text={"工作台"}
              icon={<IconHome />}
            />

            <Nav.Sub
              itemKey={"platform"}
              text="平台设置"
              icon={<IconSetting />}
            >
              <Nav.Item itemKey={"/platform/app"} text={"模型"} />
              <Nav.Item itemKey={"/platform/role"} text={"角色预设"} />
              <Nav.Item itemKey={"/platform/plugin"} text={"插件管理"} />
            </Nav.Sub>
          </Nav>
          <Dropdown
            trigger={"click"}
            className="w-[230px] min-h-[250px]"
            position="rightTop"
            render={
              <Dropdown.Menu className="w-full">
                <Dropdown.Item className="w-full" icon={<IconBox />}>
                  Menu Item 1
                </Dropdown.Item>
              </Dropdown.Menu>
            }
          >
            <div className="flex w-[200px] absolute bottom-0 items-center justify-start gap-2  mx-2 my-3 hover:bg-semi-color-fill-0 rounded-lg px-2 py-2 cursor-pointer">
              <Avatar
                style={{ backgroundColor: "#87d068" }}
                size="small"
                alt="Alice Swift"
              >
                AS
              </Avatar>
              <div>Herther</div>
            </div>
          </Dropdown>
        </div>
      </Sider>
      <Content className="h-full p-2">
        <Outlet />
      </Content>
    </Layout>
  );
};
export default SettingIndex;
