import { Spin } from "@douyinfe/semi-ui";
import React from "react";

export default function Loading({ detailLoading }:any) {
  return (
    <>
      {detailLoading && (
        <div className="flex-1 bg-semi-color-white  px-2 h-full w-full flex justify-center items-center">
          <Spin
            size="large"
            wrapperClassName="spin w-full h-full flex-1 overflow-hidden"
          />
        </div>
      )}
    </>
  );
}
