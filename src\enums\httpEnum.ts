/**
 * @description: 请求结果集
 */
export enum ResultEnum {
	SUCCESS = 200,
	ERROR = 500,
	TIMEOUT = 10042,
	NOT_AUTH = 401,
	NOT_FOUND = 404,
	NOT_PRMISSION = 403,
	TYPE = "success"
}

// export enum errorCode {}
// 401 = "认证失败，无法访问系统资源",
// 403 = "当前操作没有权限",
// 404 = "访问资源不存在",
// default = "系统出错了...."

/**
 * @description: 请求方法
 */
export enum RequestEnum {
	GET = "GET",
	POST = "POST",
	PATCH = "PATCH",
	PUT = "PUT",
	DELETE = "DELETE"
}

/**
 * @description:  常用的contentTyp类型
 */
export enum ContentTypeEnum {
	// json
	JSON = "application/json;charset=UTF-8",
	// json
	TEXT = "text/plain;charset=UTF-8",
	// form-data 一般配合qs
	FORM_URLENCODED = "application/x-www-form-urlencoded;charset=UTF-8",
	// form-data  上传
	FORM_DATA = "multipart/form-data;charset=UTF-8"
}
