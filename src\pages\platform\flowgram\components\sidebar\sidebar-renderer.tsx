import { useCallback, useContext, useEffect } from "react";

import {
  PlaygroundEntityContext,
  useRefresh,
  useClientContext,
} from "@flowgram.ai/free-layout-editor";
import { SideSheet, TextArea, Typography } from "@douyinfe/semi-ui";

import {
  SidebarContext,
  IsSidebarContext,
  NodeRenderContext,
} from "../../context";
import { getIcon } from "../../form-components/form-header/utils";
import { Icon } from "../../form-components/form-header/styles";

export const SidebarRenderer = () => {
  const { nodeRender, setNodeRender } = useContext(SidebarContext);
  const { selection, playground } = useClientContext();
  const refresh = useRefresh();
  const handleClose = useCallback(() => {
    setNodeRender(undefined);
  }, []);
  /**
   * Listen readonly
   */
  useEffect(() => {
    const disposable = playground.config.onReadonlyOrDisabledChange(() =>
      refresh()
    );
    return () => disposable.dispose();
  }, [playground]);
  /**
   * Listen selection
   */
  useEffect(() => {
    const toDispose = selection.onSelectionChanged(() => {
      /**
       * 如果没有选中任何节点，则自动关闭侧边栏
       * If no node is selected, the sidebar is automatically closed
       */
      if (selection.selection.length === 0) {
        handleClose();
      } else if (
        selection.selection.length === 1 &&
        selection.selection[0] !== nodeRender?.node
      ) {
        handleClose();
      }
    });
    return () => toDispose.dispose();
  }, [selection, handleClose]);
  /**
   * Close when node disposed
   */
  useEffect(() => {
    if (nodeRender) {
      const toDispose = nodeRender.node.onDispose(() => {
        setNodeRender(undefined);
      });
      return () => toDispose.dispose();
    }
    return () => {};
  }, [nodeRender]);

  if (playground.config.readonly) {
    return null;
  }
  /**
   * Add key to rerender the sidebar when the node changes
   */
  const content = nodeRender ? (
    <PlaygroundEntityContext.Provider
      key={nodeRender.node.id}
      value={nodeRender.node}
    >
      <NodeRenderContext.Provider value={nodeRender}>
        {nodeRender.form?.render()}
      </NodeRenderContext.Provider>
    </PlaygroundEntityContext.Provider>
  ) : null;

  const title = nodeRender?.form?.values?.title || "节点属性";
  // console.log();

  // const registry = nodeRender.getNodeRegistry<FlowNodeRegistry>();

  // const titleRender = () => {
  //   return (

  //   );
  // };
  // console.log(nodeRender);
  // useEffect(() => {
  //   console.log(document.querySelector(".editor-container"));
  // }, []);
  // const icon = getIcon(nodeRender?.node);

  return (
    // getPopupContainer={()=>document.querySelector('.flowgram-editor')!}
    <SideSheet
      // getPopupContainer={() =>
      //   document.querySelector(".editor-container") ?? document.body
      // }
      // getPopupContainer={() => document.querySelector(".flowgram-editor")!}
      mask={false}
      headerStyle={{
        padding: "10px 10px",
        paddingTop: "10px",
        paddingBottom: "10px",
        borderBottom: "1px solid var(--semi-color-border)",
      }}
      bodyStyle={{ paddingTop: "12px" }}
      title={
        <div className="sidebar-title">
          <div className="flex items-center gap-2">
            <Icon src={nodeRender?.node.getNodeRegistry()?.info.icon} />
            <div className="sidebar-title-text">{title}</div>
          </div>
          <div className="text-[12px] text-semi-color-text-2 font-normal  w-[350px] line-clamp-2  leading-4 hover:bg-semi-color-fill-0 cursor-pointer p-1 text-wrap rounded-md select-none">
            {nodeRender?.node.getNodeRegistry()?.info?.description}
          </div>
          {/* <TextArea autosize style={{ width: 320, marginTop: 4 }} rows={3} /> */}
        </div>
      }
      visible={!!nodeRender}
      onCancel={handleClose}
    >
      <IsSidebarContext.Provider value={true}>
        {content}
      </IsSidebarContext.Provider>
    </SideSheet>
  );
};
