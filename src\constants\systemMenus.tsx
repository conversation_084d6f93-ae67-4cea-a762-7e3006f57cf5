import AgentIcon from "@/components/icon/AgentIcon";
import DocIcon from "@/components/icon/DocIcon";
import Log from "@/components/icon/Log";
import McpIcon from "@/components/icon/McpIcon";
import Org from "@/components/icon/org";
import Resource from "@/components/icon/Resource";
import Setting from "@/components/icon/Setting";
import SettingIcon from "@/components/icon/SettingIcon";
import { MessageShare } from "@/components/MessageShare";
import Icon, {
  IconAlignVBotStroked,
  IconBox,
  IconElementStroked,
  IconFile,
  IconHistogram,
  IconHistory,
  IconIdCard,
  IconOrderedListStroked,
  IconSectionStroked,
  IconSetting,
  IconSettingStroked,
  IconStarStroked,
  IconUserStroked,
  IconVennChartStroked,
} from "@douyinfe/semi-icons";
import { IconIntro } from "@douyinfe/semi-icons-lab";
import {
  AppWindow,
  Book,
  Bot,
  Box,
  ChartColumnIncreasing,
  MessageSquareText,
  Sparkle,
  SquareTerminal,
  TvMinimal,
  Wrench,
} from "lucide-react";

// In systemMenus.tsx
export const PlateMenus = [
  {
    label: "首页",
    key: "/home",
    icon: (
      <Icon svg={<MessageSquareText />} className="text-semi-color-text-1" />
    ),
  },
  {
    label: "应用管理",
    key: "/platform/app",
    icon: <Icon svg={<SquareTerminal />} className="text-semi-color-text-1" />,
    children: [
      {
        label: "智能体",
        key: "/platform/app/index", // Match actual route path
        icon: (
          <Icon svg={<SquareTerminal />} className="text-semi-color-text-1" />
        ),
      },
    ],
  },
  {
    label: "资源管理",
    key: "/platform/resource",
    icon: <Icon svg={<Sparkle />} className="text-semi-color-text-1" />,
    children: [
      {
        key: "/platform/resource/mcp",
        label: "MCP",
        icon: <Icon svg={<TvMinimal size="16px" />} />,
      },
      {
        key: "/platform/resource/tools",
        label: "工具",
        icon: <Icon svg={<Wrench size="16px" />} />,
      },
      {
        key: "/platform/resource/knowledge",
        label: "知识库",
        icon: <Icon svg={<Book size="16px" />} />,
      },
      {
        key: "/platform/resource/prompt",
        label: "提示词",
        icon: <Icon svg={<SquareTerminal size="16px" />} />,
      },
    ],
  },
] as Array<MenuProps>;

export const System = [
  {
    key: "/system",
    label: "系统管理",
    icon: <IconSettingStroked />,
    children: [
      {
        key: "/system/notice",
        label: "通知公告",
      },
      {
        key: "/system/dictionary",
        label: "字典管理",
      },
      {
        key: "/system/oss",
        label: "存储配置",
      },
      {
        key: "/system/files",
        label: "文件管理",
      },
      {
        key: "/system/client",
        label: "客户端管理",
      },
      {
        key: "/system/model",
        label: "模型管理",
      },
    ],
  },
  {
    key: "/system/log",
    label: "系统日志",
    icon: <Icon svg={<Log />} />,
    children: [
      {
        key: "/system/log/login-log",
        label: "登录日志",
      },
      {
        key: "/system/log/oper-log",
        label: "操作日志",
      },
      {
        key: "/system/log/online",
        label: "在线用户",
      },
    ],
  },
  {
    key: "/system/org",
    label: "组织架构",
    icon: <Icon svg={<Org />} />,
    children: [
      {
        key: "/system/org/user",
        label: "用户管理",
      },
      {
        key: "/system/org/post",
        label: "岗位管理",
      },
    ],
  },
] as Array<MenuProps>;

export const Platform = [
  {
    label: "首页",
    key: "/home",
    icon: <Icon svg={<ChartColumnIncreasing  size="1em" />} />,
  },
  {
    key: "/platform/app/index",
    label: "智能体",
    icon: <Icon svg={<Bot size="1em"/>}  />,
  },
  {
    key: "/platform/resource",
    label: "资源管理",
    icon: <Icon svg={<AppWindow size="1em" />}  />,
    children: [
      {
        key: "/platform/resource/mcp",
        label: "MCP管理",
      },
      {
        key: "/platform/resource/tools",
        label: "工具管理",
      },
      {
        key: "/platform/resource/knowledge",
        label: "知识库管理",
      },
      {
        key: "/platform/resource/prompt",
        label: "提示词模板",
      },
    ],
  },
] as Array<MenuProps>;

export const SystemMenus = [
  {
    key: "/system",
    label: "系统管理",
    icon: <IconSettingStroked />,
    children: [
      {
        key: "/system/dictionary",
        label: "字典管理",
      },
      {
        key: "/system/oss",
        label: "存储配置",
      },
      {
        key: "/system/files",
        label: "文件管理",
      },
      {
        key: "/system/client",
        label: "客户端管理",
      },
    ],
  },
  {
    key: "/system/log",
    label: "系统日志",
    icon: <IconSectionStroked />,
    children: [
      {
        key: "/system/log/login-log",
        label: "登录日志",
      },
      {
        key: "/system/log/oper-log",
        label: "操作日志",
      },
      {
        key: "/system/log/online",
        label: "在线用户",
      },
    ],
  },
] as Array<MenuProps>;

export const findSystemMenu = (key: string): MenuProps => {
  return System.find((menu) => menu.key == key) as MenuProps;
};

export const AiMenus = [
  {
    key: "/platform/app",
    label: "智能体应用",
    icon: <Icon svg={<AgentIcon />} size="large" />,
  },
  {
    key: "/platform/mcp",
    label: "MCP管理",
    icon: <Icon svg={<McpIcon />} size="large" />,
  },
  // { itemKey: "/platform/resource", text: "资源管理", icon: <ResourceIcon /> },
  {
    key: "/platform/knowledge",
    label: "知识库管理",
    icon: <Icon svg={<DocIcon />} size="large" />,
  },
  {
    key: "/platform/setting",
    label: "系统管理",
    icon: <Icon svg={<SettingIcon />} size="large" />,
  },
];

export const getMenuByKey = (key: string) => {
  if (key == "platform") {
    return AiMenus;
  }
  if (key == "system") {
    return SystemMenus;
  }
  return [];
};
export const findMenuByKey = (
  menus: Array<MenuProps>,
  key: string
): MenuProps | undefined => {
  for (const menu of menus) {
    if (menu.key === key) {
      return menu;
    }
    if (menu.children) {
      const found = findMenuByKey(menu.children, key);
      if (found) {
        return found;
      }
    }
  }
  return undefined;
};
export const findPlateMenu = (path: string): MenuProps => {
  for (const menu of PlateMenus) {
    if (menu.children) {
      const subMatch = menu.children.find((child) => child.key === path);
      if (subMatch) return menu;
    }
    if (menu.key === path) return menu;
  }
  const match = PlateMenus.find((menu) => {
    if (menu.children) {
      return menu.children.some((child) => path.startsWith(child.key));
    }
    return path.startsWith(menu.key);
  });

  return match || PlateMenus[0];
};
