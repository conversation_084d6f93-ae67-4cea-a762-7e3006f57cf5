import { updateAgentModelConfig } from "@/api/platform/agent";
import { IconIssueStroked } from "@douyinfe/semi-icons";
import {
  Modal,
  Form,
  Slider,
  InputNumber,
  RadioGroup,
  Radio,
  Tooltip,
  Banner,
  Toast,
} from "@douyinfe/semi-ui";

import React, { useEffect, useState } from "react";

export default function ModelSetting({
  open,
  onCancel,
  onOk,
  selectModelId,
  detailData,
}: any) {
  const [formData, setFormData] = useState<any>({
    temperature: 0.7,
    topP: 1,
    frequencyPenalty: 0,
    maxTokens: 2048,
    replyType: "TEXT",
  });

  // 初始化赋值
  useEffect(() => {
    if (open && detailData?.modelConfig) {
      setFormData({
        id: detailData.modelConfig.id,
        temperature: detailData.modelConfig.temperature ?? 0.7,
        topP: detailData.modelConfig.topP ?? 1,
        frequencyPenalty: detailData.modelConfig.frequencyPenalty ?? 0,
        maxTokens: detailData.modelConfig.maxTokens ?? 2048,
        replyType: detailData.modelConfig.replyType ?? "TEXT",
        selectModelId: selectModelId,
      });
    }
  }, [open, detailData]);

  // 同步更新函数
  const handleChange = (field: string, value: number | string) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOk = () => {
    const { temperature, topP, maxTokens } = formData;

    if (temperature < 0 || temperature > 1) {
      return Toast.error("温度必须在 0~1 之间");
    }

    if (topP < 0 || topP > 1) {
      return Toast.error("Top P 必须在 0~1 之间");
    }

    if (maxTokens < 0 || maxTokens > 4096) {
      return Toast.error("最大回复长度必须在 0~4096 之间");
    }
    console.log(formData);
    //这里需要调用后端的接口保存模型配置
    //这里需要把formData 数字类型转为小数
    handleModelConfig(formData);
    // onOk(formData);
  };

  const handleModelConfig = (formData: any) => {
    updateAgentModelConfig(formData).then((res) => {
      Toast.success(res.msg);
      onOk();
    });
  };
  return (
    <Modal
      title="模型配置"
      visible={open}
      bodyStyle={{
        minHeight: 400,
        display: "flex",
      }}
      width={550}
      onCancel={onCancel}
      onOk={handleOk}
    >
      <div className="w-full flex flex-col gap-3">
        <Banner
          fullMode={false}
          type="info"
          className="mb-2"
          bordered
          icon={null}
          closeIcon={null}
          title={<div className="text-[16px] font-semibold">已选择模型</div>}
          description={
            <div className="text-semi-color-text-1 text-sm">
              你可先联系对应的研发同学，确认是否已在
              申请了应用，并填写对应的信息。
            </div>
          }
        />
        {/* 温度 */}
        <div className="w-full flex flex-col">
          <div className="flex gap-2 items-center">
            <div>生成随机性</div>
            <Tooltip
              position="bottom"
              content={"temperature 越大，输出的文本的多样性越大，随机性越高。"}
            >
              <IconIssueStroked className="text-semi-color-text-2" />
            </Tooltip>
          </div>
          <div className="flex-1 flex-row flex">
            <div className="flex-1">
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={formData.temperature}
                onChange={(v: any) => handleChange("temperature", v)}
              />
            </div>
            <InputNumber
              value={formData.temperature}
              onChange={(v: any) => handleChange("temperature", v)}
              // precision={2}
              min={0}
              max={1}
              step={0.1}
              style={{ width: 150 }}
            />
          </div>
        </div>

        {/* Top P */}
        <div className="w-full flex flex-col">
          <div className="flex gap-2 items-center">
            <div>Top p</div>
            <Tooltip
              position="bottom"
              content={
                "Top p 是一个概率阈值，它控制了模型在生成文本时考虑的概率。"
              }
            >
              <IconIssueStroked className="text-semi-color-text-2" />
            </Tooltip>
          </div>
          <div className="flex-1 flex-row flex">
            <div className="flex-1 ">
              <Slider
                min={0}
                max={1}
                step={0.1}
                value={formData.topP}
                onChange={(v: any) => handleChange("topP", v)}
              />
            </div>
            <InputNumber
              value={formData.topP}
              onChange={(v: any) => handleChange("topP", v)}
              min={0}
              max={1}
              step={0.1}
              style={{ width: 150 }}
            />
          </div>
        </div>

        {/* 频率惩罚 */}
        <div className="w-full flex flex-col">
          <div className="flex gap-2 items-center">
            <div>重复语句惩罚</div>
            <Tooltip
              position="bottom"
              content={
                "频率惩罚（frequencyPenalty）是一个介于-2到2之间的值，它控制了模型在生成文本时考虑重复语句的频率。"
              }
            >
              <IconIssueStroked className="text-semi-color-text-2" />
            </Tooltip>
          </div>
          <div className="flex-1 flex-row flex">
            <div className="flex-1 ">
              <Slider
                min={-2}
                max={2}
                step={0.01}
                value={formData.frequencyPenalty}
                onChange={(v: any) => handleChange("frequencyPenalty", v)}
                style={{ flex: 1 }}
              />
            </div>
            <InputNumber
              value={formData.frequencyPenalty}
              onChange={(v: any) => handleChange("frequencyPenalty", v)}
              min={-2}
              max={2}
              step={0.01}
              style={{ width: 150 }}
            />
          </div>
        </div>

        {/* 最大回复长度 */}
        <div className="w-full flex flex-col">
          <div className="flex gap-2 items-center">
            <div>最大回复长度</div>
            <Tooltip
              position="bottom"
              content={
                "最大回复长度（maxTokens）是一个介于0到4096之间的整数，它控制了模型在生成文本时生成的最大长度。"
              }
            >
              <IconIssueStroked className="text-semi-color-text-2" />
            </Tooltip>
          </div>
          <div className="flex-1 flex-row flex">
            <div className="flex-1 ">
              <Slider
                min={0}
                max={4096}
                step={1}
                value={formData.maxTokens}
                onChange={(v) => handleChange("maxTokens", v)}
                style={{ flex: 1 }}
              />
            </div>
            <InputNumber
              min={0}
              max={4096}
              step={1}
              value={formData.maxTokens}
              onChange={(v) => handleChange("maxTokens", v)}
              style={{ width: 150 }}
            />
          </div>
        </div>
        {/* 回复格式 */}
        <div className="flex w-full flex-col">
          <div className="flex gap-2 items-center">
            <div>回复格式</div>
            <Tooltip
              position="bottom"
              content={
                "回复格式（replyType）是一个字符串，它控制了模型在生成文本时返回的格式。"
              }
            >
              <IconIssueStroked className="text-semi-color-text-2" />
            </Tooltip>
          </div>
          <div className="flex w-full items-center flex-1 justify-between">
            <RadioGroup
              type="button"
              defaultValue={formData.replyType}
              onChange={(v: any) => handleChange("replyType", v.target.value)}
            >
              <Radio value={"TEXT"}>文本格式</Radio>
              <Radio value={"JSON"}>JSON对象</Radio>
            </RadioGroup>
          </div>
        </div>
      </div>
    </Modal>
  );
}
