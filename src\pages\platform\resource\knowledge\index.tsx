import { Knowledge as KnowledgeIcon } from "@/components/icon/KnowledgeIcon";
import Icon, { IconMore, IconPlus, IconSearch } from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Empty,
  Input,
  List,
  OverflowList,
  Pagination,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useState, useEffect } from "react";
import KnowledgeForm from "./component/KnowledgeForm";
import { useBoolean } from "@/hooks";
import { getKnowledgeList } from "@/api/platform/knowledge";
import {
  IllustrationNoContentDark,
  IllustrationNoResult,
  IllustrationNoResultDark,
} from "@douyinfe/semi-illustrations";
import { useNavigate } from "@tanstack/react-router";
import EmtpyBox from "@/components/icon/EmtpyBox";
import { useTable } from "@/hooks/useTables";
import { debounce } from "lodash-es";

function Knowledge() {
  const [formOpen, { setTrue, setFalse }] = useBoolean();
  const navigate = useNavigate();
  const {
    dataSource,
    loading,
    pagination,
    refresh,
    setSearchParams,
  } = useTable({
    api: getKnowledgeList,
    columns: [],
    onRowSelect: (selectedRowKeys: any, selectedRows: any) => {},
  });
  const handleGotoDetails = (item: any) => {
    navigate({
      to: `/platform/resource/knowledge/details/${item.id}`,
    });
  };

  const renderItem = (item: string, index: number) => (
    <Tag color="blue" key={index} style={{ marginRight: 8, flex: "0 0 auto" }}>
      {item}
    </Tag>
  );

  const renderOverflow = (items: string[]) =>
    items.length ? (
      <Tag style={{ flex: "0 0 auto", fontVariantNumeric: "tabular-nums" }}>
        +{items.length}
      </Tag>
    ) : null;

  return (
    <div className="h-full w-full overflow-auto relative flex flex-col py-2 px-2">
      {/* 标题和操作区域 */}
      <div className="text-nowrap font-extrabold pl-4 gap-2 pt-2 text-lg mb-2 flex-col flex  justify-between">
        <div className="flex flex-row justify-between items-center">
          <div>知识库</div>
          <div className="flex gap-2 items-center">
            <Button theme="solid" type="primary" onClick={setTrue}>
              创建知识库
            </Button>
          </div>
        </div>
        <div className="flex justify-end flex-row">
          <div>
            {/* 接口请求 */}
            <Input
              prefix={<IconSearch />}
              className="w-[300px]"
              showClear
              onChange={(v) => {
                // 防抖使用l odash-es
                debounce(() => {
                  debugger;
                  setSearchParams({
                    name: v,
                  });
                  refresh();
                }, 500);
              }}
            />
          </div>
        </div>
      </div>

      {/* 分页列表区域 */}
      <div
        style={{ height: "calc(100vh - 120px)" }}
        className="overflow-y-auto overflow-x-hidden"
      >
        <List
          split={false}
          className="h-full"
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          loading={loading}
          emptyContent={
            <Empty
              image={<EmtpyBox />}
              darkModeImage={<IllustrationNoContentDark />}
              description="暂无数据"
            />
          }
          dataSource={dataSource}
          layout="horizontal"
          renderItem={(item) => (
            <List.Item
              className="w-full"
              onClick={(e) => {
                e.stopPropagation();
                handleGotoDetails(item);
              }}
            >
              {/* 卡片具体内容 */}
              <Card
                className="w-full"
                bodyStyle={{ width: "100%", padding: "20px" }}
                shadows="hover"
                footer={
                  <div className="flex justify-between items-center">
              
                    <div className="flex gap-1 items-center">
                      <Typography.Text type="secondary">
                        {item.createdAt}
                      </Typography.Text>
                      {/* 操作下拉菜单 */}
                      <Dropdown
                        position="bottom"
                        className="w-[120px]"
                        render={
                          <Dropdown.Menu>
                            <Dropdown.Item onClick={(e) => e.stopPropagation()}>
                              修改
                            </Dropdown.Item>
                            <Dropdown.Divider />
                            <Dropdown.Item
                              type="danger"
                              onClick={(e) => e.stopPropagation()}
                            >
                              删除
                            </Dropdown.Item>
                          </Dropdown.Menu>
                        }
                      >
                        <Button
                          theme="borderless"
                          type="tertiary"
                          onClickCapture={(e) => e.stopPropagation()}
                          icon={<IconMore />}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </Dropdown>
                    </div>
                  </div>
                }
              >
                {/* 卡片内容 */}
                <Card.Meta
                  avatar={<Icon svg={<KnowledgeIcon />} />}
                  title={
                    <div className="flex flex-col">
                      <Typography.Text strong>{item.name}</Typography.Text>
                      <Typography.Text
                        type="tertiary"
                        className="text-sx"
                        size="small"
                      >
                        {item.createdByName}
                      </Typography.Text>
                    </div>
                  }
                />
                <Typography.Text
                  type="secondary"
                  ellipsis={{
                    rows: 1,
                    showTooltip: { content: item.description },
                  }}
                  className="text-sm px-1 py-1"
                >
                  {item.description}
                </Typography.Text>
              </Card>
            </List.Item>
          )}
        />
      </div>
      {pagination.total > 0 && (
        <div className="flex justify-between mb-2 items-center px-2 py-2  rounded-lg">
          <div className="text-sm text-semi-color-text-2 ">
            显示第 {pagination.currentPage} 页- 每页 {pagination.pageSize}{" "}
            条，共 {pagination.total} 条
          </div>
          <Pagination {...pagination} />
        </div>
      )}

      {/* 创建知识库弹窗 */}
      <KnowledgeForm
        open={formOpen}
        onCancel={setFalse}
        onOK={setFalse}
        knowledgeId={undefined}
      />
    </div>
  );
}

export default Knowledge;
