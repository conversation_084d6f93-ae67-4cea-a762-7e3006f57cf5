import { resetPwd } from '@/api/system/user'
import { Banner, Button, Form, Modal, Progress, TabPane, Tabs, Tag, Toast } from '@douyinfe/semi-ui'
import React from 'react'

export default function RestPassword({ open, onCancel, id, onRefresh }: any) {
    const [type, setType] = React.useState<any>("default")
    const [passwordStrength, setPasswordStrength] = React.useState<'weak' | 'medium' | 'strong' | null>(null)
    const [strengthPercent, setStrengthPercent] = React.useState<number>(0)
    const [loading, setLoading] = React.useState(false);
    const formApiRef = React.useRef(null);
    const getFormApi = (formApi: any) => {
        formApiRef.current = formApi;
    };
    // 密码强度检测逻辑
    const checkPasswordStrength = (password: string) => {
        let strengthLevel = 0

        if (password.length >= 6) strengthLevel++
        if (/[A-Z]/.test(password)) strengthLevel++ // 包含大写字母
        if (/[0-9]/.test(password)) strengthLevel++ // 包含数字
        if (/[^A-Za-z0-9]/.test(password)) strengthLevel++ // 包含特殊字符

        let percent = 0
        if (strengthLevel === 1) {
            percent = 33
        } else if (strengthLevel === 2) {
            percent = 66
        } else if (strengthLevel >= 3) {
            percent = 100
        }

        setStrengthPercent(percent)

        if (strengthLevel < 2) {
            setPasswordStrength('weak')
        } else if (strengthLevel === 2) {
            setPasswordStrength('medium')
        } else {
            setPasswordStrength('strong')
        }
    }

    const getStrengthConfig = () => {
        switch (passwordStrength) {
            case 'weak':
                return { color: 'var(--semi-color-danger)', tagColor: "red", text: '弱', tip: '建议包含大小写字母、数字和符号' }
            case 'medium':
                return { color: 'var(--semi-color-warning)', tagColor: "yellow", text: '中', tip: '密码强度中等，可以更复杂一些' }
            case 'strong':
                return { color: '', text: '强', tagColor: "green", tip: '密码强度良好' }
            default:
                return { color: 'default', tagColor: "red", text: '', tip: '' }
        }
    }

    const config = getStrengthConfig() as any;
    const handleReset = () => {
        // @ts-expect-error
        formApiRef.current?.reset();
        onCancel();
    };
    const handleSubmit = async (type: string) => {
        try {
            setLoading(true);
            if (type === 'default') {
                resetPwd({
                    userId: id,
                }, type).then(({ msg }) => {
                    Toast.success(msg)
                    handleReset()
                })
            } else {
                const values = await formApiRef.current?.validate();
                await resetPwd({
                    userId: id,
                    password: values.password,
                }, type).then(({ msg }) => {
                    Toast.success(msg)
                    handleReset()
                    setPasswordStrength(null)
                    setStrengthPercent(0)
                    onRefresh()
                })
            }
        } finally {
            setLoading(false);
        }
    }
    return (
        <Modal
            width={600}
            visible={open}
            title={"修改密码"}
            centered
            bodyStyle={{
                minHeight: 300,
            }}
            footer={
                <div className="flex flex-row items-center justify-end gap-2">
                    <Button type="tertiary" onClick={onCancel}>
                        取消
                    </Button>
                    {
                        type !== "default" && (
                            <Button loading={loading} theme="solid" type="primary" onClick={() => handleSubmit("custom")}>
                                修改密码
                            </Button>
                        )
                    }
                </div>
            }
            onCancel={onCancel}
            okText={"提交"}
        >

            <Tabs activeKey={type} onTabClick={
                (key: any) => {
                    setType(key)
                }
            }>
                <TabPane
                    tab={
                        <span>
                            系统默认
                        </span>
                    }
                    itemKey="default"
                >
                    <Banner className='mt-2' fullMode={false} type="warning" bordered icon={null} closeIcon={null}
                        description={<div>使用系统配置的默认密码进行重置</div>}
                    />
                    <Button loading={loading} className='mt-4' size="large" theme='solid' onClick={() => handleSubmit("default")} >重置系统默认密码</Button>

                </TabPane>
                <TabPane
                    tab={
                        <span>
                            自定义密码
                        </span>
                    }
                    itemKey="custom"
                >

                    <Form getFormApi={getFormApi} onValueChange={(values) => {
                        if (values.password) {
                            checkPasswordStrength(values.password)
                        } else {
                            // 清空密码时重置状态
                            setPasswordStrength(null)
                            setStrengthPercent(0)
                        }
                    }}>
                        <Form.Input showClear rules={[
                            { required: true, message: '请输入新密码' },
                            {
                                min: 6, message: '密码长度不能小于6位'
                            }
                        ]} placeholder={"请输入新密码"} label="新密码"

                            field='password' mode="password" />
                        {/* 密码强度 */}
                        {/* 密码强度提示 */}
                        {passwordStrength && (
                            <div className="mt-2">
                                <div className="flex items-center justify-between mb-1">
                                    <Tag color={config.tagColor} size="small">
                                        密码强度：{config.text}
                                    </Tag>
                                    <span className="text-xs text-semi-color-text-2">{config.tip}</span>
                                </div>
                                <Progress
                                    percent={strengthPercent}
                                    size="small"
                                    stroke={config.color as any}
                                    style={{ height: 10 }}
                                />
                            </div>
                        )}
                    </Form>
                </TabPane>
            </Tabs>
        </Modal>
    )
}
