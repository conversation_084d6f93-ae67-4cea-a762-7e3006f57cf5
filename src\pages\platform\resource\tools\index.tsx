import { Knowledge as KnowledgeIcon } from "@/components/icon/KnowledgeIcon";
import Icon, { IconMore, IconPlus, IconSearch } from "@douyinfe/semi-icons";
import {
  Avatar,
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Empty,
  Input,
  List,
  OverflowList,
  Pagination,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
} from "@douyinfe/semi-ui";
import React, { useState, useEffect } from "react";
import { useBoolean } from "@/hooks";
import { getKnowledgeList } from "@/api/platform/knowledge";
import {
  IllustrationNoContentDark,
  IllustrationNoResult,
  IllustrationNoResultDark,
} from "@douyinfe/semi-illustrations";
import { useNavigate } from "@tanstack/react-router";
import EmtpyBox from "@/components/icon/EmtpyBox";
import { useTable } from "@/hooks/useTables";
import { debounce } from "lodash-es";
import { MdEditorDemo } from "@/components/MdEditor";


function Tools() {
  const [formOpen, { setTrue, setFalse }] = useBoolean();
  const navigate = useNavigate();
  return (
    <div className="h-full w-full overflow-auto relative flex flex-col py-2 px-2">
      {/* 标题和操作区域 */}
      <div className="text-nowrap font-extrabold pl-4 gap-2 pt-2 text-lg mb-2 flex-col flex  justify-between">
        <div className="flex flex-row justify-between items-center">
          <div>工具列表</div>
          <div className="flex gap-2 items-center">
            <Button theme="solid" type="primary" onClick={setTrue}>
              创建工具
            </Button>
          </div>
        </div>
        <div className="flex justify-end flex-row">
          <div>
            {/* 接口请求 */}
            <Input
              prefix={<IconSearch />}
              className="w-[300px]"
              showClear
            />
          </div>
        </div>
      </div>

      {/* Markdown 提示词编辑器 */}
      <div className="flex-1 overflow-y-auto w-full">
        <MdEditorDemo />
      </div>

    </div>
  );
}
export default Tools;
