import React from "react";

export default function MofaIcon() {
  return (
    <svg
      t="1745072980800"
      className="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="7450"
      width="3em"
      height="3em"
    >
      <path
        d="M438.499556 704.625778c0.170667-4.664889 7.964444-6.542222 10.24-2.446222 10.808889 19.171556 29.240889 47.388444 50.176 60.245333 20.878222 12.856889 54.442667 16.497778 76.458666 17.408 4.664889 0.170667 6.485333 7.964444 2.389334 10.24-19.171556 10.808889-47.331556 29.240889-60.188445 50.176-12.856889 20.878222-16.497778 54.442667-17.408 76.458667-0.227556 4.664889-7.964444 6.485333-10.24 2.446222-10.808889-19.228444-29.240889-47.388444-50.232889-60.245334-20.878222-12.856889-54.442667-16.497778-76.401778-17.408-4.664889-0.170667-6.542222-7.964444-2.446222-10.24 19.171556-10.808889 47.388444-29.240889 60.245334-50.232888 12.8-20.878222 16.497778-54.385778 17.408-76.401778z"
        fill="#6A62FF"
        p-id="7451"
      ></path>
      <path
        d="M788.992 661.959111c-0.398222-4.664889-8.533333-6.656-11.036444-2.616889-7.338667 12.060444-17.92 26.567111-29.696 33.848889-11.832889 7.224889-29.582222 10.012444-43.633778 11.150222-4.664889 0.341333-6.656 8.533333-2.616889 10.979556 12.003556 7.338667 26.567111 17.92 33.848889 29.752889 7.224889 11.776 10.012444 29.582222 11.150222 43.633778 0.341333 4.664889 8.533333 6.599111 10.979556 2.56 7.338667-12.003556 17.92-26.567111 29.752888-33.792 11.776-7.168 29.582222-10.069333 43.633778-11.150223 4.664889-0.398222 6.599111-8.533333 2.56-11.036444-12.003556-7.338667-26.567111-17.92-33.792-29.696-7.168-11.832889-10.069333-29.582222-11.150222-43.633778z"
        fill="#6A62FF"
        opacity=".5"
        p-id="7452"
      ></path>
      <path
        d="M627.313778 170.723556l-10.296889-13.027556c-39.992889-50.460444-59.960889-75.719111-83.228445-72.021333-23.210667 3.754667-34.304 33.962667-56.547555 94.435555l-5.745778 15.587556c-6.314667 17.180444-9.500444 25.770667-15.530667 32.085333-6.087111 6.314667-14.336 9.614222-30.890666 16.213333l-15.075556 6.030223-10.524444 4.266666c-51.2 20.48-76.970667 32.142222-80.327111 54.727111-3.470222 24.120889 20.935111 44.771556 69.745777 86.016l12.629334 10.695112c13.880889 11.719111 20.821333 17.635556 24.746666 25.6 3.982222 7.964444 4.551111 17.180444 5.688889 35.612444l1.024 16.782222c3.982222 64.853333 5.973333 97.28 27.079111 108.430222 21.048889 11.207111 47.274667-6.257778 99.669334-41.187555l13.596444-9.102222c14.848-9.898667 22.300444-14.904889 30.833778-16.213334 8.533333-1.308444 17.066667 1.024 34.417778 5.802667l15.644444 4.323556c60.700444 16.839111 91.022222 25.258667 107.52 8.021333 16.554667-17.180444 8.362667-48.64-8.078222-111.502222l-4.266667-16.213334c-4.664889-17.863111-6.997333-26.794667-5.688889-35.612444 1.251556-8.874667 6.030222-16.611556 15.587556-32.085333l8.647111-14.108445c33.507556-54.442667 50.289778-81.635556 39.424-103.537778-10.865778-21.731556-42.097778-23.722667-104.675555-27.591111l-16.213334-1.024c-17.749333-1.137778-26.624-1.706667-34.360889-5.802666-7.736889-4.096-13.425778-11.264-24.746666-25.6"
        fill="#6A62FF"
        p-id="7453"
      ></path>
      <path
        d="M376.945778 568.547556c-91.192889 44.600889-167.082667 115.143111-195.697778 199.452444-32.085333-200.817778 12.458667-330.524444 83.854222-411.192889 6.144 12.572444 14.165333 22.983111 21.333334 31.175111 14.904889 16.896 36.352 34.986667 58.083555 53.361778l15.644445 13.198222 7.281777 6.257778c0.227556 2.673778 0.398222 5.916444 0.625778 10.069333l1.308445 20.707556c1.706667 27.932444 3.413333 55.182222 7.566222 76.970667z"
        fill="#6A62FF"
        opacity=".5"
        p-id="7454"
      ></path>
    </svg>
  );
}
