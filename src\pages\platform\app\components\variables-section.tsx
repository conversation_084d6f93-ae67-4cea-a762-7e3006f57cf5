import React from 'react';
import { Table, Button, Icon, Divider, Collapsible } from '@douyinfe/semi-ui';
import { IconChevronDown, IconChevronUp, IconDeleteStroked, IconEdit, IconPlus } from '@douyinfe/semi-icons';

interface VariableData {
    key: string;
    label: string;
    required: boolean;
    maxLength?: number;
    description?: string;
    options?: Array<{ name: string }>;
    type: "text" | "number" | "options" | "custom";
}

interface VariablesSectionProps {
    variables: VariableData[];
    varOpen: boolean;
    toggleVar: () => void;
    openVarModel: () => void;
    formApi: any;
}

const VariablesSection: React.FC<VariablesSectionProps> = ({
    variables,
    varOpen,
    toggleVar,
    openVarModel,
    formApi,
}) => {
    const columns = [
        { title: '变量', dataIndex: 'key' },
        { title: '默认值', dataIndex: 'defaultValue' },
        { title: '描述', dataIndex: 'description' },
        {
            title: '操作',
            dataIndex: 'operate',
            render: (_: any, row: any) => (
                <div className="flex flex-row gap-1 items-center">
                    <Button
                        theme="borderless"
                        size="small"
                        type="tertiary"
                        icon={<IconEdit />}
                        onClick={() => console.log('Edit:', row)}
                    />
                    <Button
                        theme="borderless"
                        size="small"
                        type="tertiary"
                        icon={<IconDeleteStroked />}
                        onClick={() => {
                            const updated = variables.filter(v => v.key !== row.key);
                            if (formApi) {
                                formApi.setValue("variables", updated);
                            }
                        }}
                    />
                </div>
            ),
        },
    ];

    return (
        <div>
            <div className="w-full hover:bg-semi-color-fill-0 cursor-pointer flex items-center p-1 rounded-md justify-between my-1" onClick={toggleVar}>
                <div className="text-semi-color-text-1">变量</div>
                <div className="flex items-center gap-2">
                    <Button
                        size="small"
                        theme="borderless"
                        type="tertiary"
                        icon={<IconPlus />}
                        onClick={(e) => {
                            e.stopPropagation();
                            openVarModel();
                        }}
                    />
                    <Divider layout="vertical" />
                    <Button
                        size="small"
                        theme="borderless"
                        type="tertiary"
                        icon={varOpen ? <IconChevronUp /> : <IconChevronDown />}
                        onClick={toggleVar}
                    />
                </div>
            </div>

            <Collapsible isOpen={varOpen}>
                <Table
                    size="small"
                    dataSource={variables}
                    columns={columns}
                    rowKey="key"
                    pagination={false}
                />
            </Collapsible>
        </div>
    );
};

export default VariablesSection;