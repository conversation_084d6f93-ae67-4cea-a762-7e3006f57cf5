import {
  <PERSON>,
  Row,
  Col,
  Skeleton,
  Typography,
  Space,
  Popover,
  Tag,
  Card,
  Button,
  Input,
} from "@douyinfe/semi-ui";
import { useRef, useState } from "react";
import Knowledge from "@/components/icon/Knowledge";
import { IconEdit } from "@douyinfe/semi-icons";
import KnowledgeNameModel from "./KnowledgeNameModel";

interface Props {
  knowledgeInfo: any;
  aiModels: any[];
  formRef: React.MutableRefObject<any>;
  onTagsUpdate: (tags: string[]) => void;
}

export default function KnowledgeDetailSidebar({
  knowledgeInfo,
  aiModels,
  formRef,
  onTagsUpdate,
}: Props) {
  const [nameOpen, setNameOpen] = useState(false);
  const renderTags = () => {
    if (!knowledgeInfo?.tags) {
      return <Skeleton.Title style={{ width: "100%", height: "50px" }} />;
    }
    return (
      <Space wrap>
        {knowledgeInfo?.tags?.split(",").map((item: any, index: any) => (
          <Tag key={index} shape="circle" color="blue">
            {item}
          </Tag>
        ))}
      </Space>
    );
  };

  return (
    <Card
      bordered={false}
      headerLine={false}
      className="h-full"
      bodyStyle={{ height: "100%" }}
      title={
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <Typography.Text className="text-md font-bold">
              {knowledgeInfo?.name || <Skeleton.Title style={{ width: 120 }} />}
            </Typography.Text>
            <IconEdit className="cursor-pointer hover:text-semi-color-primary" onClick={() => {
              setNameOpen(true)

            }} />
          </div>
          <div className="text-semi-color-text-2 text-xs w-full">
            {knowledgeInfo?.description || (
              <Skeleton.Title style={{ width: "100%" }} />
            )}
          </div>
        </div>
      }
    >
      <Form getFormApi={(form) => (formRef.current = form)}>
        <Row>
          <Col span={24}>
            <Form.Select
              className="w-full"
              field="embedModel"
              label="索引模型"
              showClear
              placeholder="请选择索引模型"
              optionList={aiModels}
              trigger="blur"
            />
          </Col>
          <Col span={24}>
            <div>
              <Form.Label>文档标签</Form.Label>
            </div>
            <Space wrap>{renderTags()}</Space>
            <Popover
              content={({ initialFocusRef }) => (
                <div style={{ padding: 12, width: 300 }}>
                  <div className="text-md font-bold mb-2">添加标签</div>
                  <Space className="mb-[30px] w-full">
                    <Input
                      showClear
                      name="tag"
                      className="flex-1"
                      ref={initialFocusRef}
                      placeholder="请输入标签"
                    />
                    <Button
                      theme="solid"
                      type="primary"
                      onClick={() => {
                        // 处理标签添加逻辑
                        const newTags = [
                          ...knowledgeInfo.tags.split(","),
                          "新标签",
                        ];
                        onTagsUpdate(newTags);
                      }}
                    >
                      添加
                    </Button>
                  </Space>
                </div>
              )}
              trigger="click"
            >
              <Button className="mt-2" type="secondary" block>
                添加标签
              </Button>
            </Popover>
          </Col>
        </Row>
      </Form>
      <KnowledgeNameModel visible={nameOpen} handleOk={() => {

      }} handleCancel={() => {
        setNameOpen(false)
      }} />
    </Card>
  );
}
