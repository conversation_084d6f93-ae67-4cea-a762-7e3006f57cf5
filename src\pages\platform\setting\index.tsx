import ModelIcon from "@/components/icon/ModelIcon";
import { Layout, Nav } from "@douyinfe/semi-ui";
import { useEffect, useState } from "react";
import ModelIndex from "./model/index";
import TeamsIcon from "@/components/icon/TeamsIcon";
import TenantIcon from "@/components/icon/TenantIcon";

function Setting() {
  // 1. 修改状态类型为字符串（假设导航是单选模式）
  const [activeKey, setActiveKey] = useState<string>("model");

  // 2. 从URL初始化状态
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const storedKey = params.get("activeKey");
    if (storedKey) {
      setActiveKey(storedKey);
    }
  }, []);

  // 3. 点击处理函数
  const handleNavSelect = (data: any) => {
    const selectedKey = data.selectedKeys[0];
    setActiveKey(selectedKey);

    // 4. 更新URL参数（关键步骤）
    const params = new URLSearchParams(window.location.search);
    params.set("activeKey", selectedKey);
    window.history.pushState(
      null,
      "",
      `${window.location.pathname}?${params.toString()}`
    );
  };

  const menus = [
    {
      label: "模型管理",
      icon: <ModelIcon />,
      key: "model",
    },
    {
      label: "团队管理",
      icon: <TeamsIcon />,
      key: "teams",
    },
    {
      label: "企业设置",
      icon: <TenantIcon />,
      key: "tenant",
    },
  ];

  return (
    <Layout className="w-full  h-full px-2 py-2 rounded-md">
      <Layout.Sider style={{ height: "100%" }}>
        <Nav
          selectedKeys={[activeKey]} // 5. 使用数组形式传递选中键
          onSelect={handleNavSelect}
          style={{ maxWidth: "200px", height: "100%" }}
          className="h-full"
        >
          {menus.map((menu) => {
            return (
              <Nav.Item
                key={menu.key}
                itemKey={menu.key}
                text={menu.label}
                icon={menu.icon}
              />
            );
          })}
        </Nav>
      </Layout.Sider>
      <div className="w-full h-full overflow-auto bg-semi-color-white">
        {activeKey === "model" && <ModelIndex />}
      </div>
    </Layout>
  );
}

export default Setting;
