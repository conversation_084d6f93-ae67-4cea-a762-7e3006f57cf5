import EmtpyBox from "@/components/icon/EmtpyBox";
import Icon, {
  IconDeleteStroked,
  IconMore,
  IconSearch,
} from "@douyinfe/semi-icons";
import {
  IllustrationNoContent,
  IllustrationNoContentDark,
} from "@douyinfe/semi-illustrations";
import {
  Avatar,
  Button,
  Card,
  Checkbox,
  Highlight,
  Col,
  Divider,
  Dropdown,
  Empty,
  Input,
  List,
  Pagination,
  Row,
  Select,
  Tag,
  Toast,
  Typography,
  Form,
  Modal,
} from "@douyinfe/semi-ui";
import classNames from "classnames";
import React, { useEffect, useState } from "react";
import "./styles.scss";
import PromptIcon from "@/components/icon/PromptIcon";
import { useTable } from "@/hooks/useTables";
import {
  deletePromptTemplate,
  getPromptTemplate,
} from "@/api/platform/promptTemplate";
import PromptModal from "./prompt-form";
import { useNavigate } from "@tanstack/react-router";
import { SemiFormFieldCustomSelector } from "@/components/CustomSelector";
import { getCategorySelected } from "@/api/platform/category";
import { debounce } from "lodash-es";
export default function Prompt() {
  const navigate = useNavigate();
  const [categorys, setCategorys] = useState<any>([]);
  const getCategorys = async () => {
    getCategorySelected().then((res: any) => {
      // console.log(res)
      setCategorys([{ value: "all", label: "全部" }, ...(res.data || [])]);
    });
  };

  useEffect(() => {
    getCategorys();
  }, []);

  const {
    dataSource,
    loading,
    columns,
    visibleColumns,
    updateVisibleColumns,
    pagination,
    rowSelection,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getPromptTemplate,
    params: {
      // deptId: deptId,
    },
    columns: [],
    onRowSelect: (selectedRowKeys: any, selectedRows: any) => {},
  });

  const handleAddPrompt = () => {
    console.log("新增提示词");
    navigate({
      to: "/platform/resource/prompt/form",
    });
  };
  const debouncedSetSearchParams = React.useRef(
    debounce((v) => {
      const search = { ...v };
      if (v.category === "all") {
        search.category = "";
      }
      setSearchParams(search);
      refresh();
    }, 300)
  ).current;

  const handleEditor = (item: any) => {
    navigate({
      to: "/platform/resource/prompt/form",
      search: {
        id: item.id,
      },
    });
  };

  const handleDelete = (item: any) => {
    const modal = Modal.warning({
      title: "确认删除提示词模板？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        return await deletePromptTemplate(item.id)
          .then(({ msg }) => {
            Toast.success(msg);
            //刷新列表
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  return (
    <div className="h-full w-full overflow-auto relative flex flex-col py-2 px-2">
     <div className="text-nowrap  pl-4 gap-2 pt-2 text-lg mb-2 flex-col flex  justify-between">
        {/* <div className="text-nowrap font-extrabold pl-4 gap-2 pt-2 text-lg mb-2 flex-col flex  justify-between">
          <div>提示词</div>
          <Button type="primary" theme="solid" onClick={handleAddPrompt}>
            新增提示词模板
          </Button>
        </div> */}
        <div className="flex flex-row justify-between items-center">
          <div className="font-extrabold">提示词</div>
          <div className="flex gap-2 items-center">
            <Button theme="solid" type="primary" onClick={handleAddPrompt}>
              创建提示词
            </Button>
          </div>
        </div>
        <Form
          onValueChange={(v) => {
            debouncedSetSearchParams(v);
          }}
        >
          <Row align="middle" gutter={[0, 5]}>
            <Col span={24} className="w-full">
              <div className="flex items-center  mt-1">
                <div className="text-sm  w-[80px] ">使用场景</div>
                <div className="flex-1">
                  <Form.Input
                    fieldStyle={{ padding: "0px" }}
                    field="name"
                    showClear
                    noLabel
                    placeholder="搜索prompt模板"
                    prefix={<IconSearch />}
                    style={{ width: "300px" }}
                  />
                </div>
              </div>
            </Col>
            <Col span={24} />
            <Col span={24}>
              <div className="flex items-center mt-1">
                <div className="text-sm w-[80px] ">使用场景</div>
                <SemiFormFieldCustomSelector
                  className="flex-1"
                  fieldClassName="flex-1"
                  fieldStyle={{ padding: "0px" }}
                  field="category"
                  // style={{ paddingBottom: "0px" }}
                  noLabel
                  initValue={"all"}
                  options={categorys}
                  // value={useScene}
                />
              </div>
            </Col>
          </Row>
        </Form>
      </div>

      {/* 新增按钮 */}

      <div className="mt-2 flex-1 overflow-auto overflow-x-hidden">
        <List
          dataSource={dataSource}
          layout="horizontal"
          loading={loading}
          grid={{
            gutter: [12, 12],
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 8,
            xxl: 6,
          }}
          emptyContent={
            <Empty
              image={<EmtpyBox />}
              darkModeImage={<IllustrationNoContentDark />}
              description="暂无数据"
            />
          }
          renderItem={(item) => {
            return (
              <List.Item className="w-full" onClick={() => {}}>
                <Card
                  className="w-full"
                  bodyStyle={{ width: "100%", padding: "20px 20px 20px 20px" }}
                  shadows="hover"
                >
                  <div className="flex flex-col gap-2">
                    <div className="py-1 flex flex-row gap-2">
                      <div className="w-[45px] h-[45px] flex items-center justify-center text-xl rounded-md bg-[#e3f2fe] ">
                        {item.icon}
                      </div>
                      {/* <Icon svg={<PromptIcon />} size="large" /> */}
                      {/* <Avatar size="medium" src={item.logo} shape="square" /> */}
                      <div className="flex flex-col gap-1">
                        <div className="font-semibold text-md text-semi-color-text-0">
                          {item.name}
                        </div>
                        <div className="text-semi-color-text-2">
                          {item.categoryDetails?.name && (
                            <Tag>{item.categoryDetails?.name}</Tag>
                          )}
                        </div>
                      </div>
                    </div>
                    {/* <div className="mb-2 flex flex-row gap-1">
                      {item.tags.split(",").map((tag: any) => (
                        <Tag>{tag}</Tag>
                      ))}
                    </div> */}
                    <div className="mb-2 flex flex-row gap-1 h-[40px]">
                      <Typography.Text
                        type="tertiary"
                        ellipsis={{
                          rows: 2,
                          showTooltip: {
                            opts: { content: item.description },
                          },
                        }}
                      >
                        {item.description}
                      </Typography.Text>
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2 text-sm text-semi-color-text-2">
                        <Avatar
                          className="text-semi-color-white"
                          style={{ backgroundColor: "#339af0" }}
                          size="20px"
                          src={item.onwer.avatar}
                        >
                          {/* {item?.ownerInfo?.nickname
                            ? item?.ownerInfo?.nickname.substring(0, 1)
                            : "-"} */}
                        </Avatar>
                        <div>
                          {item.onwer.name}
                          {/* {item?.ownerInfo?.nickname
                            ? item?.ownerInfo?.nickname
                            : "-"} */}
                        </div>
                        {/* <div className="flex gap-1 items-center">
                          <span>创建时间：-</span>
                        </div> */}
                      </div>
                      <div>
                        <Dropdown
                          className="min-w-[150px]"
                          zIndex={400}
                          trigger={"click"}
                          position={"bottomLeft"}
                          render={
                            <Dropdown.Menu>
                              <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditor(item);
                                }}
                              >
                                编辑模板
                              </Dropdown.Item>
                              <Divider />
                              {/* 横线 */}
                              {/* <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                复制
                              </Dropdown.Item> */}
                              {/* <Dropdown.Item
                                onClick={(e) => {
                                  e.stopPropagation();
                                  Toast.info({ content: "You clicked me!" });
                                }}
                              >
                                导出模板
                              </Dropdown.Item> */}
                              <Dropdown.Item
                                type="danger"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDelete(item);
                                }}
                              >
                                删除
                              </Dropdown.Item>
                            </Dropdown.Menu>
                          }
                        >
                          <Button
                            theme="borderless"
                            onClick={(e) => e.stopPropagation()}
                            type="tertiary"
                            icon={<IconMore />}
                          />
                        </Dropdown>
                      </div>
                    </div>
                  </div>
                </Card>
              </List.Item>
            );
          }}
        />
      </div>
      {pagination.total > 0 && (
        <div className="flex justify-between mb-2 items-center px-2 py-2  rounded-lg">
          <div className="text-sm text-semi-color-text-2 ">
            显示第 {pagination.currentPage} 页- 每页 {pagination.pageSize}{" "}
            条，共 {pagination.total} 条
          </div>
          <Pagination {...pagination} />
        </div>
      )}
      {/* <PromptModal /> */}
    </div>
  );
}
