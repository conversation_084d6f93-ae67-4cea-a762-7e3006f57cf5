import React, { useState } from 'react';
import { SideSheet, Button, Tabs, Toast } from '@douyinfe/semi-ui';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { html } from '@codemirror/lang-html';
import { css } from '@codemirror/lang-css';
import { python } from '@codemirror/lang-python';
import { markdown } from '@codemirror/lang-markdown';
import { java } from '@codemirror/lang-java';
import { cpp } from '@codemirror/lang-cpp';
import { rust } from '@codemirror/lang-rust';
import { sql } from '@codemirror/lang-sql';
import { json } from '@codemirror/lang-json';
import { useCodeEditor } from '../context/CodeEditorContext';
import CodeBlock from './Markdown/CodeBlock';
import { MarkdownBox } from './Markdown/MarkdownBox';

// 语言映射
const languageExtensions = {
  javascript: javascript(),
  js: javascript(),
  jsx: javascript({ jsx: true }),
  typescript: javascript({ typescript: true }),
  ts: javascript({ typescript: true }),
  tsx: javascript({ jsx: true, typescript: true }),
  html: html(),
  css: css(),
  python: python(),
  py: python(),
  markdown: markdown(),
  md: markdown(),
  java: java(),
  cpp: cpp(),
  c: cpp(),
  rust: rust(),
  sql: sql(),
  json: json(),
};

export const CodeEditor: React.FC = () => {
  const { isEditing, currentCode, language, elementId, closeEditor, updateCode, saveChanges } = useCodeEditor();
  const [activeTab, setActiveTab] = useState('edit');
  const [codeTheme, setCodeTheme] = useState<'light' | 'dark'>('light');

  // 获取当前语言的 CodeMirror 扩展
  const getLanguageExtension = () => {
    const lang = language?.replace('language-', '') || '';
    return languageExtensions[lang] || javascript();
  };

  const handleSave = () => {
    Toast.success('代码已更新');
    saveChanges();
  };

  return (
    <SideSheet
      title="代码编辑器"
      width="80%"
      visible={isEditing}
      onCancel={closeEditor}
      placement="right"
      mask={true}
      footer={
        <div className="flex justify-end gap-2">
          <Button theme="solid" type="primary" onClick={handleSave}>
            应用更改
          </Button>
          <Button theme="borderless" onClick={closeEditor}>
            取消
          </Button>
        </div>
      }
    >
      <div className="h-full flex flex-col">
        <Tabs
          type="line"
          activeKey={activeTab}
          onChange={setActiveTab}
          className="flex-grow"
        >
          <Tabs.TabPane tab="编辑" itemKey="edit">
            <div className="h-[calc(100vh-180px)]">
              <CodeMirror
                value={currentCode}
                height="100%"
                theme={codeTheme}
                extensions={[getLanguageExtension()]}
                onChange={(value) => updateCode(value)}
                basicSetup={{
                  lineNumbers: true,
                  highlightActiveLineGutter: true,
                  highlightSpecialChars: true,
                  foldGutter: true,
                  dropCursor: true,
                  allowMultipleSelections: true,
                  indentOnInput: true,
                  syntaxHighlighting: true,
                  bracketMatching: true,
                  closeBrackets: true,
                  autocompletion: true,
                  rectangularSelection: true,
                  crosshairCursor: true,
                  highlightActiveLine: true,
                  highlightSelectionMatches: true,
                  closeBracketsKeymap: true,
                  searchKeymap: true,
                  foldKeymap: true,
                  completionKeymap: true,
                  lintKeymap: true,
                }}
              />
            </div>
          </Tabs.TabPane>
          <Tabs.TabPane tab="预览" itemKey="preview">
            <div className="h-[calc(100vh-180px)] overflow-auto p-4 border rounded-md">
              {language === 'markdown' || language === 'md' ? (
                <MarkdownBox content={currentCode} />
              ) : (
                <CodeBlock language={language} elementId={`preview-${elementId}`}>
                  {currentCode}
                </CodeBlock>
              )}
            </div>
          </Tabs.TabPane>
        </Tabs>
      </div>
    </SideSheet>
  );
};