export const commonSpec = {
    type: "bar",
    seriesField: "type",
    // title: {
    //   visible: true,
    //   // text: 'Grouped bar chart',
    //   // subtext: 'This is a grouped bar chart',
    // },
    bar: {
      style: {
        cornerRadius: 4,
      },
    },
    crosshair: {
      xField: { visible: true },
      yField: { visible: false },
    },
  
    // label: {
    //   visible: true,
    //   position: 'top'
    // },
    padding: {
      top: 0,
      bottom: 10,
      right: 10,
      left: 10,
    },
    autoFit: true,
    height: 250,
    legends: {
      visible: true,
      orient: "top",
      position: "end",
    },
    color: ["#1e90ff",'#ff4757'],
    axes: [
      {
        orient: "bottom",
      },
      { orient: "left", domainLine: { visible: true } },
    ],
  };
export const pieSpec = {
    type: "pie",
    height: 250,
   
    valueField: "value",
    categoryField: "type",
    label: {
      visible: true,
    },
    padding: {
      top: 0,
      bottom: 10,
      right: 10,
      left: 10,
    },
    tooltip: {
      mark: {
        content: [
          {
            key: (datum:any) => datum["type"],
            value: (datum:any) => datum["value"] ,
          },
        ],
      },
    },
    color: ["#1e90ff",'#ff4757'],
    // color: ["#5769ff",'#f5222d'],
    legends: {
      visible: true,
      orient: "right",
    },
    seriesStyle: [
      {
        type: "pie",
        dataIndex: 0,
        style: {
          fill: (datum: any) => {
            if (datum["type"] === "oxygen") {
              return "#FFA500"; // 修改为你想要的颜色
            }
            return undefined; // 使用默认颜色或全局 color 配置
          },
        },
      },
    ],
  };