import React from 'react'

export default function WxIcon() {
  return (
    <svg t="1750779623340" className="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2082" width="1em" height="1em"><path d="M693.208615 368.009846c11.756308 0 23.394462 0.846769 34.89477 2.146462-31.369846-146.038154-187.490462-254.523077-365.686154-254.523077C163.209846 115.613538 0 251.431385 0 423.857231c0 99.544615 54.291692 181.287385 145.033846 244.676923l-36.253538 109.016615 126.700307-63.547077c45.331692 8.979692 81.703385 18.215385 126.956308 18.215385 11.362462 0 22.646154-0.571077 33.831385-1.457231a269.548308 269.548308 0 0 1-11.185231-75.913846c0-158.345846 135.975385-286.838154 308.125538-286.838154z m-194.875077-98.284308c27.293538 0 45.351385 17.959385 45.351385 45.213539 0 27.155692-18.057846 45.371077-45.351385 45.371077-27.155692 0-54.409846-18.195692-54.409846-45.371077 0-27.254154 27.254154-45.213538 54.409846-45.213539z m-253.636923 90.604308c-27.175385 0-54.567385-18.195692-54.567384-45.371077 0-27.273846 27.411692-45.233231 54.567384-45.233231s45.233231 17.959385 45.233231 45.233231c0 27.175385-18.077538 45.371077-45.233231 45.371077z" fill="#05CB02" p-id="2083"></path><path d="M1024 650.496c0-144.915692-144.994462-263.030154-307.849846-263.030154-172.465231 0-308.263385 118.114462-308.263385 263.030154 0 145.152 135.798154 262.990769 308.263385 262.990769 36.096 0 72.507077-9.097846 108.740923-18.195692l99.406769 54.449231-27.273846-90.564923C969.806769 804.608 1024 732.238769 1024 650.496z m-407.808-45.371077c-18.038154 0-36.233846-17.939692-36.233846-36.273231 0-18.057846 18.195692-36.253538 36.233846-36.253538 27.431385 0 45.390769 18.195692 45.390769 36.253538 0 18.333538-17.959385 36.273231-45.390769 36.273231z m199.364923 0c-17.92 0-35.997538-17.939692-35.997538-36.273231 0-18.057846 18.077538-36.253538 35.997538-36.253538 27.155692 0 45.371077 18.195692 45.371077 36.253538 0 18.333538-18.215385 36.273231-45.371077 36.273231z" fill="#05CB02" p-id="2084"></path></svg>
  )
}
