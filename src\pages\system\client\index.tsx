import DictTag from "@/components/DictTag";
import EmptyDataIcon from "@/components/icon/EmptyDataIcon";
import useDictionary from "@/hooks/useDictionary";
import Icon, {
  IconDeleteStroked,
  IconEditStroked,
  IconMore,
  IconPlusStroked,
  IconSync,
} from "@douyinfe/semi-icons";
import {
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  List,
  Modal,
  Pagination,
  Typography,
  Toast,
  Tooltip,
  Card,
  OverflowList,
  Tag,
  DropdownDivider,
} from "@douyinfe/semi-ui";
import { useEffect, useState, useCallback } from "react";
import { useBoolean } from "@/hooks";
import { debounce } from "lodash-es";
import { useTable } from "@/hooks/useTables";
import { delClient, getClientList } from "@/api/system/client";
import { titles } from "./contants";
import ClientForm from "./components/client-form";
import ClientIcon from "@/components/icon/ClientIcon";
import { formatDuration } from "@/utils";
import { Apple, Box, HelpCircle, Monitor, Smartphone } from "lucide-react";

export default function Clients() {
  const { data: dictionaryData, loadDictionary } = useDictionary([
    "sys_normal_disable",
    "sys_device_type",
    "sys_common_status",
    "sys_grant_type",
  ]);
  const [statusOptions, setStatusOptions] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any>([]);
  const [formOpen, { setTrue, setFalse }] = useBoolean();
  const [formId, setFormId] = useState<any>(undefined);
  const [formTitle, setFormTitle] = useState(titles[0]);

  useEffect(() => {
    setTimeout(() => {
      loadDictionary();
      setStatusOptions(
        dictionaryData["sys_common_status"]?.map((item: any) => ({
          label: item.dictLabel,
          value: item.dictValue,
        }))
      );
    }, 0);
  }, []);

  const handleEditForm = (row: any) => {
    setFormTitle(titles[1]);
    setFormId(row.id);
    setTrue();
  };

  const {
    dataSource,
    loading,
    pagination,
    refresh,
    searchParams,
    setSearchParams,
    resetSearchParams,
  } = useTable({
    api: getClientList,
    params: {},
  });

  // 防抖提交（300ms）
  const debouncedSubmit = useCallback(
    debounce((values: any) => {
      setSearchParams(values);
      refresh();
    }, 300),
    []
  );

  const handleAddForm = () => {
    setFormTitle(titles[0]);
    setFormId(undefined);
    setTrue();
  };

  const handleRefresh = () => {
    refresh();
  };

  const handleCancel = () => {
    setFormId(undefined);
    setFalse();
  };

  const handleRemoveClient = (id: number) => {
    const modal = Modal.warning({
      title: "确认删除客户端数据？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        return await delClient(id)
          .then(({ msg }) => {
            Toast.success(msg);
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  const handleBatchDelete = () => {
    const modal = Modal.warning({
      title: "批量删除提醒",
      content: "确认删除客户端数据？删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        return await delClient(selectedRowKeys)
          .then(({ msg }) => {
            Toast.success(msg);
            refresh();
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
      },
    });
  };

  const getDeviceIcon = (deviceType: string) => {
    let icon = null;
    switch (deviceType) {
      case 'pc':
        icon = <Icon className="text-semi-color-primary" svg={<Monitor size={25} />} />;
        break;
      case 'android':
        icon = <Icon className="text-semi-color-primary" svg={<Smartphone size={25} className="inline " />} />;
        break;
      case 'ios':
        icon = <Icon className="text-semi-color-primary" svg={<Apple size={25} className="inline " />} />;
        break;
      case 'xcx':
        icon = <Icon className="text-semi-color-primary" svg={<Box size={25} className="inline " />} />;
        break;
      default:
        icon = <Icon className="text-semi-color-primary" svg={<HelpCircle size={25} className="inline" />} />;
    }

    return <div className="w-10 rounded-lg flex items-center justify-center h-10 bg-semi-color-primary-light-hover">
      {icon}
    </div>
  };

  return (
    <div className="w-full h-full py-2 px-2">
      <div className="flex flex-col w-full h-full rounded-md">
        <div className="flex flex-col sm:flex-row justify-between gap-2 px-2 mb-2 items-center">
          <div className="font-semibold text-xl">客户端管理</div>
        </div>
        <div className="flex flex-row flex-wrap justify-between mb-2 items-center gap-2 px-2">
          <Form<typeof searchParams>
            initValues={searchParams}
            stopValidateWithError={true}
            showValidateIcon={true}
            onValueChange={(values) => {
              debouncedSubmit(values);
            }}
            onReset={() => {
              resetSearchParams();
              refresh();
            }}
            onSubmit={(values) => {
              setSearchParams(values);
              refresh();
            }}
          >
            <div className="flex gap-2">
              <Form.Input
                className="min-w-[250px]"
                showClear
                fieldStyle={{ padding: "0" }}
                noErrorMessage
                trigger={["change", "blur"]}
                noLabel
                field="clientKey"
                placeholder={"请输入客户端KEY"}
              />
              <Form.Select
                className="min-w-[150px]"
                showClear
                noErrorMessage
                fieldStyle={{ padding: "0" }}
                optionList={statusOptions}
                trigger={["change", "blur"]}
                noLabel
                field="status"
                placeholder={"请选择状态"}
              />
            </div>
          </Form>
          <div className="flex flex-row items-center gap-2">
            <Button
              type="primary"
              theme="solid"
              icon={<IconPlusStroked />}
              onClick={handleAddForm}
            >
              创建客户端
            </Button>
            <Tooltip content="刷新列表">
              <Button
                type="tertiary"
                icon={<IconSync />}
                onClick={handleRefresh}
              />
            </Tooltip>
          </div>
        </div>

        {/* 卡片列表 */}
        <div className="flex-1 overflow-y-auto px-2">
          <List
            grid={{
              gutter: [12, 12],
              xs: 24,
              sm: 24,
              md: 12,
              lg: 12,
              xl: 8,
              xxl: 6,
            }}
            dataSource={dataSource}
            loading={loading}
            emptyContent={
              <Empty
                image={<EmptyDataIcon style={{ width: 100 }} />}
                description="暂无数据"
              />
            }
            renderItem={(item) => (
              <List.Item>
                <Card
                  // title={
                  //   <div className="flex flex-wrap w-full flex-row justify-between items-center">
                  // <Typography.Text strong>
                  //   <span>
                  //     {
                  //       dictionaryData.sys_device_type.find((itemDict: any) => {
                  //         const match = String(itemDict.dictValue) === String(item.deviceType);
                  //         return match;
                  //       })?.dictLabel ?? '未知设备类型'
                  //     }
                  //     {/* 加入图标使用lucide的图标根据设备类型来显示不同的 */}
                  //     客户端
                  //   </span>
                  // </Typography.Text>
                  //     <div className="font-normal">
                  // <DictTag
                  //   dictType="sys_common_status"
                  //   dictValue={item.status}
                  //   dictionaryData={
                  //     dictionaryData.sys_common_status || []
                  //   }
                  // />
                  //     </div>
                  //   </div>
                  // }
                  headerLine={false}
                  className="w-full"
                  // footerStyle={{
                  //   padding: "15px 0px 10px 15px",
                  // }}
                  bodyStyle={{ padding: "10px" }}
                  // headerStyle={{
                  //   padding: "15px",
                  //   paddingInline: "15px"
                  // }}
                  shadows="hover"
                  footer={
                    <div className="flex justify-between items-center">
                      <OverflowList
                        items={item.grantTypeList}
                        minVisibleItems={1}
                        overflowRenderer={() => {
                          return item.grantTypeList.length ? (
                            <Tag
                              style={{
                                flex: "0 0 auto",
                                fontVariantNumeric: "tabular-nums",
                              }}
                            >
                              +{item.grantTypeList.length}
                            </Tag>
                          ) : null;
                        }}
                        visibleItemRenderer={(text: any, index) => {
                          console.log(dictionaryData.sys_grant_type);
                          return (
                            <div key={index} className="mr-1">
                              <DictTag
                                // dictType="tag"
                                dictType="sys_grant_type"
                                dictValue={text as string}
                                dictionaryData={
                                  dictionaryData.sys_grant_type || []
                                }
                              />
                            </div>
                          );
                        }}
                      />
                      <Dropdown
                        position="bottom"
                        trigger="click"
                        zIndex={200}
                        render={
                          <Dropdown.Menu>
                            <Dropdown.Item
                              icon={<IconEditStroked />}
                              onClick={() => handleEditForm(item)}
                            >
                              修改客户端
                            </Dropdown.Item>
                            <DropdownDivider />
                            <Dropdown.Item
                              type="danger"
                              icon={<IconDeleteStroked />}
                              onClick={() => handleRemoveClient(item.id)}
                            >
                              删除客户端
                            </Dropdown.Item>
                          </Dropdown.Menu>
                        }
                      >
                        <Button
                          theme="borderless"
                          type="tertiary"
                          icon={<IconMore />}
                        />
                      </Dropdown>
                    </div>
                  }
                >
                  <Card.Meta
                    title={
                      <div className="flex gap-2 items-center">
                        <Typography.Text strong>
                          <span>
                            {
                              dictionaryData.sys_device_type.find((itemDict: any) => {
                                const match = String(itemDict.dictValue) === String(item.deviceType);
                                return match;
                              })?.dictLabel ?? '未知设备类型'
                            }
                            {/* 加入图标使用lucide的图标根据设备类型来显示不同的 */}
                            客户端
                          </span>

                        </Typography.Text>
                        <DictTag
                          dictType="sys_common_status"
                          dictValue={item.status}
                          dictionaryData={
                            dictionaryData.sys_common_status || []
                          }
                        />
                      </div>
                    }
                    avatar={getDeviceIcon(item.deviceType)

                    }
                  />
                  <div className="h-[80px] py-1 flex flex-col gap-1 overflow-y-auto">
                    <Typography.Text type="secondary">
                      KEY：{item.clientKey}
                    </Typography.Text>
                    <Typography.Text type="secondary">
                      活跃超时时间：{formatDuration(Number(item.activeTimeout))}
                    </Typography.Text>
                    <Typography.Text type="secondary">
                      固定超时时间：{formatDuration(item.timeout)}
                    </Typography.Text>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </div>

        {/* 分页器 */}
        {pagination.total > 0 && (
          <div className="flex justify-end mt-2 px-2">
            <Pagination {...pagination} />
          </div>
        )}

        {/* 弹窗表单 */}
        <ClientForm
          open={formOpen}
          title={formTitle}
          id={formId}
          deviceTypes={dictionaryData?.sys_device_type}
          grantTypes={dictionaryData?.sys_grant_type}
          statuss={dictionaryData?.sys_normal_disable}
          onCancel={handleCancel}
          onRefresh={handleRefresh}
        />
      </div>
    </div>
  );
}
