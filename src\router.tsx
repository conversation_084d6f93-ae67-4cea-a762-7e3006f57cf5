import { routes } from "./routes";
import { loginRoute } from "./routes/auth";
import {
  profileRoute,
  rootRoute,
} from "./routes/base";
import { platformFlowRoute } from "./routes/flow";
import { homeRoute, readict } from "./routes/home";
import platformRoutes from "./routes/platform";
import systemRoutes from "./routes/system";

// const routeTree = rootRoute.addChildren([
//   loginRoute,
//   platformFlowRoute,
//   homeRoute,
//   readict,
//   profileRoute,
//   platformAppDetailsRoute,
//   platformRoutes,
//   ...systemRoutes,
//   indexRoute,
// ]);

const routeTree = rootRoute.addChildren([
  // loginRoute,
  // // ...systemRoutes,
  // // homeRoute,
  // profileRoute,
  // // platformAppDetailsRoute,
  // ...platformRoutes,
  // // indexRoute,
  ...routes
]);
export default routeTree;
