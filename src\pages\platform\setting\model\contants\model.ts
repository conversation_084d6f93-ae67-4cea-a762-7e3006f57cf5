// src/pages/platform/model/components/modelFormRules.ts
export const formRules = {
  aliasName: [{ required: true, message: "请输入模型别名" }],
  provider: [{ required: true, message: "请选择模型供应商" }],
  modelVersion: [{ required: true, message: "请选择模型版本" }],
  type: [{ required: true, message: "请选择模型类型" }],
  endpoint: [
    { required: true, message: "请输入接口地址" },
    {
      pattern: /^https?:\/\/\S+/i,
      message: "请输入有效的 URL 地址（例如：http://example.com）",
    },
  ],
  apiKey: [{ required: true, message: "请输入API KEY" }],
};


export const initPageInfo = {
    page: 1,
    pageSize: 10,
};