export const commonSpec = {
    type: "bar",
    data: [
    //   {
    //     id: "barData",
    //     values: [
    //       { type: "Autocracies", year: "1930", value: 129 },
    //       { type: "Autocracies", year: "1940", value: 133 },
    //       { type: "Autocracies", year: "1950", value: 130 },
    //       { type: "Autocracies", year: "1960", value: 126 },
    //       { type: "Autocracies", year: "1970", value: 117 },
    //       { type: "Autocracies", year: "1980", value: 114 },
    //       { type: "Autocracies", year: "1990", value: 111 },
    //       { type: "Autocracies", year: "2000", value: 89 },
    //       { type: "Autocracies", year: "2010", value: 80 },
    //       { type: "Autocracies", year: "2018", value: 80 },
    //       { type: "Democracies", year: "1930", value: 22 },
    //       { type: "Democracies", year: "1940", value: 13 },
    //       { type: "Democracies", year: "1950", value: 25 },
    //       { type: "Democracies", year: "1960", value: 29 },
    //       { type: "Democracies", year: "1970", value: 38 },
    //       { type: "Democracies", year: "1980", value: 41 },
    //       { type: "Democracies", year: "1990", value: 57 },
    //       { type: "Democracies", year: "2000", value: 87 },
    //       { type: "Democracies", year: "2010", value: 98 },
    //       { type: "Democracies", year: "2018", value: 99 },
    //     ],
    //   },
    ],
    seriesField: "type",
    // title: {
    //   visible: true,
    //   // text: 'Grouped bar chart',
    //   // subtext: 'This is a grouped bar chart',
    // },
    bar: {
      style: {
        cornerRadius: 4,
      },
    },
    crosshair: {
      xField: { visible: true },
      yField: { visible: false },
    },
  
    // label: {
    //   visible: true,
    //   position: 'top'
    // },
    padding: {
      top: 0,
      bottom: 10,
      right: 10,
      left: 10,
    },
    autoFit: true,
    height: 250,
    legends: {
      visible: true,
      orient: "top",
      position: "end",
    },
    color: ["#1e90ff",'#ff4757'],
    axes: [
      {
        orient: "bottom",
      },
      { orient: "left", domainLine: { visible: true } },
    ],
  };
export const pieSpec = {
    type: "pie",
    name: "series1",
    height: 250,
    valueField: "value",
    categoryField: "type",
    label: {
      visible: true,
    },
    padding: {
      top: 0,
      bottom: 10,
      right: 10,
      left: 10,
    },
    tooltip: {
      mark: {
        content: [
          {
            key: (datum:any) => datum["type"],
            value: (datum:any) => datum["value"] ,
          },
        ],
      },
    },
    color: ["#1e90ff",'#ff4757'],
    legends: {
      visible: true,
      orient: "right",
    },
    seriesStyle: [
      {
        type: "pie",
        dataIndex: 0,
        style: {
          fill: (datum: any) => {
            if (datum["type"] === "oxygen") {
              return "#FFA500"; // 修改为你想要的颜色
            }
            return undefined; // 使用默认颜色或全局 color 配置
          },
        },
      },
    ],
  };