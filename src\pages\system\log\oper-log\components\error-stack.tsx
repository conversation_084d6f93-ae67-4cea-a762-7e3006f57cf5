import { Button, CodeHighlight, Modal } from "@douyinfe/semi-ui";
import React from "react";

export default function ErrorStack({
  onCancel,
  open,
  errorStack,
}: any) {
  return (
    <>
      <Modal
        title="错误信息"
        width={"50%"}
        className="semi-light-scrollbar"
        onCancel={onCancel}
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          minHeight: "500px",
          overflow: "auto",
        }}
        visible={open}
        footer={
          <Button type="tertiary" onClick={onCancel}>
            关闭
          </Button>
        }
        centered
      >
        <CodeHighlight
          language={"javascript"}
          code={errorStack}
        />
      </Modal>
    </>
  );
}
