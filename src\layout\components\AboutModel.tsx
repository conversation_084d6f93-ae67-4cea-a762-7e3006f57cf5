import { Logo } from "@/components/icon/Logo";
import LogoCol from "@/components/icon/LogoCol";
import { Modal } from "@douyinfe/semi-ui";
import React from "react";

export default function AboutModel({ open, onCancel }: any) {
  return (
    <Modal
      //   title={"关于系统"}
      width={600}
      visible={open}
      onCancel={onCancel}
      centered
      footer={null}
      closeOnEsc={false}
    >
      <div className="flex items-center min-h-[200px] flex-col justify-center  gap-2 w-full">
        <div className="flex items-center gap-2 flex-col">
          <Logo className="w-[300px] h-[50px]" />
          <div className="font-bold">LynkzHub</div>
        </div>
        <div className="text-semi-color-text-2">Version 1.0.0</div>
        <div className="text-semi-color-text-2">
          © 2025 LangGenius, Inc., Contributors. Open Source License
        </div>
      </div>
    </Modal>
  );
}
