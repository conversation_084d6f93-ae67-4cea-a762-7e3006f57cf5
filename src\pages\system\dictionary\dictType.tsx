import { delDictType, getDictTypeList } from "@/api/system/dict";
import { useBoolean } from "@/hooks";
import { IconMore, IconPlus, IconRefresh } from "@douyinfe/semi-icons";
import {
  Button,
  Card,
  Col,
  Divider,
  Dropdown,
  Input,
  List,
  Modal,
  Row,
  Spin,
  Toast,
  Typography,
} from "@douyinfe/semi-ui";
import classNames from "classnames";
import React, { useEffect, useState } from "react";
import DictTypeForm from "./components/dictTypeForm";
import { dictTypeFormTitle } from "./constants/dictonary";

export default function DictType({ onClickDictType = (dict: any) => {} }) {
  const [selectedDict, setSelectedDict] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [formOpen, { setTrue, setFalse }] = useBoolean();

  const [dataSource, setData] = useState<any>([]);
  const [total, setTotal] = useState(0);
  const [formTitle, setFormTitle] = useState(dictTypeFormTitle[0]);
  const [formId, setFormId] = useState<any>(undefined);

  const handleRemoveDictType = (dictId: string) => {
    const modal = Modal.warning({
      title: "确认删除字典类型？",
      content: "删除后无法恢复，请谨慎操作！",
      confirmLoading: false,
      onOk: async () => {
        modal.update({
          confirmLoading: true,
        });
        // 删除字典数据
        return await delDictType(dictId)
          .then(({ msg }) => {
            Toast.success(msg);
            setSelectedDict(undefined)
            //刷新列表
            featchDictType();
            onClickDictType(null);
            return Promise.resolve();
          })
          .catch(() => {
            return Promise.reject();
          });
        // 刷新字典数据
        // 刷新缓存
      },
    });
  };

  const handleItemClick = (item: any) => {
    setSelectedDict((prev: any) =>
      prev?.dictId === item.dictId ? null : item
    );
    if (selectedDict?.dictId === item.dictId) {
      onClickDictType(null);
    } else {
      onClickDictType(item);
    }
  };
  const featchDictType = () => {
    setLoading(true);
    getDictTypeList({})
      .then((res) => {
        setLoading(false);
        setData(res.rows);
        setTotal(res.total);
      })
      .catch(() => {
        setLoading(false);
      });
  };
  useEffect(() => {
    featchDictType();
  }, []);

  const handleEditForm = (row: any) => {
    setFormTitle(dictTypeFormTitle[1]);
    setFormId(row.dictId);
    setTrue();
  };
  const handleAddForm = () => {
    setFormTitle(dictTypeFormTitle[0]);
    setFormId(undefined);
    setTrue();
  };

  return (
    <div className="h-full bg-semi-color-white w-full rounded-lg overflow-hidden flex flex-col">
      <div className="flex justify-between w-full gap-2 items-center flex-col px-2 py-2">
        <div className="flex justify-between w-full items-center">
          <div className="text-semi-color-text-0 text-md font-semibold">
            字典类型
          </div>
          <div className="flex gap-2 items-center">
            <Button onClick={() => handleAddForm()} icon={<IconPlus />}></Button>
            <Button
              onClick={() => featchDictType()}
              icon={<IconRefresh />}
            ></Button>
          </div>
        </div>
        <Divider />
      </div>
      <div className="mb-1 w-full px-2">
        <Input showClear placeholder={"请输入字典编码"} />
      </div>
      <div className="w-full h-full px-2 mb-2 overflow-y-auto flex-1">
        {loading ? (
          <Spin
            style={{ width: "100%", height: "100%" }}
            tip="正在加载..."
            spinning={loading}
          ></Spin>
        ) : (
          <List
            dataSource={dataSource}
            split={false}
            renderItem={(item, index) => (
              <List.Item
                onClick={() => handleItemClick(item)}
                className={classNames(
                  "w-full justify-between   group/item flex items-center flex-nowrap",
                  "hover:bg-semi-color-fill-0",
                  "rounded-md",
                  "cursor-pointer",
                  {
                    "bg-semi-color-primary-light-hover font-bold text-semi-color-primary":
                      selectedDict?.dictId === item.dictId,
                  }
                )}
                key={item.dictId}
              >
                <Typography.Text ellipsis={{ showTooltip: true }}>
                  {item.dictName}
                </Typography.Text>
                <Dropdown
                  className="min-w-[150px]"
                  zIndex={1000}
                  trigger={"click"}
                  position={"bottomLeft"}
                  render={
                    <Dropdown.Menu>
                      <Dropdown.Item
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditForm(item);
                        }}
                      >
                        修改
                      </Dropdown.Item>
                      <Dropdown.Item onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveDictType(item.dictId);
                      }}>
                        删除
                      </Dropdown.Item>
                    </Dropdown.Menu>
                  }
                >
                  {selectedDict?.dictId === item.dictId && (
                    <div
                      className="cursor-pointer "
                      onClick={(e) => e.stopPropagation()}
                    >
                      <IconMore />
                    </div>
                  )}
                </Dropdown>
              </List.Item>
            )}
          ></List>
        )}
      </div>

      <DictTypeForm
        open={formOpen}
        title={formTitle}
        id={formId}
        onCancel={setFalse}
        onRefresh={featchDictType}
      />
    </div>
  );
}
