import React from 'react';
import { CodeEditorProvider } from '../context/CodeEditorContext';
import { CodeEditor } from './CodeEditor';
import CodeBlock from './Markdown/CodeBlock';

interface CodeBlockWrapperProps {
  children: React.ReactNode;
  className?: string;
  language?: string;
  elementId: string;
  onCodeChange?: (newCode: string) => void;
}

// 这个组件将 CodeBlock 和编辑功能包装在一起
export const CodeBlockWrapper: React.FC<CodeBlockWrapperProps> = (props) => {
  const { onCodeChange } = props;

  return (
    <CodeEditorProvider>
      <CodeBlock {...props} />
      <CodeEditor />
    </CodeEditorProvider>
  );
};

export default CodeBlockWrapper;
